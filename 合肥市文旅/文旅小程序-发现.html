<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 发现</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        /* 确保页面可以正常滚动 */
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }
        
        /* 底部安全区域适配 */
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
        
        /* 隐藏滚动条但保持滚动功能 */
        body::-webkit-scrollbar {
            display: none;
        }
        
        body {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        /* 虚拟人动画 */
        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .image-placeholder {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-size: 2rem;
        }

        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .chat-bubble {
            animation: fadeInOut 4s ease-in-out infinite;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: scale(0.8); }
            25%, 75% { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white px-4 py-3 shadow-sm">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <button onclick="history.back()" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <div>
                    <h1 class="text-lg font-bold text-gray-900">发现合肥</h1>
                    <p class="text-xs text-gray-500">探索更多精彩</p>
                </div>
            </div>
            <button class="p-2 text-gray-400">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </button>
        </div>
        
        <!-- 搜索框 -->
        <div class="relative">
            <input type="text" placeholder="搜索合肥景点、美食、活动..." 
                   class="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white">
            <svg class="absolute left-3 top-3.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </div>
    </header>

    <!-- 分类标签 -->
    <section class="px-4 py-4 bg-white border-b border-gray-100">
        <div class="flex space-x-3 overflow-x-auto">
            <button class="px-4 py-2 bg-primary text-white text-sm rounded-full whitespace-nowrap">全部</button>
            <button class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">自然风光</button>
            <button class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">历史文化</button>
            <button class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">美食餐厅</button>
            <button class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">休闲娱乐</button>
        </div>
    </section>

    <!-- 主要内容区域 -->
    <main class="px-4 py-4 space-y-6 pb-24">
        <!-- 热门推荐 -->
        <section>
            <h2 class="text-lg font-semibold text-gray-900 mb-4">热门推荐</h2>
            <div class="space-y-4">
                <!-- 景点卡片 1 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop&crop=center"
                             alt="巢湖风景区" class="w-full h-48 object-cover"
                             onerror="this.src='https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=200&fit=crop&crop=center'"
                             loading="lazy">
                        <div class="absolute top-3 right-3">
                            <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">热门</span>
                        </div>
                        <div class="absolute bottom-3 left-3 text-white">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span class="text-sm">4.7</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">巢湖风景区</h3>
                        <p class="text-sm text-gray-500 mb-3">八百里巢湖，烟波浩渺，是合肥最美的自然风光</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>📍 巢湖市</span>
                                <span>🎫 免费</span>
                            </div>
                            <button class="bg-primary text-white text-sm px-4 py-2 rounded-lg">查看详情</button>
                        </div>
                    </div>
                </div>

                <!-- 景点卡片 2 -->
                <div onclick="openBaogongPark()" class="bg-white rounded-xl overflow-hidden shadow-sm cursor-pointer hover:shadow-md transition-shadow">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=200&fit=crop&crop=center"
                             alt="包公园" class="w-full h-48 object-cover"
                             onerror="this.src='https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=200&fit=crop&crop=center'"
                             loading="lazy">
                        <div class="absolute top-3 right-3">
                            <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">推荐</span>
                        </div>
                        <div class="absolute bottom-3 left-3 text-white">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span class="text-sm">4.6</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">包公园</h3>
                        <p class="text-sm text-gray-500 mb-3">包拯故里，清廉文化圣地，感受千年历史文化</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>📍 包河区</span>
                                <span>🎫 ¥20</span>
                            </div>
                            <button onclick="event.stopPropagation(); openBaogongPark()" class="bg-primary text-white text-sm px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 附近景点 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">附近景点</h2>
                <button class="text-sm text-primary">查看更多</button>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=200&h=120&fit=crop&crop=center"
                         alt="安徽博物院" class="w-full h-24 object-cover"
                         onerror="this.src='https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=200&h=120&fit=crop&crop=center'"
                         loading="lazy">
                    <div class="p-3">
                        <h3 class="font-medium text-gray-900 mb-1">安徽博物院</h3>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">1.2km</span>
                            <span class="text-xs text-gray-500">免费</span>
                        </div>
                    </div>
                </div>
                
                <div onclick="openSanheAncientTown()" class="bg-white rounded-xl overflow-hidden shadow-sm cursor-pointer hover:shadow-md transition-shadow">
                    <img src="https://images.unsplash.com/photo-1548013146-72479768bada?w=200&h=120&fit=crop&crop=center"
                         alt="三河古镇" class="w-full h-24 object-cover"
                         onerror="this.src='https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=200&h=120&fit=crop&crop=center'"
                         loading="lazy">
                    <div class="p-3">
                        <h3 class="font-medium text-gray-900 mb-1">三河古镇</h3>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">25km</span>
                            <span class="text-xs text-gray-500">¥80</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <!-- 对话气泡 -->
            <div class="chat-bubble absolute -top-16 -left-20 bg-white rounded-2xl px-4 py-2 shadow-lg border border-gray-200 max-w-32">
                <p class="text-xs text-gray-700">发现更多合肥美景！</p>
                <div class="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200"></div>
            </div>
            
            <!-- 虚拟人形象 -->
            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <!-- 卡通头像 -->
                <div class="relative">
                    <!-- 脸部 -->
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <!-- 眼睛 -->
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <!-- 嘴巴 -->
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <!-- 腮红 -->
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <!-- 帽子 -->
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>
            
            <!-- 在线状态指示器 -->
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div class="grid grid-cols-6 py-2 pb-safe">
            <button onclick="navigateTo('首页')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs">首页</span>
            </button>
            <button onclick="navigateTo('发现')" class="flex flex-col items-center py-2 text-primary">
                <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-xs font-medium">发现</span>
            </button>
            <button onclick="navigateTo('行程')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                </svg>
                <span class="text-xs">行程</span>
            </button>
            <button onclick="navigateTo('服务')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="text-xs">服务</span>
            </button>
            <button onclick="navigateTo('社区')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="text-xs">社区</span>
            </button>
            <button onclick="navigateTo('我的')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </nav>

    <script>
        // 页面导航功能
        function navigateTo(pageName) {
            const pageMap = {
                '首页': '文旅小程序-首页.html',
                '发现': '文旅小程序-发现.html',
                '行程': '文旅小程序-行程.html',
                '服务': '文旅小程序-服务.html',
                '社区': '文旅小程序-社区.html',
                '我的': '文旅小程序-我的.html'
            };
            
            if (pageMap[pageName]) {
                // 添加页面切换动画
                document.body.style.opacity = '0.8';
                document.body.style.transform = 'scale(0.95)';
                document.body.style.transition = 'all 0.3s ease-out';
                
                setTimeout(() => {
                    window.location.href = pageMap[pageName];
                }, 300);
            } else {
                // 如果页面不存在，显示提示
                showToast(`${pageName}页面正在开发中...`);
            }
        }
        
        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        function openChat() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        function toggleChat() {
            openChat();
        }

        function openBaogongPark() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-包公园.html';
            }, 300);
        }

        function openSanheAncientTown() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-三河古镇.html';
            }, 300);
        }

        // 图片加载失败处理
        function handleImageError(img) {
            const fallbackImages = [
                'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=200&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=200&fit=crop&crop=center'
            ];

            const randomFallback = fallbackImages[Math.floor(Math.random() * fallbackImages.length)];

            if (img.src !== randomFallback) {
                img.src = randomFallback;
            } else {
                // 如果备用图片也失败，显示占位符
                img.style.display = 'none';
                const placeholder = document.createElement('div');
                placeholder.className = 'image-placeholder w-full h-48 object-cover';
                placeholder.innerHTML = '🏞️';
                img.parentNode.insertBefore(placeholder, img);
            }
        }

        // 图片源配置
        const imageConfig = {
            '巢湖风景区': [
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=200&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400&h=200&fit=crop&crop=center'
            ],
            '包公园': [
                'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=200&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=200&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop&crop=center'
            ],
            '三河古镇': [
                'https://images.unsplash.com/photo-1548013146-72479768bada?w=200&h=120&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=200&h=120&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=120&fit=crop&crop=center'
            ],
            '安徽博物院': [
                'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=200&h=120&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=200&h=120&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=200&h=120&fit=crop&crop=center'
            ]
        };

        // 预加载图片
        function preloadImages() {
            Object.values(imageConfig).flat().forEach(src => {
                const img = new Image();
                img.src = src;
            });
        }

        // 智能图片加载
        function loadImageWithFallback(img, altText) {
            const sources = imageConfig[altText] || [
                'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=200&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop&crop=center'
            ];

            let currentIndex = 0;

            function tryNextImage() {
                if (currentIndex < sources.length) {
                    img.src = sources[currentIndex];
                    currentIndex++;
                } else {
                    // 所有图片都失败，显示占位符
                    showImagePlaceholder(img);
                }
            }

            img.addEventListener('error', tryNextImage);
            tryNextImage();
        }

        // 显示图片占位符
        function showImagePlaceholder(img) {
            img.style.display = 'none';
            const placeholder = document.createElement('div');
            placeholder.className = img.className.replace('object-cover', '') + ' image-placeholder';

            const icon = img.alt.includes('巢湖') ? '🌊' :
                        img.alt.includes('包公园') ? '⚖️' :
                        img.alt.includes('三河古镇') ? '🏮' :
                        img.alt.includes('博物院') ? '🏛️' : '🏞️';

            placeholder.innerHTML = `
                <div class="text-center">
                    <div class="text-4xl mb-2">${icon}</div>
                    <div class="text-sm text-gray-500">${img.alt}</div>
                </div>
            `;

            img.parentNode.insertBefore(placeholder, img);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 预加载图片
            preloadImages();

            // 处理所有图片
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.classList.add('loading-shimmer');

                img.addEventListener('load', function() {
                    this.classList.remove('loading-shimmer');
                });

                // 如果图片已经有错误处理，跳过
                if (!img.hasAttribute('onerror')) {
                    loadImageWithFallback(img, img.alt);
                }
            });
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前页面的导航状态
            setActiveNavigation('发现');
        });
        
        // 设置当前激活的导航项
        function setActiveNavigation(currentPage) {
            const navButtons = document.querySelectorAll('nav button');
            navButtons.forEach(button => {
                const span = button.querySelector('span');
                if (span && span.textContent === currentPage) {
                    button.className = button.className.replace('text-gray-400', 'text-primary');
                    span.className = span.className.includes('font-medium') ? span.className : span.className + ' font-medium';
                } else {
                    button.className = button.className.replace('text-primary', 'text-gray-400');
                    span.className = span.className.replace(' font-medium', '');
                }
            });
        }
    </script>
</body>
</html>
