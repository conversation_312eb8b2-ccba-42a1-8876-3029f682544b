<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 行程</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body::-webkit-scrollbar {
            display: none;
        }
        
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
        
        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .chat-bubble {
            animation: fadeInOut 4s ease-in-out infinite;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: scale(0.8); }
            25%, 75% { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white px-4 py-3 shadow-sm">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <button onclick="history.back()" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <div>
                    <h1 class="text-lg font-bold text-gray-900">我的行程</h1>
                    <p class="text-xs text-gray-500">智能规划，轻松出行</p>
                </div>
            </div>
            <button class="p-2 bg-primary text-white rounded-lg" onclick="createTrip()">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="px-4 py-4 space-y-6 pb-24">
        <!-- 智能规划入口 -->
        <section class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-bold mb-2">AI智能规划</h2>
                    <p class="text-sm opacity-90">告诉我您的需求，为您定制专属行程</p>
                </div>
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
            </div>
            <button onclick="startAIPlanning()" class="mt-4 bg-white text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                开始规划
            </button>
        </section>

        <!-- 我的行程列表 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">我的行程</h2>
                <button class="text-sm text-primary">管理</button>
            </div>
            
            <div class="space-y-4">
                <!-- 行程卡片 1 -->
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-start justify-between mb-3">
                        <div>
                            <h3 class="font-semibold text-gray-900">合肥一日游</h3>
                            <p class="text-sm text-gray-500">3月20日 - 3月20日</p>
                        </div>
                        <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">进行中</span>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">09:00 包公园</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <span class="text-sm text-gray-500">12:00 庐州烤鸭店</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <span class="text-sm text-gray-500">14:00 安徽博物院</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>📍 3个景点</span>
                            <span>⏱️ 8小时</span>
                        </div>
                        <button class="bg-primary text-white text-sm px-4 py-2 rounded-lg">查看详情</button>
                    </div>
                </div>

                <!-- 行程卡片 2 -->
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-start justify-between mb-3">
                        <div>
                            <h3 class="font-semibold text-gray-900">巢湖周末游</h3>
                            <p class="text-sm text-gray-500">3月25日 - 3月26日</p>
                        </div>
                        <span class="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">计划中</span>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <span class="text-sm text-gray-500">Day1: 巢湖风景区</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                            <span class="text-sm text-gray-500">Day2: 三河古镇</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>📍 5个景点</span>
                            <span>⏱️ 2天</span>
                        </div>
                        <button class="bg-gray-100 text-gray-700 text-sm px-4 py-2 rounded-lg">编辑行程</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 推荐行程 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">推荐行程</h2>
                <button class="text-sm text-primary">查看更多</button>
            </div>
            
            <div class="grid grid-cols-1 gap-4">
                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=160&fit=crop&crop=center" 
                         alt="合肥经典一日游" class="w-full h-32 object-cover">
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">合肥经典一日游</h3>
                        <p class="text-sm text-gray-500 mb-3">包公园 → 李鸿章故居 → 安徽博物院</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <span>⭐ 4.8</span>
                                <span>👥 1.2k人体验</span>
                            </div>
                            <button class="bg-primary text-white text-sm px-4 py-2 rounded-lg">使用模板</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <div class="chat-bubble absolute -top-16 -left-20 bg-white rounded-2xl px-4 py-2 shadow-lg border border-gray-200 max-w-32">
                <p class="text-xs text-gray-700">需要规划行程吗？</p>
                <div class="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200"></div>
            </div>
            
            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <div class="relative">
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>
            
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div class="grid grid-cols-6 py-2 pb-safe">
            <button onclick="navigateTo('首页')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs">首页</span>
            </button>
            <button onclick="navigateTo('发现')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="text-xs">发现</span>
            </button>
            <button onclick="navigateTo('行程')" class="flex flex-col items-center py-2 text-primary">
                <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-xs font-medium">行程</span>
            </button>
            <button onclick="navigateTo('服务')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="text-xs">服务</span>
            </button>
            <button onclick="navigateTo('社区')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="text-xs">社区</span>
            </button>
            <button onclick="navigateTo('我的')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </nav>

    <script>
        function navigateTo(pageName) {
            const pageMap = {
                '首页': '文旅小程序-首页.html',
                '发现': '文旅小程序-发现.html',
                '行程': '文旅小程序-行程.html',
                '服务': '文旅小程序-服务.html',
                '社区': '文旅小程序-社区.html',
                '我的': '文旅小程序-我的.html'
            };
            
            if (pageMap[pageName]) {
                document.body.style.opacity = '0.8';
                document.body.style.transform = 'scale(0.95)';
                document.body.style.transition = 'all 0.3s ease-out';
                
                setTimeout(() => {
                    window.location.href = pageMap[pageName];
                }, 300);
            } else {
                showToast(`${pageName}页面正在开发中...`);
            }
        }
        
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        function createTrip() {
            showToast('创建新行程功能开发中...');
        }

        function startAIPlanning() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-行程规划.html';
            }, 300);
        }

        function openChat() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        function toggleChat() {
            openChat();
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            setActiveNavigation('行程');
        });
        
        function setActiveNavigation(currentPage) {
            const navButtons = document.querySelectorAll('nav button');
            navButtons.forEach(button => {
                const span = button.querySelector('span');
                if (span && span.textContent === currentPage) {
                    button.className = button.className.replace('text-gray-400', 'text-primary');
                    span.className = span.className.includes('font-medium') ? span.className : span.className + ' font-medium';
                } else {
                    button.className = button.className.replace('text-primary', 'text-gray-400');
                    span.className = span.className.replace(' font-medium', '');
                }
            });
        }
    </script>
</body>
</html>
