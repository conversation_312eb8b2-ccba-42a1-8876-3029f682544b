<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 巢湖风景区</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body::-webkit-scrollbar {
            display: none;
        }
        
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
        
        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .hero-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 15c8.284 0 15 6.716 15 15s-6.716 15-15 15-15-6.716-15-15 6.716-15 15-15zm0 2c-7.18 0-13 5.82-13 13s5.82 13 13 13 13-5.82 13-13-5.82-13-13-13z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .feature-card {
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .wave-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='20' viewBox='0 0 100 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 10c10-5 20-5 30 0s20 5 30 0 20-5 30 0 20 5 30 0v10H0V10z' fill='%23e0f2fe' fill-opacity='0.5'/%3E%3C/svg%3E");
            background-repeat: repeat-x;
            background-position: bottom;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white px-4 py-3 shadow-sm sticky top-0 z-30">
        <div class="flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            
            <div class="flex-1">
                <h1 class="text-lg font-bold text-gray-900">巢湖风景区</h1>
                <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>合肥市巢湖市</span>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <button onclick="shareLocation()" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                    </svg>
                </button>
                <button onclick="addToFavorites()" class="p-2 text-gray-400 hover:text-red-500">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- 英雄区域 -->
    <section class="hero-gradient wave-pattern text-white px-4 py-8">
        <div class="text-center">
            <h2 class="text-2xl font-bold mb-2">八百里巢湖</h2>
            <p class="text-blue-100 mb-4">烟波浩渺，合肥最美的自然风光</p>
            <div class="flex items-center justify-center space-x-6 text-sm">
                <div class="flex items-center space-x-1">
                    <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span>4.7分</span>
                </div>
                <span>🎫 免费</span>
                <span>⏰ 全天开放</span>
            </div>
        </div>
    </section>

    <!-- 景点介绍 -->
    <section class="px-4 py-6">
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">景区介绍</h3>
            <p class="text-gray-700 leading-relaxed mb-4">
                巢湖是中国五大淡水湖之一，素有"八百里巢湖"之称。湖水清澈，烟波浩渺，
                四周群山环抱，风景秀丽。春季湖畔樱花盛开，夏季荷花满塘，秋季芦苇飞舞，
                冬季候鸟翔集，四季景色各异，美不胜收。
            </p>
            <p class="text-gray-700 leading-relaxed">
                巢湖不仅自然风光优美，还有着深厚的历史文化底蕴。这里是古代兵家必争之地，
                留下了许多历史遗迹和传说故事。现在的巢湖已成为合肥市民休闲度假的首选之地，
                也是摄影爱好者和自然爱好者的天堂。
            </p>
        </div>

        <!-- 主要景点 -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">主要景点</h3>
            <div class="space-y-4">
                <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-2xl">🌊</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">湖心岛</h4>
                            <p class="text-sm text-gray-600">巢湖中央的小岛，可乘船前往，是观赏湖景的最佳位置</p>
                        </div>
                    </div>
                </div>
                
                <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-2xl">🌸</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">樱花大道</h4>
                            <p class="text-sm text-gray-600">春季樱花盛开的湖畔大道，是赏花拍照的热门地点</p>
                        </div>
                    </div>
                </div>
                
                <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-2xl">🦢</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">候鸟观测点</h4>
                            <p class="text-sm text-gray-600">冬季候鸟栖息地，可观赏到各种珍稀鸟类</p>
                        </div>
                    </div>
                </div>
                
                <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-2xl">🚤</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">游船码头</h4>
                            <p class="text-sm text-gray-600">乘船游湖，从水上欣赏巢湖全景，体验不同视角</p>
                        </div>
                    </div>
                </div>
                
                <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-2xl">🏔️</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">环湖观景台</h4>
                            <p class="text-sm text-gray-600">多个观景台分布湖畔，可俯瞰巢湖全貌</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最佳游览时间 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">最佳游览时间</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-4 bg-pink-50 rounded-xl">
                    <div class="text-2xl mb-2">🌸</div>
                    <h4 class="font-semibold text-gray-900 mb-1">春季 (3-5月)</h4>
                    <p class="text-sm text-gray-600">樱花盛开，气候宜人</p>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-xl">
                    <div class="text-2xl mb-2">🌞</div>
                    <h4 class="font-semibold text-gray-900 mb-1">夏季 (6-8月)</h4>
                    <p class="text-sm text-gray-600">荷花满塘，避暑胜地</p>
                </div>
                <div class="text-center p-4 bg-orange-50 rounded-xl">
                    <div class="text-2xl mb-2">🍂</div>
                    <h4 class="font-semibold text-gray-900 mb-1">秋季 (9-11月)</h4>
                    <p class="text-sm text-gray-600">芦苇飞舞，层林尽染</p>
                </div>
                <div class="text-center p-4 bg-blue-50 rounded-xl">
                    <div class="text-2xl mb-2">❄️</div>
                    <h4 class="font-semibold text-gray-900 mb-1">冬季 (12-2月)</h4>
                    <p class="text-sm text-gray-600">候鸟翔集，宁静致远</p>
                </div>
            </div>
        </div>

        <!-- 游览信息 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">游览信息</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">开放时间</span>
                    <span class="font-medium text-gray-900">全天开放</span>
                </div>
                <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">门票价格</span>
                    <span class="font-medium text-gray-900">免费</span>
                </div>
                <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">游船票价</span>
                    <span class="font-medium text-gray-900">¥30-80/人</span>
                </div>
                <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">建议游览时间</span>
                    <span class="font-medium text-gray-900">半天-1天</span>
                </div>
                <div class="flex items-center justify-between py-2">
                    <span class="text-gray-600">交通方式</span>
                    <span class="font-medium text-gray-900">自驾或旅游专线</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 操作按钮 -->
    <section class="px-4 pb-8">
        <div class="grid grid-cols-2 gap-4">
            <button onclick="openNavigation()" class="bg-primary text-white py-4 rounded-xl font-semibold">
                🧭 导航前往
            </button>
            <button onclick="bookBoat()" class="bg-blue-500 text-white py-4 rounded-xl font-semibold">
                🚤 预订游船
            </button>
        </div>
    </section>

    <!-- 加入行程按钮 -->
    <div class="fixed left-4 bottom-24 z-40">
        <button onclick="addToTrip()" class="bg-purple-500 text-white px-4 py-3 rounded-full shadow-lg hover:bg-purple-600 transition-colors flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span class="text-sm font-medium">加入行程</span>
        </button>
    </div>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <div class="relative">
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>
            
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <script>
        // 分享位置
        function shareLocation() {
            showToast('位置信息已复制到剪贴板');
        }
        
        // 添加收藏
        function addToFavorites() {
            const button = event.currentTarget;
            const icon = button.querySelector('svg');
            
            if (button.classList.contains('favorited')) {
                button.classList.remove('favorited', 'text-red-500');
                button.classList.add('text-gray-400');
                icon.setAttribute('fill', 'none');
                showToast('已取消收藏');
            } else {
                button.classList.add('favorited', 'text-red-500');
                button.classList.remove('text-gray-400');
                icon.setAttribute('fill', 'currentColor');
                showToast('已添加到收藏');
            }
        }
        
        // 打开导航
        function openNavigation() {
            showToast('正在打开导航应用...');
        }
        
        // 预订游船
        function bookBoat() {
            showToast('正在跳转到游船预订页面...');
        }
        
        // 打开智能对话
        function openChat() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        // 加入行程
        function addToTrip() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-行程规划.html';
            }, 300);
        }
        
        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }
    </script>
</body>
</html>
