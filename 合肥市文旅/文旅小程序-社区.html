<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 社区</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        body::-webkit-scrollbar {
            display: none;
        }

        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }

        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .chat-bubble {
            animation: fadeInOut 4s ease-in-out infinite;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: scale(0.8); }
            25%, 75% { opacity: 1; transform: scale(1); }
        }

        .heart-animation {
            animation: heartBeat 0.6s ease-in-out;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white px-4 py-3 shadow-sm sticky top-0 z-30">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <button onclick="history.back()" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <div>
                    <h1 class="text-lg font-bold text-gray-900">旅游社区</h1>
                    <p class="text-xs text-gray-500">分享你的旅行故事</p>
                </div>
            </div>
            <button onclick="createPost()" class="p-2 bg-primary text-white rounded-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
        </div>

        <!-- 分类标签 -->
        <div class="flex space-x-3 overflow-x-auto mt-3 pb-1">
            <button onclick="filterPosts('all')" class="px-4 py-2 bg-primary text-white text-sm rounded-full whitespace-nowrap">全部</button>
            <button onclick="filterPosts('travel')" class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">游记</button>
            <button onclick="filterPosts('food')" class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">美食</button>
            <button onclick="filterPosts('guide')" class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">攻略</button>
            <button onclick="filterPosts('photo')" class="px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap">摄影</button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="px-4 py-4 space-y-4 pb-24">
        <!-- 热门推荐 -->
        <section class="bg-gradient-to-r from-orange-400 to-pink-500 rounded-2xl p-4 text-white">
            <div class="flex items-center justify-between mb-3">
                <div>
                    <h2 class="text-lg font-bold">🔥 热门游记</h2>
                    <p class="text-sm opacity-90">最受欢迎的旅行分享</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
            </div>
            <button onclick="viewHotPosts()" class="bg-white/20 text-white px-4 py-2 rounded-lg text-sm font-medium">
                查看热门
            </button>
        </section>

        <!-- 游记列表 -->
        <section class="space-y-4">
            <!-- 游记卡片 1 -->
            <article class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                <!-- 用户信息 -->
                <div class="flex items-center space-x-3 p-4 pb-3">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face"
                         alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">小雨同学</h3>
                        <p class="text-xs text-gray-500">2小时前 · 巢湖风景区</p>
                    </div>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01"></path>
                        </svg>
                    </button>
                </div>

                <!-- 内容 -->
                <div class="px-4 pb-3">
                    <h4 class="font-semibold text-gray-900 mb-2">春日巢湖，烟波浩渺的诗意之旅 🌸</h4>
                    <p class="text-gray-700 text-sm leading-relaxed mb-3">
                        今天和朋友们一起去了巢湖，真的被这里的美景震撼到了！八百里巢湖烟波浩渺，春日的湖水格外清澈，远山如黛，近水含烟...
                    </p>
                </div>

                <!-- 图片 -->
                <div class="px-4 pb-3">
                    <div class="grid grid-cols-3 gap-2">
                        <img src="https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=120&h=120&fit=crop&crop=center"
                             alt="巢湖美景" class="w-full h-24 rounded-lg object-cover">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=120&fit=crop&crop=center"
                             alt="湖边风光" class="w-full h-24 rounded-lg object-cover">
                        <div class="relative w-full h-24 rounded-lg bg-gray-100 flex items-center justify-center">
                            <span class="text-gray-500 text-sm">+6</span>
                            <div class="absolute inset-0 bg-black/20 rounded-lg"></div>
                        </div>
                    </div>
                </div>

                <!-- 标签 -->
                <div class="px-4 pb-3">
                    <div class="flex space-x-2">
                        <span class="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">#巢湖</span>
                        <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">#春游</span>
                        <span class="bg-pink-100 text-pink-700 text-xs px-2 py-1 rounded-full">#摄影</span>
                    </div>
                </div>

                <!-- 互动区域 -->
                <div class="flex items-center justify-between px-4 py-3 border-t border-gray-100">
                    <div class="flex items-center space-x-6">
                        <button onclick="likePost(this)" class="flex items-center space-x-1 text-gray-500 hover:text-red-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            <span class="text-sm">128</span>
                        </button>
                        <button onclick="commentPost()" class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <span class="text-sm">32</span>
                        </button>
                        <button onclick="collectPost(this)" class="flex items-center space-x-1 text-gray-500 hover:text-yellow-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                            </svg>
                            <span class="text-sm">收藏</span>
                        </button>
                    </div>
                    <button onclick="sharePost()" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                    </button>
                </div>
            </article>

            <!-- 游记卡片 2 -->
            <article class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                <!-- 用户信息 -->
                <div class="flex items-center space-x-3 p-4 pb-3">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face"
                         alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">旅行达人Leo</h3>
                        <p class="text-xs text-gray-500">5小时前 · 包公园</p>
                    </div>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01"></path>
                        </svg>
                    </button>
                </div>

                <!-- 内容 -->
                <div class="px-4 pb-3">
                    <h4 class="font-semibold text-gray-900 mb-2">包公园深度游：感受千年清廉文化 ⚖️</h4>
                    <p class="text-gray-700 text-sm leading-relaxed mb-3">
                        包公园不仅仅是一个景点，更是一座活着的历史博物馆。走进包公祠，仿佛能听到包拯铁面无私的声音...
                    </p>
                </div>

                <!-- 单张大图 -->
                <div class="px-4 pb-3">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop&crop=center"
                         alt="包公园" class="w-full h-48 rounded-xl object-cover">
                </div>

                <!-- 标签 -->
                <div class="px-4 pb-3">
                    <div class="flex space-x-2">
                        <span class="bg-purple-100 text-purple-700 text-xs px-2 py-1 rounded-full">#包公园</span>
                        <span class="bg-orange-100 text-orange-700 text-xs px-2 py-1 rounded-full">#历史文化</span>
                        <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">#攻略</span>
                    </div>
                </div>

                <!-- 互动区域 -->
                <div class="flex items-center justify-between px-4 py-3 border-t border-gray-100">
                    <div class="flex items-center space-x-6">
                        <button onclick="likePost(this)" class="flex items-center space-x-1 text-gray-500 hover:text-red-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            <span class="text-sm">89</span>
                        </button>
                        <button onclick="commentPost()" class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <span class="text-sm">15</span>
                        </button>
                        <button onclick="collectPost(this)" class="flex items-center space-x-1 text-gray-500 hover:text-yellow-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                            </svg>
                            <span class="text-sm">收藏</span>
                        </button>
                    </div>
                    <button onclick="sharePost()" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                    </button>
                </div>
            </article>
        </section>

        <!-- 美食分享卡片 -->
        <section class="space-y-4">
            <article class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                <!-- 用户信息 -->
                <div class="flex items-center space-x-3 p-4 pb-3">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face"
                         alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">美食探索家</h3>
                        <p class="text-xs text-gray-500">1天前 · 庐州烤鸭店</p>
                    </div>
                    <span class="bg-orange-100 text-orange-700 text-xs px-2 py-1 rounded-full">美食</span>
                </div>

                <!-- 内容 -->
                <div class="px-4 pb-3">
                    <h4 class="font-semibold text-gray-900 mb-2">庐州烤鸭：合肥人的味蕾记忆 🦆</h4>
                    <p class="text-gray-700 text-sm leading-relaxed mb-3">
                        作为合肥的经典美食，庐州烤鸭承载着几代人的味蕾记忆。皮脆肉嫩，香而不腻，配上特制的甜面酱...
                    </p>
                </div>

                <!-- 图片 -->
                <div class="px-4 pb-3">
                    <div class="grid grid-cols-2 gap-2">
                        <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=180&h=120&fit=crop&crop=center"
                             alt="庐州烤鸭" class="w-full h-24 rounded-lg object-cover">
                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=180&h=120&fit=crop&crop=center"
                             alt="美食环境" class="w-full h-24 rounded-lg object-cover">
                    </div>
                </div>

                <!-- 标签 -->
                <div class="px-4 pb-3">
                    <div class="flex space-x-2">
                        <span class="bg-red-100 text-red-700 text-xs px-2 py-1 rounded-full">#庐州烤鸭</span>
                        <span class="bg-yellow-100 text-yellow-700 text-xs px-2 py-1 rounded-full">#合肥美食</span>
                        <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">#推荐</span>
                    </div>
                </div>

                <!-- 互动区域 -->
                <div class="flex items-center justify-between px-4 py-3 border-t border-gray-100">
                    <div class="flex items-center space-x-6">
                        <button onclick="likePost(this)" class="flex items-center space-x-1 text-gray-500 hover:text-red-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            <span class="text-sm">76</span>
                        </button>
                        <button onclick="commentPost()" class="flex items-center space-x-1 text-gray-500 hover:text-blue-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <span class="text-sm">23</span>
                        </button>
                        <button onclick="collectPost(this)" class="flex items-center space-x-1 text-gray-500 hover:text-yellow-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                            </svg>
                            <span class="text-sm">收藏</span>
                        </button>
                    </div>
                    <button onclick="sharePost()" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                    </button>
                </div>
            </article>
        </section>
    </main>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <div class="chat-bubble absolute -top-16 -left-20 bg-white rounded-2xl px-4 py-2 shadow-lg border border-gray-200 max-w-32">
                <p class="text-xs text-gray-700">分享你的旅行故事吧！</p>
                <div class="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200"></div>
            </div>

            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <div class="relative">
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>

            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div class="grid grid-cols-6 py-2 pb-safe">
            <button onclick="navigateTo('首页')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs">首页</span>
            </button>
            <button onclick="navigateTo('发现')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="text-xs">发现</span>
            </button>
            <button onclick="navigateTo('行程')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                </svg>
                <span class="text-xs">行程</span>
            </button>
            <button onclick="navigateTo('服务')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="text-xs">服务</span>
            </button>
            <button onclick="navigateTo('社区')" class="flex flex-col items-center py-2 text-primary">
                <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                </svg>
                <span class="text-xs font-medium">社区</span>
            </button>
            <button onclick="navigateTo('我的')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </nav>

    <script>
        function navigateTo(pageName) {
            const pageMap = {
                '首页': '文旅小程序-首页.html',
                '发现': '文旅小程序-发现.html',
                '行程': '文旅小程序-行程.html',
                '服务': '文旅小程序-服务.html',
                '社区': '文旅小程序-社区.html',
                '我的': '文旅小程序-我的.html'
            };

            if (pageMap[pageName]) {
                document.body.style.opacity = '0.8';
                document.body.style.transform = 'scale(0.95)';
                document.body.style.transition = 'all 0.3s ease-out';

                setTimeout(() => {
                    window.location.href = pageMap[pageName];
                }, 300);
            } else {
                showToast(`${pageName}页面正在开发中...`);
            }
        }

        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        // 社区功能
        function createPost() {
            showToast('发布游记功能开发中...');
        }

        function viewHotPosts() {
            showToast('查看热门游记功能开发中...');
        }

        function filterPosts(category) {
            // 更新分类按钮状态
            const buttons = document.querySelectorAll('header button');
            buttons.forEach(btn => {
                if (btn.textContent.includes('全部') && category === 'all') {
                    btn.className = 'px-4 py-2 bg-primary text-white text-sm rounded-full whitespace-nowrap';
                } else if (btn.textContent.includes('游记') && category === 'travel') {
                    btn.className = 'px-4 py-2 bg-primary text-white text-sm rounded-full whitespace-nowrap';
                } else if (btn.textContent.includes('美食') && category === 'food') {
                    btn.className = 'px-4 py-2 bg-primary text-white text-sm rounded-full whitespace-nowrap';
                } else if (btn.textContent.includes('攻略') && category === 'guide') {
                    btn.className = 'px-4 py-2 bg-primary text-white text-sm rounded-full whitespace-nowrap';
                } else if (btn.textContent.includes('摄影') && category === 'photo') {
                    btn.className = 'px-4 py-2 bg-primary text-white text-sm rounded-full whitespace-nowrap';
                } else {
                    btn.className = 'px-4 py-2 bg-gray-100 text-gray-600 text-sm rounded-full whitespace-nowrap';
                }
            });

            const categoryNames = {
                'all': '全部',
                'travel': '游记',
                'food': '美食',
                'guide': '攻略',
                'photo': '摄影'
            };

            showToast(`筛选${categoryNames[category]}内容...`);
        }

        function likePost(button) {
            const heartIcon = button.querySelector('svg');
            const countSpan = button.querySelector('span');
            let count = parseInt(countSpan.textContent);

            // 添加动画效果
            heartIcon.classList.add('heart-animation');

            // 切换点赞状态
            if (button.classList.contains('liked')) {
                button.classList.remove('liked');
                button.classList.remove('text-red-500');
                button.classList.add('text-gray-500');
                heartIcon.setAttribute('fill', 'none');
                countSpan.textContent = count - 1;
            } else {
                button.classList.add('liked');
                button.classList.remove('text-gray-500');
                button.classList.add('text-red-500');
                heartIcon.setAttribute('fill', 'currentColor');
                countSpan.textContent = count + 1;
            }

            // 移除动画类
            setTimeout(() => {
                heartIcon.classList.remove('heart-animation');
            }, 600);
        }

        function commentPost() {
            showToast('评论功能开发中...');
        }

        function collectPost(button) {
            const bookmarkIcon = button.querySelector('svg');
            const textSpan = button.querySelector('span');

            // 切换收藏状态
            if (button.classList.contains('collected')) {
                button.classList.remove('collected');
                button.classList.remove('text-yellow-500');
                button.classList.add('text-gray-500');
                bookmarkIcon.setAttribute('fill', 'none');
                textSpan.textContent = '收藏';
                showToast('已取消收藏');
            } else {
                button.classList.add('collected');
                button.classList.remove('text-gray-500');
                button.classList.add('text-yellow-500');
                bookmarkIcon.setAttribute('fill', 'currentColor');
                textSpan.textContent = '已收藏';
                showToast('收藏成功');
            }
        }

        function sharePost() {
            showToast('分享功能开发中...');
        }

        function openChat() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        function toggleChat() {
            openChat();
        }

        document.addEventListener('DOMContentLoaded', function() {
            setActiveNavigation('社区');
        });

        function setActiveNavigation(currentPage) {
            const navButtons = document.querySelectorAll('nav button');
            navButtons.forEach(button => {
                const span = button.querySelector('span');
                if (span && span.textContent === currentPage) {
                    button.className = button.className.replace('text-gray-400', 'text-primary');
                    span.className = span.className.includes('font-medium') ? span.className : span.className + ' font-medium';
                } else {
                    button.className = button.className.replace('text-primary', 'text-gray-400');
                    span.className = span.className.replace(' font-medium', '');
                }
            });
        }
    </script>
</body>
</html>