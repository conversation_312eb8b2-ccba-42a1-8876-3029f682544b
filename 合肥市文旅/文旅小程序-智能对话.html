<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 智能对话</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body::-webkit-scrollbar {
            display: none;
        }
        
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
        
        .typing-animation {
            animation: typing 1.5s infinite;
        }
        
        @keyframes typing {
            0%, 60%, 100% { opacity: 1; }
            30% { opacity: 0.4; }
        }
        
        .message-slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white px-4 py-3 shadow-sm sticky top-0 z-30">
        <div class="flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            
            <!-- 小肥头像 -->
            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                <div class="relative">
                    <div class="w-8 h-8 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-2 left-1.5 w-1.5 h-1.5 bg-black rounded-full"></div>
                        <div class="absolute top-2 right-1.5 w-1.5 h-1.5 bg-black rounded-full"></div>
                        <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-1.5 border-2 border-black border-t-0 rounded-b-full"></div>
                    </div>
                </div>
            </div>
            
            <div class="flex-1">
                <h1 class="text-lg font-bold text-gray-900">小肥智能助手</h1>
                <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-xs text-gray-500">在线服务中</span>
                </div>
            </div>
            
            <button onclick="clearChat()" class="p-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
    </header>

    <!-- 推荐问题 -->
    <section class="px-4 py-4 bg-white border-b border-gray-100">
        <h2 class="text-sm font-semibold text-gray-700 mb-3">💡 推荐问题</h2>
        <div class="grid grid-cols-1 gap-2">
            <button onclick="askQuestion('合肥有哪些必去的景点？')" 
                    class="text-left p-3 bg-blue-50 text-blue-700 rounded-xl text-sm hover:bg-blue-100 transition-colors">
                🏞️ 合肥有哪些必去的景点？
            </button>
            <button onclick="askQuestion('推荐一些合肥特色美食')" 
                    class="text-left p-3 bg-green-50 text-green-700 rounded-xl text-sm hover:bg-green-100 transition-colors">
                🍜 推荐一些合肥特色美食
            </button>
            <button onclick="openTripPlanning()"
                    class="text-left p-3 bg-purple-50 text-purple-700 rounded-xl text-sm hover:bg-purple-100 transition-colors">
                📅 帮我规划一日游行程
            </button>
            <button onclick="askQuestion('骆岗公园怎么去？')" 
                    class="text-left p-3 bg-orange-50 text-orange-700 rounded-xl text-sm hover:bg-orange-100 transition-colors">
                🚗 骆岗公园怎么去？
            </button>
        </div>
    </section>

    <!-- 对话区域 -->
    <main id="chatContainer" class="flex-1 px-4 py-4 space-y-4 pb-24 min-h-96">
        <!-- 欢迎消息 -->
        <div class="flex items-start space-x-3 message-slide-in">
            <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                <div class="w-6 h-6 bg-yellow-200 rounded-full relative">
                    <div class="absolute top-1.5 left-1 w-1 h-1 bg-black rounded-full"></div>
                    <div class="absolute top-1.5 right-1 w-1 h-1 bg-black rounded-full"></div>
                    <div class="absolute bottom-1.5 left-1/2 transform -translate-x-1/2 w-2 h-1 border border-black border-t-0 rounded-b-full"></div>
                </div>
            </div>
            <div class="flex-1">
                <div class="bg-white rounded-2xl rounded-tl-md p-4 shadow-sm border border-gray-100">
                    <p class="text-gray-800 text-sm leading-relaxed">
                        你好！我是小肥，你的专属合肥旅游助手！🎉
                        <br><br>
                        我可以帮你：
                        <br>• 🏞️ 推荐热门景点和隐藏宝藏
                        <br>• 🍜 寻找地道美食和特色餐厅  
                        <br>• 📅 制定个性化旅游行程
                        <br>• 🚗 提供交通和导航信息
                        <br>• 🏨 推荐优质酒店住宿
                        <br><br>
                        有什么想了解的，尽管问我吧！😊
                    </p>
                </div>
                <div class="text-xs text-gray-400 mt-1 ml-2">刚刚</div>
            </div>
        </div>
    </main>

    <!-- 输入区域 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3 pb-safe">
        <div class="flex items-end space-x-3">
            <div class="flex-1 relative">
                <textarea id="messageInput" 
                         placeholder="输入你想了解的问题..." 
                         class="w-full px-4 py-3 pr-12 bg-gray-100 rounded-2xl text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white"
                         rows="1"
                         maxlength="500"></textarea>
                <button onclick="sendMessage()" 
                        class="absolute right-2 bottom-2 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
            <button onclick="voiceInput()" 
                    class="w-12 h-12 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                </svg>
            </button>
        </div>
    </div>

    <script>
        let messageCount = 1;
        
        // 预设回答
        const responses = {
            '合肥有哪些必去的景点？': {
                text: '合肥有很多值得一去的景点呢！🏞️\n\n🌊 **巢湖风景区** - 八百里巢湖烟波浩渺，是合肥最美的自然风光\n\n🌳 **骆岗公园** - 城市绿肺，生态乐园，免费开放的休闲好去处\n\n🏛️ **安徽博物院** - 了解安徽历史文化的最佳场所\n\n🏮 **三河古镇** - 千年古镇，体验江南水乡风情\n\n⚖️ **包公园** - 包拯故里，感受清廉文化\n\n需要我详细介绍某个景点吗？',
                delay: 2000
            },
            '推荐一些合肥特色美食': {
                text: '合肥的美食可是很有特色的！🍜\n\n🦆 **庐州烤鸭** - 合肥最著名的特色菜，皮脆肉嫩\n\n🥟 **包子** - 合肥的包子特别香，推荐老字号店铺\n\n🍲 **李鸿章大杂烩** - 历史名菜，营养丰富\n\n🐟 **巢湖银鱼** - 巢湖特产，鲜美无比\n\n🥬 **毛豆腐** - 安徽特色小吃，外焦内嫩\n\n想知道哪家店比较正宗吗？我可以推荐具体位置哦！',
                delay: 2500
            },
            '帮我规划一日游行程': {
                text: '为你推荐一个经典的合肥一日游行程！📅\n\n**上午 9:00-11:30**\n🌳 骆岗公园 - 晨练散步，呼吸新鲜空气\n\n**中午 12:00-13:30**\n🍜 庐州烤鸭店 - 品尝正宗合肥美食\n\n**下午 14:00-16:30**\n🏛️ 安徽博物院 - 了解安徽历史文化\n\n**傍晚 17:00-19:00**\n🌊 巢湖风景区 - 欣赏湖光山色，拍照留念\n\n**晚上 19:30**\n🏮 返回市区，可选择夜游老城区\n\n需要我调整行程或提供交通路线吗？',
                delay: 3000
            },
            '骆岗公园怎么去？': {
                text: '骆岗公园的交通很方便！🚗\n\n**🚌 公交路线：**\n• 乘坐地铁2号线到骆岗站，步行约10分钟\n• 公交126路、150路直达骆岗公园站\n\n**🚗 自驾路线：**\n• 导航搜索"骆岗公园"即可\n• 公园有免费停车场\n\n**📍 具体位置：**\n合肥市包河区骆岗街道\n\n**⏰ 开放时间：**\n全天24小时开放，免费入园\n\n**💡 小贴士：**\n建议早上或傍晚去，空气更清新，景色更美！\n\n还需要了解公园内的具体景点吗？',
                delay: 2000
            }
        };
        
        function askQuestion(question) {
            // 添加用户消息
            addUserMessage(question);
            
            // 显示小肥正在输入
            showTyping();
            
            // 延迟显示回答
            const response = responses[question];
            if (response) {
                setTimeout(() => {
                    hideTyping();
                    addBotMessage(response.text);
                }, response.delay);
            } else {
                setTimeout(() => {
                    hideTyping();
                    addBotMessage('这个问题很有趣！我正在学习中，请稍后再试或者换个问题问我吧！😊');
                }, 1500);
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                addUserMessage(message);
                input.value = '';
                input.style.height = 'auto';
                
                // 显示小肥正在输入
                showTyping();
                
                // 检查是否有预设回答
                const response = responses[message];
                if (response) {
                    setTimeout(() => {
                        hideTyping();
                        addBotMessage(response.text);
                    }, response.delay);
                } else {
                    // 智能回答逻辑
                    setTimeout(() => {
                        hideTyping();
                        generateSmartResponse(message);
                    }, 2000);
                }
            }
        }
        
        function generateSmartResponse(message) {
            let response = '';
            
            if (message.includes('景点') || message.includes('玩') || message.includes('去哪')) {
                response = '合肥有很多不错的景点！推荐你去巢湖风景区看湖光山色，或者到骆岗公园享受绿色生态。还有安徽博物院可以了解历史文化。你比较喜欢哪种类型的景点呢？🏞️';
            } else if (message.includes('美食') || message.includes('吃') || message.includes('餐厅')) {
                response = '说到合肥美食，一定要试试庐州烤鸭！还有李鸿章大杂烩、巢湖银鱼都很有特色。你想吃什么类型的菜呢？我可以推荐具体的餐厅！🍜';
            } else if (message.includes('酒店') || message.includes('住宿') || message.includes('住')) {
                response = '合肥有很多不错的酒店选择！万达文华酒店是豪华型的，服务很好。如果预算有限，也有很多经济型酒店。你的预算大概是多少呢？🏨';
            } else if (message.includes('交通') || message.includes('怎么去') || message.includes('路线')) {
                response = '合肥的交通很便利！地铁、公交都很方便。你想去哪个具体的地方？我可以为你规划最佳路线！🚗';
            } else if (message.includes('天气') || message.includes('气候')) {
                response = '合肥属于亚热带季风气候，四季分明。春秋最适合旅游，夏天比较热，冬天偏冷。建议你根据季节准备合适的衣物！🌤️';
            } else if (message.includes('骆岗公园')) {
                response = '骆岗公园是合肥的城市绿肺！🌳\n\n**特色亮点：**\n• 免费开放的生态公园\n• 适合晨练、散步、亲子活动\n• 空气清新，四季景色各异\n• 交通便利，地铁2号线可达\n\n**最佳游览时间：**\n早上6-9点或傍晚5-7点\n\n需要我提供具体的交通路线吗？';
            } else if (message.includes('巢湖')) {
                response = '巢湖是合肥最美的自然风光！🌊\n\n**游览亮点：**\n• 八百里巢湖烟波浩渺\n• 春季樱花盛开，秋季芦苇飞舞\n• 可以环湖骑行或乘船游览\n• 周边有温泉度假村\n\n**推荐活动：**\n• 湖边摄影\n• 环湖自行车\n• 湖鲜美食品尝\n\n想了解具体的游览路线吗？';
            } else if (message.includes('你好') || message.includes('hi') || message.includes('hello')) {
                response = '你好！很高兴见到你！😊 我是小肥，你的专属合肥旅游助手。有什么关于合肥旅游的问题都可以问我哦！';
            } else if (message.includes('谢谢') || message.includes('感谢')) {
                response = '不客气！能帮到你我很开心！😊 还有其他问题随时问我，祝你在合肥玩得愉快！';
            } else {
                response = '谢谢你的问题！我会努力为你提供帮助。你可以问我关于合肥的景点、美食、交通、住宿等任何问题，我都很乐意为你解答！😊\n\n💡 **常见问题：**\n• 合肥有哪些必去景点？\n• 推荐一些特色美食\n• 帮我规划旅游行程\n• 某个景点怎么去？';
            }
            
            addBotMessage(response);
        }
        
        function addUserMessage(message) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 justify-end message-slide-in';
            
            messageDiv.innerHTML = `
                <div class="flex-1 max-w-xs">
                    <div class="bg-primary text-white rounded-2xl rounded-tr-md p-4">
                        <p class="text-sm leading-relaxed">${message}</p>
                    </div>
                    <div class="text-xs text-gray-400 mt-1 mr-2 text-right">刚刚</div>
                </div>
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            `;
            
            container.appendChild(messageDiv);
            scrollToBottom();
        }
        
        function addBotMessage(message) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 message-slide-in';
            
            // 处理换行和格式
            const formattedMessage = message.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            
            messageDiv.innerHTML = `
                <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <div class="w-6 h-6 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-1.5 left-1 w-1 h-1 bg-black rounded-full"></div>
                        <div class="absolute top-1.5 right-1 w-1 h-1 bg-black rounded-full"></div>
                        <div class="absolute bottom-1.5 left-1/2 transform -translate-x-1/2 w-2 h-1 border border-black border-t-0 rounded-b-full"></div>
                    </div>
                </div>
                <div class="flex-1 max-w-sm">
                    <div class="bg-white rounded-2xl rounded-tl-md p-4 shadow-sm border border-gray-100">
                        <p class="text-gray-800 text-sm leading-relaxed">${formattedMessage}</p>
                    </div>
                    <div class="text-xs text-gray-400 mt-1 ml-2">刚刚</div>
                </div>
            `;
            
            container.appendChild(messageDiv);
            scrollToBottom();
        }
        
        function showTyping() {
            const container = document.getElementById('chatContainer');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typingIndicator';
            typingDiv.className = 'flex items-start space-x-3';
            
            typingDiv.innerHTML = `
                <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <div class="w-6 h-6 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-1.5 left-1 w-1 h-1 bg-black rounded-full"></div>
                        <div class="absolute top-1.5 right-1 w-1 h-1 bg-black rounded-full"></div>
                        <div class="absolute bottom-1.5 left-1/2 transform -translate-x-1/2 w-2 h-1 border border-black border-t-0 rounded-b-full"></div>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="bg-white rounded-2xl rounded-tl-md p-4 shadow-sm border border-gray-100">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-gray-400 rounded-full typing-animation"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full typing-animation" style="animation-delay: 0.2s;"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full typing-animation" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1 ml-2">正在输入...</div>
                </div>
            `;
            
            container.appendChild(typingDiv);
            scrollToBottom();
        }
        
        function hideTyping() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }
        
        function scrollToBottom() {
            setTimeout(() => {
                window.scrollTo(0, document.body.scrollHeight);
            }, 100);
        }
        
        function clearChat() {
            if (confirm('确定要清空对话记录吗？')) {
                const container = document.getElementById('chatContainer');
                // 保留欢迎消息
                const welcomeMessage = container.firstElementChild;
                container.innerHTML = '';
                container.appendChild(welcomeMessage);
                messageCount = 1;
            }
        }
        
        function voiceInput() {
            alert('语音输入功能开发中...');
        }

        function openTripPlanning() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-行程规划.html';
            }, 300);
        }
        
        // 输入框自动调整高度
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
