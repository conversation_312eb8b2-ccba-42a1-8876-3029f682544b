<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物联网知识库 - 专业技术知识平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e3a8a',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light text-gray-900 font-sans overflow-x-hidden">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex justify-between items-center h-16">
                <!-- Logo区域 -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <span class="text-xl font-bold text-dark">物联网知识库</span>
                </div>
                
                <!-- 主导航菜单 -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-primary font-medium border-b-2 border-primary pb-1">首页</a>
                    <a href="knowledge.html" class="text-gray-600 hover:text-primary transition-colors">知识库</a>
                    <a href="news.html" class="text-gray-600 hover:text-primary transition-colors">动态资讯</a>
                    <a href="faq.html" class="text-gray-600 hover:text-primary transition-colors">常见问题</a>
                    <a href="resources.html" class="text-gray-600 hover:text-primary transition-colors">资源汇总</a>
                    <a href="about.html" class="text-gray-600 hover:text-primary transition-colors">关于我们</a>
                </div>
                
                <!-- 登录注册按钮 -->
                <div class="flex items-center space-x-4">
                    <button class="text-gray-600 hover:text-primary transition-colors">登录</button>
                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors">注册</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Banner区域 -->
    <section class="relative bg-gradient-to-br from-primary via-blue-700 to-secondary min-h-screen flex items-center">
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="absolute inset-0" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'); background-size: cover; background-position: center; opacity: 0.1;"></div>
        
        <div class="relative max-w-7xl mx-auto px-6 text-center text-white">
            <h1 class="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                物联网技术
                <span class="block text-accent">知识宝库</span>
            </h1>
            <p class="text-xl md:text-2xl mb-12 text-blue-100 max-w-3xl mx-auto leading-relaxed">
                汇聚全球物联网前沿技术，为开发者和技术专家提供系统化、专业化的知识服务平台
            </p>
            
            <!-- 搜索框 -->
            <div class="max-w-2xl mx-auto mb-16">
                <div class="relative">
                    <input type="text" placeholder="搜索技术文档、案例、教程..." 
                           class="w-full px-6 py-4 text-lg text-gray-900 bg-white rounded-full shadow-lg focus:outline-none focus:ring-4 focus:ring-blue-300 pr-16">
                    <button class="absolute right-2 top-2 bg-primary text-white p-2 rounded-full hover:bg-blue-800 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 快速入口 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 hover:bg-opacity-20 transition-all cursor-pointer">
                    <div class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">技术文档</h3>
                    <p class="text-blue-100 text-sm">深入的技术指南和API文档</p>
                </div>
                
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 hover:bg-opacity-20 transition-all cursor-pointer">
                    <div class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">实战案例</h3>
                    <p class="text-blue-100 text-sm">真实项目案例和最佳实践</p>
                </div>
                
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 hover:bg-opacity-20 transition-all cursor-pointer">
                    <div class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">视频教程</h3>
                    <p class="text-blue-100 text-sm">专业讲师录制的系列课程</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 物联知识库展示区 -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-dark mb-4">物联知识库</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">系统化整合物联网全领域知识，为技术人员提供专业、全面的学习资源</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="group hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden border border-gray-100">
                    <div class="h-48 bg-gradient-to-br from-blue-500 to-blue-600 relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                             alt="电信网络" class="w-full h-full object-cover opacity-80">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4">
                            <h3 class="text-xl font-bold text-white">电信网络知识</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">涵盖基站、电缆、光纤等基础设施，内容复杂且广泛，为网络工程师提供全面的技术指导。</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">1,234 篇文档</span>
                            <button class="text-primary hover:text-blue-800 font-medium">探索更多 →</button>
                        </div>
                    </div>
                </div>
                
                <div class="group hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden border border-gray-100">
                    <div class="h-48 bg-gradient-to-br from-green-500 to-green-600 relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                             alt="物联网技术" class="w-full h-full object-cover opacity-80">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4">
                            <h3 class="text-xl font-bold text-white">物联网核心技术</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">传感器技术、通信协议、数据处理等核心技术详解，助力开发者掌握物联网开发精髓。</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">856 篇文档</span>
                            <button class="text-primary hover:text-blue-800 font-medium">探索更多 →</button>
                        </div>
                    </div>
                </div>
                
                <div class="group hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden border border-gray-100">
                    <div class="h-48 bg-gradient-to-br from-purple-500 to-purple-600 relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                             alt="开发实践" class="w-full h-full object-cover opacity-80">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4">
                            <h3 class="text-xl font-bold text-white">开发实践指南</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">从项目规划到部署运维，提供完整的开发流程指导和最佳实践案例分享。</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">672 篇文档</span>
                            <button class="text-primary hover:text-blue-800 font-medium">探索更多 →</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 亮点功能区 -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-dark mb-4">平台亮点功能</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">基于AI技术的智能化学习平台，为用户提供个性化的知识服务体验</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">更全面的知识</h3>
                    <p class="text-gray-600 leading-relaxed">系统化整合电信全领域知识，定期更新最新技术动态，确保内容的前沿性和实用性。</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">更智能的推荐</h3>
                    <p class="text-gray-600 leading-relaxed">基于用户行为和学习偏好，智能推送相关技术文档与案例，提升学习效率。</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">更方便的学习</h3>
                    <p class="text-gray-600 leading-relaxed">支持移动端访问、离线下载及知识检索，随时随地获取所需的技术知识。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 学习标兵展示 -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-dark mb-4">学习标兵</h2>
                <p class="text-xl text-gray-600">优秀学员的学习心得与成长经历分享</p>
            </div>

            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-12">
                <div class="flex flex-col md:flex-row items-center gap-12">
                    <div class="flex-shrink-0">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                             alt="学习标兵" class="w-32 h-32 rounded-full object-cover shadow-lg">
                    </div>
                    <div class="flex-1 text-center md:text-left">
                        <blockquote class="text-xl md:text-2xl text-gray-700 mb-6 leading-relaxed italic">
                            "通过物联网知识库，我学到了很多实用的技术知识，比如基站、电缆、光纤等网络基础设施的原理和应用。这些知识非常复杂但极其实用，对我的工作帮助极大。"
                        </blockquote>
                        <div class="flex flex-col md:flex-row md:items-center gap-4">
                            <div>
                                <p class="font-bold text-lg text-dark">张工程师</p>
                                <p class="text-gray-600">网络架构部 · 2022年入职</p>
                            </div>
                            <div class="flex items-center gap-2 text-yellow-500">
                                <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <span class="text-sm text-gray-600">优秀学员</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 其他资源区 -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-dark mb-4">其他资源</h2>
                <p class="text-xl text-gray-600">丰富的学习资源，助力技术能力全面提升</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">行业报告</h3>
                    <p class="text-gray-600 mb-6">最新的行业分析报告和技术趋势白皮书，把握行业发展脉搏。</p>
                    <button class="text-primary hover:text-blue-800 font-medium">查看更多 →</button>
                </div>

                <div class="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">标准文档</h3>
                    <p class="text-gray-600 mb-6">国际标准和行业规范文档，确保技术实施的标准化和规范化。</p>
                    <button class="text-primary hover:text-blue-800 font-medium">查看更多 →</button>
                </div>

                <div class="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">培训视频</h3>
                    <p class="text-gray-600 mb-6">专业讲师录制的系列培训视频，深入浅出讲解复杂技术概念。</p>
                    <button class="text-primary hover:text-blue-800 font-medium">查看更多 →</button>
                </div>
            </div>
        </div>
    </section>

    <!-- 最新动态 -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-4xl font-bold text-dark mb-4">最新动态</h2>
                    <p class="text-xl text-gray-600">关注平台最新更新和技术动态</p>
                </div>
                <button class="text-primary hover:text-blue-800 font-medium">查看全部 →</button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <article class="group cursor-pointer">
                    <div class="bg-gray-100 rounded-xl h-48 mb-4 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                             alt="知识库更新" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    <div class="space-y-2">
                        <span class="text-sm text-primary font-medium">知识库更新</span>
                        <h3 class="text-lg font-bold text-dark group-hover:text-primary transition-colors">5G网络架构深度解析文档上线</h3>
                        <p class="text-gray-600 text-sm">详细介绍5G网络的核心架构、关键技术和部署策略，为网络工程师提供全面的技术指导。</p>
                        <p class="text-xs text-gray-500">2024年1月15日</p>
                    </div>
                </article>

                <article class="group cursor-pointer">
                    <div class="bg-gray-100 rounded-xl h-48 mb-4 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                             alt="技术白皮书" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    <div class="space-y-2">
                        <span class="text-sm text-green-600 font-medium">技术白皮书</span>
                        <h3 class="text-lg font-bold text-dark group-hover:text-primary transition-colors">物联网安全技术发展趋势报告</h3>
                        <p class="text-gray-600 text-sm">分析当前物联网安全面临的挑战，探讨未来安全技术的发展方向和解决方案。</p>
                        <p class="text-xs text-gray-500">2024年1月12日</p>
                    </div>
                </article>

                <article class="group cursor-pointer">
                    <div class="bg-gray-100 rounded-xl h-48 mb-4 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                             alt="案例库更新" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    <div class="space-y-2">
                        <span class="text-sm text-purple-600 font-medium">案例库更新</span>
                        <h3 class="text-lg font-bold text-dark group-hover:text-primary transition-colors">智慧城市物联网部署实战案例</h3>
                        <p class="text-gray-600 text-sm">分享某一线城市智慧交通系统的完整部署过程，包括技术选型、实施方案和运维经验。</p>
                        <p class="text-xs text-gray-500">2024年1月10日</p>
                    </div>
                </article>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-16">
        <div class="max-w-7xl mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <span class="text-xl font-bold">物联网知识库</span>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">专业的物联网技术知识平台，为技术人员、开发者及行业从业者提供全面的知识服务。</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">知识库</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">技术文档</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">实战案例</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">视频教程</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">联系我们</h3>
                    <ul class="space-y-2">
                        <li class="text-gray-300">邮箱：<EMAIL></li>
                        <li class="text-gray-300">电话：400-123-4567</li>
                        <li class="text-gray-300">地址：北京市朝阳区科技园</li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-12 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 物联网知识库. 保留所有权利.</p>
            </div>
        </div>
    </footer>
</body>
</html>
