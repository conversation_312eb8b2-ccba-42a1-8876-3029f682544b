<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 探索</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 标签选中状态 */
        .tab-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">探索合肥</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🔍</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📍</span>
                    </button>
                </div>
            </div>
            
            <!-- 分类标签 -->
            <div class="flex space-x-3">
                <button class="tab-active px-4 py-2 rounded-full text-sm font-medium" onclick="switchTab('attractions')">
                    景点
                </button>
                <button class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm font-medium" onclick="switchTab('activities')">
                    活动
                </button>
                <button class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm font-medium" onclick="switchTab('food')">
                    美食
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 筛选条件 -->
            <div class="px-4 -mt-4 mb-4">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">筛选条件</span>
                        <button class="text-xs text-blue-500">重置</button>
                    </div>
                    
                    <!-- 距离筛选 -->
                    <div class="mb-3">
                        <div class="text-xs text-gray-600 mb-2">距离</div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-xs">5km内</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">10km内</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">20km内</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">不限</button>
                        </div>
                    </div>
                    
                    <!-- 类型筛选 -->
                    <div>
                        <div class="text-xs text-gray-600 mb-2">类型</div>
                        <div class="flex space-x-2 hide-scrollbar overflow-x-auto">
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs whitespace-nowrap">历史文化</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs whitespace-nowrap">自然风光</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs whitespace-nowrap">主题乐园</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs whitespace-nowrap">科技馆</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 景点列表 -->
            <div id="attractions-content" class="px-4 mb-20">
                <div class="flex items-center justify-between mb-4">
                    <span class="text-sm text-gray-600">共找到 <span class="text-blue-500 font-medium">24</span> 个景点</span>
                    <button class="flex items-center space-x-1 text-sm text-gray-600">
                        <span>距离排序</span>
                        <span class="text-xs">⇅</span>
                    </button>
                </div>
                
                <!-- 景点卡片列表 -->
                <div class="space-y-4">
                    <!-- 景点1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=120&h=120&fit=crop" 
                                 alt="包公园" 
                                 class="w-24 h-24 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="font-medium text-gray-800">包公园</h3>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-orange-400 text-xs">⭐</span>
                                        <span class="text-xs text-gray-600">4.8</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2">感受包拯文化的历史底蕴，园内有包公祠、包公墓等景点</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                        <span>📍 2.3km</span>
                                        <span>🎫 免费</span>
                                        <span>⏰ 8:00-18:00</span>
                                    </div>
                                    <button class="text-blue-500 text-xs">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 景点2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=120&h=120&fit=crop" 
                                 alt="三河古镇" 
                                 class="w-24 h-24 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="font-medium text-gray-800">三河古镇</h3>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-orange-400 text-xs">⭐</span>
                                        <span class="text-xs text-gray-600">4.6</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2">江南水乡风情，历史文化名镇，古建筑群保存完好</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                        <span>📍 45km</span>
                                        <span>🎫 ¥80</span>
                                        <span>⏰ 8:30-17:30</span>
                                    </div>
                                    <button class="text-blue-500 text-xs">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 景点3 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=120&fit=crop" 
                                 alt="巢湖" 
                                 class="w-24 h-24 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="font-medium text-gray-800">巢湖风景区</h3>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-orange-400 text-xs">⭐</span>
                                        <span class="text-xs text-gray-600">4.4</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2">中国五大淡水湖之一，湖光山色，自然风光秀美</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                        <span>📍 35km</span>
                                        <span>🎫 免费</span>
                                        <span>⏰ 全天开放</span>
                                    </div>
                                    <button class="text-blue-500 text-xs">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 景点4 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=120&h=120&fit=crop" 
                                 alt="科技馆" 
                                 class="w-24 h-24 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="font-medium text-gray-800">安徽省科技馆</h3>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-orange-400 text-xs">⭐</span>
                                        <span class="text-xs text-gray-600">4.7</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2">现代科技展示，互动体验丰富，适合亲子游览</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                        <span>📍 8.5km</span>
                                        <span>🎫 ¥30</span>
                                        <span>⏰ 9:00-17:00</span>
                                    </div>
                                    <button class="text-blue-500 text-xs">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 景点5 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=120&h=120&fit=crop" 
                                 alt="植物园" 
                                 class="w-24 h-24 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="font-medium text-gray-800">合肥植物园</h3>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-orange-400 text-xs">⭐</span>
                                        <span class="text-xs text-gray-600">4.5</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2">四季花卉展示，春季樱花盛开，是赏花的好去处</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                        <span>📍 12km</span>
                                        <span>🎫 ¥20</span>
                                        <span>⏰ 6:00-18:00</span>
                                    </div>
                                    <button class="text-blue-500 text-xs">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 加载更多 -->
                <div class="text-center mt-6">
                    <button class="text-blue-500 text-sm">加载更多景点</button>
                </div>
            </div>

            <!-- 活动列表（隐藏） -->
            <div id="activities-content" class="px-4 mb-20 hidden">
                <div class="flex items-center justify-between mb-4">
                    <span class="text-sm text-gray-600">共找到 <span class="text-blue-500 font-medium">8</span> 个活动</span>
                    <button class="flex items-center space-x-1 text-sm text-gray-600">
                        <span>时间排序</span>
                        <span class="text-xs">⇅</span>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <!-- 活动1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=150&fit=crop" 
                             alt="樱花节" 
                             class="w-full h-32 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h3 class="font-medium text-gray-800">合肥樱花节</h3>
                                <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">进行中</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">春暖花开，樱花盛放，邀您共赏春日美景</p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <div class="space-y-1">
                                    <div>📅 4月1日-4月30日</div>
                                    <div>📍 合肥植物园</div>
                                </div>
                                <button class="text-blue-500">详情</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 活动2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1414016642750-7fdd78dc33d9?w=400&h=150&fit=crop" 
                             alt="美食节" 
                             class="w-full h-32 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h3 class="font-medium text-gray-800">美食文化节</h3>
                                <span class="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full">即将开始</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">品味合肥特色美食，体验地道文化</p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <div class="space-y-1">
                                    <div>📅 5月15日-5月20日</div>
                                    <div>📍 淮河路步行街</div>
                                </div>
                                <button class="text-blue-500">详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 美食列表（隐藏） -->
            <div id="food-content" class="px-4 mb-20 hidden">
                <div class="flex items-center justify-between mb-4">
                    <span class="text-sm text-gray-600">共找到 <span class="text-blue-500 font-medium">36</span> 家美食</span>
                    <button class="flex items-center space-x-1 text-sm text-gray-600">
                        <span>评分排序</span>
                        <span class="text-xs">⇅</span>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <!-- 美食1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=120&h=120&fit=crop" 
                                 alt="庐州烤鸭" 
                                 class="w-24 h-24 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="font-medium text-gray-800">庐州烤鸭</h3>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-orange-400 text-xs">⭐</span>
                                        <span class="text-xs text-gray-600">4.8</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2">合肥特色美食，皮脆肉嫩，香味浓郁</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                        <span>🏷️ 徽菜</span>
                                        <span>💰 ¥80-120</span>
                                        <span>📍 1.2km</span>
                                    </div>
                                    <button class="text-blue-500 text-xs">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 美食2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1551218808-94e220e084d2?w=120&h=120&fit=crop" 
                                 alt="小笼包" 
                                 class="w-24 h-24 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="font-medium text-gray-800">老合肥小笼包</h3>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-orange-400 text-xs">⭐</span>
                                        <span class="text-xs text-gray-600">4.6</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2">传统手工制作，汤汁鲜美，皮薄馅大</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                                        <span>🏷️ 小吃</span>
                                        <span>💰 ¥15-30</span>
                                        <span>📍 0.8km</span>
                                    </div>
                                    <button class="text-blue-500 text-xs">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-blue-500 text-xl mb-1">🔍</span>
                    <span class="text-xs text-blue-500 font-medium">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };

            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }

        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，返回首页
                window.location.href = '首页.html';
            }
        }

        // 标签切换功能
        function switchTab(tabName) {
            // 隐藏所有内容
            document.getElementById('attractions-content').classList.add('hidden');
            document.getElementById('activities-content').classList.add('hidden');
            document.getElementById('food-content').classList.add('hidden');

            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.remove('hidden');

            // 更新标签样式
            const tabs = document.querySelectorAll('.gradient-bg button');
            tabs.forEach(tab => {
                tab.classList.remove('tab-active');
                tab.classList.add('bg-white', 'bg-opacity-20');
            });

            // 设置当前标签为选中状态
            event.target.classList.add('tab-active');
            event.target.classList.remove('bg-white', 'bg-opacity-20');
        }

        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
