<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 骆岗公园</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">骆岗公园</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">❤️</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📤</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 景点头图 -->
            <div class="relative h-64 -mt-4">
                <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop&crop=center" 
                     alt="骆岗公园" 
                     class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-black bg-opacity-30"></div>
                <div class="absolute bottom-4 left-4 text-white">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">免费</span>
                        <span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs">4.9⭐</span>
                        <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs">新开放</span>
                    </div>
                    <h2 class="text-2xl font-bold mb-1">骆岗公园</h2>
                    <p class="text-sm opacity-90">全球最大城市中央公园</p>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="px-4 py-6">
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">📍 基本信息</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-2xl mb-1">🎫</div>
                            <div class="text-sm font-medium text-gray-800">门票价格</div>
                            <div class="text-xs text-gray-600">免费开放</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">⏰</div>
                            <div class="text-sm font-medium text-gray-800">开放时间</div>
                            <div class="text-xs text-gray-600">6:00-22:00</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">🚇</div>
                            <div class="text-sm font-medium text-gray-800">交通方式</div>
                            <div class="text-xs text-gray-600">地铁5号线</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">📏</div>
                            <div class="text-sm font-medium text-gray-800">公园面积</div>
                            <div class="text-xs text-gray-600">1347公顷</div>
                        </div>
                    </div>
                </div>

                <!-- 景点介绍 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">📖 景点介绍</h3>
                    <div class="text-sm text-gray-700 leading-relaxed space-y-3">
                        <p>骆岗公园位于合肥市包河区，总面积1347公顷，是全球最大的城市中央公园。公园在原骆岗机场基础上改建而成，保留了机场跑道等历史元素。</p>
                        
                        <p>公园以"生态优先、文化传承、科技创新"为设计理念，打造集生态保护、文化展示、科普教育、休闲娱乐于一体的综合性城市公园。</p>
                        
                        <p>园内设有湿地保护区、科技体验区、文化展示区、运动健身区等多个功能区域，是合肥市民休闲娱乐和生态体验的新地标。</p>
                    </div>
                </div>

                <!-- 主要景点 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">🌳 主要景点</h3>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">生态湿地区</div>
                                <div class="text-sm text-gray-600">原生态湿地保护与观鸟</div>
                            </div>
                            <button class="text-green-500 text-sm">详情</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">科技体验馆</div>
                                <div class="text-sm text-gray-600">互动科技展示与体验</div>
                            </div>
                            <button class="text-green-500 text-sm">详情</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">历史跑道</div>
                                <div class="text-sm text-gray-600">保留的机场跑道历史遗迹</div>
                            </div>
                            <button class="text-green-500 text-sm">详情</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">运动健身区</div>
                                <div class="text-sm text-gray-600">多样化运动设施</div>
                            </div>
                            <button class="text-green-500 text-sm">详情</button>
                        </div>
                    </div>
                </div>

                <!-- 特色亮点 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">✨ 特色亮点</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-green-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">🌍</div>
                            <div class="text-sm font-medium text-green-800">全球最大</div>
                            <div class="text-xs text-green-600">城市中央公园</div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">🛫</div>
                            <div class="text-sm font-medium text-blue-800">机场改建</div>
                            <div class="text-xs text-blue-600">历史文化传承</div>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">🔬</div>
                            <div class="text-sm font-medium text-purple-800">科技体验</div>
                            <div class="text-xs text-purple-600">创新科普教育</div>
                        </div>
                        <div class="bg-orange-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">🦋</div>
                            <div class="text-sm font-medium text-orange-800">生态保护</div>
                            <div class="text-xs text-orange-600">湿地生物多样性</div>
                        </div>
                    </div>
                </div>

                <!-- 游览建议 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">💡 游览建议</h3>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs">1</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">最佳游览时间</div>
                                <div class="text-xs text-gray-600">春秋两季，早晨或傍晚时分最佳</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-blue-600 text-xs">2</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">建议游览时长</div>
                                <div class="text-xs text-gray-600">半天至一天，可深度体验各功能区</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-purple-600 text-xs">3</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">推荐路线</div>
                                <div class="text-xs text-gray-600">入口→湿地区→科技馆→历史跑道→运动区</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-orange-600 text-xs">4</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">注意事项</div>
                                <div class="text-xs text-gray-600">园区较大，建议穿舒适鞋子，携带水和防晒用品</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户评价 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-bold text-gray-800">⭐ 用户评价</h3>
                        <div class="flex items-center space-x-1">
                            <span class="text-yellow-500">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600">4.9分</span>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="border-l-4 border-green-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">自然爱好者</span>
                                <span class="text-xs text-gray-500">2天前</span>
                            </div>
                            <p class="text-sm text-gray-700">骆岗公园真的太震撼了！作为全球最大的城市中央公园，生态环境保护得非常好，湿地区域鸟类丰富。</p>
                        </div>
                        
                        <div class="border-l-4 border-blue-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">科技达人</span>
                                <span class="text-xs text-gray-500">5天前</span>
                            </div>
                            <p class="text-sm text-gray-700">科技体验馆的互动设施很棒，孩子们玩得很开心。历史跑道的保留也很有意义，见证了合肥的发展历程。</p>
                        </div>
                        
                        <div class="border-l-4 border-purple-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">运动健身爱好者</span>
                                <span class="text-xs text-gray-500">1周前</span>
                            </div>
                            <p class="text-sm text-gray-700">运动设施很齐全，跑道设计合理，空气清新。早晨来这里晨跑是一种享受！</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="px-4 pb-20">
                    <div class="grid grid-cols-2 gap-3">
                        <button class="bg-green-500 text-white py-3 rounded-xl font-medium card-hover">
                            🗺️ 语音导览
                        </button>
                        <button class="bg-blue-500 text-white py-3 rounded-xl font-medium card-hover">
                            📅 加入行程
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3 mt-3">
                        <button class="bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                            📍 查看地图
                        </button>
                        <button class="bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                            🚌 交通路线
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
