<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 游记生成</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 上传区域样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #f97316;
            background-color: #fff7ed;
        }
        .upload-area.dragover {
            border-color: #f97316;
            background-color: #fff7ed;
        }
        
        /* 风格选择按钮 */
        .style-btn {
            transition: all 0.3s ease;
        }
        .style-btn.active {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
            border-color: #f97316;
        }
        
        /* 打字机效果 */
        .typewriter {
            overflow: hidden;
            border-right: 2px solid #f97316;
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }
        
        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }
        
        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #f97316; }
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">游记生成</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📝</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">💾</span>
                    </button>
                </div>
            </div>
            <p class="text-sm opacity-90">AI帮您生成精美游记，记录美好时光</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 创建游记表单 -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl p-6 shadow-sm">
                    <h3 class="text-lg font-bold text-gray-800 mb-6">✨ 创建游记</h3>
                    
                    <div class="space-y-6">
                        <!-- 游记标题 -->
                        <div>
                            <label class="text-sm font-medium text-gray-700 block mb-3">📝 游记标题</label>
                            <input type="text" 
                                   id="travelTitle"
                                   class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" 
                                   placeholder="输入您的游记标题...">
                        </div>
                        
                        <!-- 选择照片 -->
                        <div>
                            <label class="text-sm font-medium text-gray-700 block mb-3">📸 选择照片</label>
                            <div class="upload-area rounded-lg p-6 text-center" id="uploadArea">
                                <span class="text-4xl mb-3 block">📸</span>
                                <p class="text-sm text-gray-600 mb-3">点击上传照片或拖拽到此处</p>
                                <p class="text-xs text-gray-500">支持JPG、PNG格式，最多上传9张照片</p>
                                <input type="file" id="photoInput" accept="image/*" multiple class="hidden">
                            </div>
                            
                            <!-- 已选择的照片 -->
                            <div id="selectedPhotos" class="grid grid-cols-3 gap-2 mt-4 hidden">
                                <!-- 照片预览将在这里显示 -->
                            </div>
                        </div>
                        
                        <!-- 游记风格 -->
                        <div>
                            <label class="text-sm font-medium text-gray-700 block mb-3">🎨 游记风格</label>
                            <div class="grid grid-cols-2 gap-3">
                                <button class="style-btn p-3 border border-gray-200 bg-gray-50 text-gray-600 rounded-lg text-sm active" data-style="文艺清新">
                                    🌸 文艺清新
                                </button>
                                <button class="style-btn p-3 border border-gray-200 bg-gray-50 text-gray-600 rounded-lg text-sm" data-style="详细攻略">
                                    📋 详细攻略
                                </button>
                                <button class="style-btn p-3 border border-gray-200 bg-gray-50 text-gray-600 rounded-lg text-sm" data-style="简约日记">
                                    📖 简约日记
                                </button>
                                <button class="style-btn p-3 border border-gray-200 bg-gray-50 text-gray-600 rounded-lg text-sm" data-style="幽默风趣">
                                    😄 幽默风趣
                                </button>
                            </div>
                        </div>
                        
                        <!-- 游记内容提示 -->
                        <div>
                            <label class="text-sm font-medium text-gray-700 block mb-3">💡 内容提示（可选）</label>
                            <textarea id="contentHints"
                                      class="w-full p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" 
                                      rows="3" 
                                      placeholder="描述您的旅行感受、特别的经历或想要重点突出的内容..."></textarea>
                        </div>
                        
                        <!-- 生成按钮 -->
                        <button onclick="generateTravelNote()" class="w-full bg-orange-500 text-white py-3 rounded-xl font-medium card-hover">
                            ✨ AI生成游记
                        </button>
                    </div>
                </div>
            </div>

            <!-- 生成结果区域 -->
            <div id="resultArea" class="px-4 mb-6 hidden">
                <div class="bg-white rounded-2xl p-6 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">📖 生成的游记</h4>
                        <div class="flex space-x-2">
                            <button onclick="regenerateNote()" class="text-sm text-orange-500">重新生成</button>
                            <button onclick="saveNote()" class="text-sm text-blue-500">保存</button>
                        </div>
                    </div>
                    
                    <div id="generatedContent" class="prose prose-sm max-w-none">
                        <!-- 生成的游记内容将在这里显示 -->
                    </div>
                    
                    <div class="flex space-x-3 mt-6">
                        <button onclick="shareNote()" class="flex-1 bg-blue-500 text-white py-2 rounded-lg text-sm">
                            📤 分享游记
                        </button>
                        <button onclick="editNote()" class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm">
                            ✏️ 编辑修改
                        </button>
                    </div>
                </div>
            </div>

            <!-- 游记模板 -->
            <div class="px-4 mb-6">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">📚 游记模板</h4>
                        <button class="text-sm text-orange-500">查看更多</button>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="bg-gray-50 rounded-lg p-4 card-hover" onclick="useTemplate('合肥一日游')">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <span class="text-orange-600">🏛️</span>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-gray-800">合肥一日游模板</div>
                                    <div class="text-sm text-gray-600">包公园→逍遥津→李鸿章故居</div>
                                    <div class="text-xs text-gray-500 mt-1">文艺清新风格 · 已使用 1.2k次</div>
                                </div>
                                <button class="text-orange-500 text-sm">使用</button>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4 card-hover" onclick="useTemplate('古镇探索')">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600">🏘️</span>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-gray-800">古镇探索模板</div>
                                    <div class="text-sm text-gray-600">三河古镇深度游记录</div>
                                    <div class="text-xs text-gray-500 mt-1">详细攻略风格 · 已使用 856次</div>
                                </div>
                                <button class="text-orange-500 text-sm">使用</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的游记 -->
            <div class="px-4 mb-20">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">📝 我的游记</h4>
                        <button class="text-sm text-orange-500">管理</button>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="bg-gray-50 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <div class="font-medium text-gray-800 text-sm">春日合肥行</div>
                                <span class="text-xs text-gray-500">3天前</span>
                            </div>
                            <div class="text-xs text-gray-600 mb-2">包河公园的樱花开得正好，春风拂面...</div>
                            <div class="flex items-center space-x-3 text-xs text-gray-500">
                                <span>👁️ 156</span>
                                <span>❤️ 23</span>
                                <span>💬 8</span>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <div class="font-medium text-gray-800 text-sm">三河古镇漫步</div>
                                <span class="text-xs text-gray-500">1周前</span>
                            </div>
                            <div class="text-xs text-gray-600 mb-2">江南水乡的韵味在这里得到了完美体现...</div>
                            <div class="flex items-center space-x-3 text-xs text-gray-500">
                                <span>👁️ 89</span>
                                <span>❤️ 15</span>
                                <span>💬 5</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedStyle = '文艺清新';
        let selectedPhotos = [];
        
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 生成游记
        function generateTravelNote() {
            const title = document.getElementById('travelTitle').value;
            const hints = document.getElementById('contentHints').value;
            
            if (!title.trim()) {
                alert('请输入游记标题');
                return;
            }
            
            // 显示生成过程
            showGeneratingProcess();
            
            // 模拟AI生成过程
            setTimeout(() => {
                showGeneratedNote(title, selectedStyle, hints);
            }, 3000);
        }
        
        // 显示生成过程
        function showGeneratingProcess() {
            const resultArea = document.getElementById('resultArea');
            const generatedContent = document.getElementById('generatedContent');
            
            generatedContent.innerHTML = `
                <div class="text-center py-8">
                    <div class="animate-spin w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p class="text-sm text-gray-600 mb-2">AI正在为您生成游记...</p>
                    <div class="text-xs text-gray-500">
                        <div class="typewriter">分析照片内容，提取关键信息，生成优美文字...</div>
                    </div>
                </div>
            `;
            
            resultArea.classList.remove('hidden');
        }
        
        // 显示生成的游记
        function showGeneratedNote(title, style, hints) {
            const generatedContent = document.getElementById('generatedContent');
            
            // 模拟生成的游记内容
            const sampleContent = `
                <h2 class="text-xl font-bold text-gray-800 mb-4">${title}</h2>
                
                <div class="mb-6">
                    <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=200&fit=crop" 
                         class="w-full h-48 object-cover rounded-lg mb-3">
                    <p class="text-sm text-gray-600 text-center">包河公园春日美景</p>
                </div>
                
                <div class="space-y-4 text-gray-700">
                    <p>春日的合肥，阳光正好，微风不燥。今天的包河公园之行，让我深深感受到了这座城市的历史底蕴和现代活力。</p>
                    
                    <p>走进包河公园，首先映入眼帘的是那一池碧水，波光粼粼，倒映着蓝天白云。沿着湖边小径慢慢踱步，两旁的柳树已经抽出了嫩绿的新芽，在春风中轻柔地摇摆。</p>
                    
                    <p>包公祠是这里最具历史意义的建筑，青砖黛瓦，古朴庄严。站在包公像前，仿佛能感受到那份"清心为治本，直道是身谋"的正气。这里不仅是一个景点，更是一种精神的传承。</p>
                    
                    <p>园内的樱花正值盛开期，粉色的花瓣如云似霞，引得游人纷纷驻足拍照。我也忍不住在花树下留下了美好的瞬间，这大概就是春天最美的馈赠吧。</p>
                    
                    <p>傍晚时分，夕阳西下，整个公园都被染上了金黄色的光辉。湖面上偶尔有几只水鸟掠过，留下圈圈涟漪。这一刻，时间仿佛静止了，只有内心的宁静和满足。</p>
                    
                    <p>合肥，这座有着深厚历史文化底蕴的城市，总是能在不经意间给人惊喜。今天的包河公园之行，不仅让我欣赏到了美丽的风景，更让我感受到了这座城市的温度和魅力。</p>
                </div>
                
                <div class="mt-6 p-4 bg-orange-50 rounded-lg">
                    <div class="text-sm font-medium text-orange-800 mb-2">📍 游览信息</div>
                    <div class="text-xs text-orange-700 space-y-1">
                        <div>📅 游览时间：2024年4月15日</div>
                        <div>⏰ 建议游览时长：2-3小时</div>
                        <div>🎫 门票价格：免费</div>
                        <div>🚇 交通方式：地铁1号线包公园站</div>
                    </div>
                </div>
            `;
            
            generatedContent.innerHTML = sampleContent;
        }
        
        // 重新生成
        function regenerateNote() {
            const title = document.getElementById('travelTitle').value || '我的合肥之旅';
            showGeneratingProcess();
            setTimeout(() => {
                showGeneratedNote(title, selectedStyle, '');
            }, 2000);
        }
        
        // 保存游记
        function saveNote() {
            alert('游记已保存到草稿箱\n您可以在"我的游记"中查看和管理');
        }
        
        // 分享游记
        function shareNote() {
            alert('选择分享方式：\n• 分享到社区\n• 复制链接\n• 导出为图片');
        }
        
        // 编辑游记
        function editNote() {
            alert('进入编辑模式\n您可以修改游记内容、添加更多照片或调整排版');
        }
        
        // 使用模板
        function useTemplate(templateName) {
            document.getElementById('travelTitle').value = templateName;
            alert(`已应用"${templateName}"模板\n模板内容已填充到表单中`);
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 风格选择
            const styleButtons = document.querySelectorAll('.style-btn');
            styleButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    styleButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedStyle = this.dataset.style;
                });
            });
            
            // 上传区域点击
            document.getElementById('uploadArea').addEventListener('click', function() {
                document.getElementById('photoInput').click();
            });
            
            // 文件选择
            document.getElementById('photoInput').addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    selectedPhotos = files;
                    showSelectedPhotos(files);
                }
            });
            
            // 拖拽上传
            const uploadArea = document.getElementById('uploadArea');
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    selectedPhotos = files;
                    showSelectedPhotos(files);
                }
            });
            
            // 按钮交互效果
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
        
        // 显示选择的照片
        function showSelectedPhotos(files) {
            const container = document.getElementById('selectedPhotos');
            container.innerHTML = '';
            
            files.slice(0, 9).forEach((file, index) => {
                const div = document.createElement('div');
                div.className = 'relative';
                div.innerHTML = `
                    <img src="https://images.unsplash.com/photo-157189634984${index}?w=100&h=100&fit=crop" 
                         class="w-full h-20 object-cover rounded-lg">
                    <button onclick="removePhoto(${index})" 
                            class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs">×</button>
                `;
                container.appendChild(div);
            });
            
            container.classList.remove('hidden');
        }
        
        // 移除照片
        function removePhoto(index) {
            selectedPhotos.splice(index, 1);
            if (selectedPhotos.length > 0) {
                showSelectedPhotos(selectedPhotos);
            } else {
                document.getElementById('selectedPhotos').classList.add('hidden');
            }
        }
    </script>
</body>
</html>
