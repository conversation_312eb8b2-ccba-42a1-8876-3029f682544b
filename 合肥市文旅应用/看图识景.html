<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 看图识景</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 上传区域样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
        .upload-area.dragover {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">看图识景</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📷</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">❓</span>
                    </button>
                </div>
            </div>
            <p class="text-sm opacity-90">拍照或上传图片，AI帮您识别景点信息</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 上传区域 -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl p-6 shadow-sm">
                    <div class="upload-area rounded-2xl p-8 text-center mb-6" id="uploadArea">
                        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-4xl">📷</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">上传景点照片</h3>
                        <p class="text-sm text-gray-600 mb-6">支持JPG、PNG格式，文件大小不超过10MB</p>
                        
                        <div class="space-y-3">
                            <button onclick="takePhoto()" class="w-full bg-green-500 text-white py-3 rounded-xl font-medium card-hover">
                                📸 拍照识别
                            </button>
                            <button onclick="selectFromGallery()" class="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                                🖼️ 从相册选择
                            </button>
                        </div>
                        
                        <input type="file" id="fileInput" accept="image/*" class="hidden" onchange="handleFileSelect(event)">
                    </div>
                    
                    <!-- 识别结果区域 -->
                    <div id="resultArea" class="hidden">
                        <div class="border-t border-gray-200 pt-6">
                            <h4 class="font-medium text-gray-800 mb-4">🎯 识别结果</h4>
                            <div id="resultContent" class="space-y-4">
                                <!-- 识别结果将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门识别 -->
            <div class="px-4 mb-6">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">🔥 热门识别</h4>
                        <button class="text-sm text-green-500">查看更多</button>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div class="text-center card-hover" onclick="recognizeExample('包河公园')">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=150&h=100&fit=crop" 
                                 class="w-full h-20 object-cover rounded-lg mb-2">
                            <span class="text-xs text-gray-600">包河公园</span>
                        </div>
                        <div class="text-center card-hover" onclick="recognizeExample('巢湖风景')">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=150&h=100&fit=crop" 
                                 class="w-full h-20 object-cover rounded-lg mb-2">
                            <span class="text-xs text-gray-600">巢湖风景</span>
                        </div>
                        <div class="text-center card-hover" onclick="recognizeExample('三河古镇')">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=100&fit=crop" 
                                 class="w-full h-20 object-cover rounded-lg mb-2">
                            <span class="text-xs text-gray-600">三河古镇</span>
                        </div>
                        <div class="text-center card-hover" onclick="recognizeExample('科技馆')">
                            <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=150&h=100&fit=crop" 
                                 class="w-full h-20 object-cover rounded-lg mb-2">
                            <span class="text-xs text-gray-600">科技馆</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用技巧 -->
            <div class="px-4 mb-20">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <h4 class="font-medium text-gray-800 mb-4">💡 使用技巧</h4>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs">1</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">拍摄清晰照片</div>
                                <div class="text-xs text-gray-600">确保景点主体清晰，光线充足</div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs">2</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">包含标志性建筑</div>
                                <div class="text-xs text-gray-600">拍摄景点的标志性建筑或特色元素</div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs">3</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">避免遮挡</div>
                                <div class="text-xs text-gray-600">尽量避免人物或其他物体遮挡主要景点</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 拍照功能
        function takePhoto() {
            // 模拟拍照功能
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                // 实际项目中可以调用摄像头
                alert('正在启动摄像头...\n实际项目中会调用设备摄像头进行拍照');
            } else {
                alert('您的设备不支持摄像头功能，请选择从相册上传');
            }
        }
        
        // 从相册选择
        function selectFromGallery() {
            document.getElementById('fileInput').click();
        }
        
        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                // 显示识别过程
                showRecognitionProcess(file.name);
            }
        }
        
        // 示例识别
        function recognizeExample(sceneName) {
            showRecognitionProcess(sceneName);
        }
        
        // 显示识别过程
        function showRecognitionProcess(imageName) {
            const resultArea = document.getElementById('resultArea');
            const resultContent = document.getElementById('resultContent');
            
            // 显示识别中状态
            resultContent.innerHTML = `
                <div class="text-center py-8">
                    <div class="animate-spin w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p class="text-sm text-gray-600">AI正在识别图片中的景点...</p>
                </div>
            `;
            resultArea.classList.remove('hidden');
            
            // 模拟识别过程
            setTimeout(() => {
                showRecognitionResult(imageName);
            }, 2000);
        }
        
        // 显示识别结果
        function showRecognitionResult(sceneName) {
            const resultContent = document.getElementById('resultContent');
            
            // 模拟识别结果
            const results = {
                '包河公园': {
                    name: '包河公园',
                    confidence: '95%',
                    description: '位于合肥市包河区的大型城市公园，以包拯文化为主题，是合肥市的重要文化景点。',
                    tags: ['历史文化', '城市公园', '包拯文化'],
                    rating: '4.8',
                    tips: '建议游览时间2-3小时，春秋季节最佳'
                },
                '巢湖风景': {
                    name: '巢湖风景区',
                    confidence: '92%',
                    description: '中国五大淡水湖之一，湖光山色秀美，是合肥周边重要的自然风景区。',
                    tags: ['自然风光', '湖泊', '摄影胜地'],
                    rating: '4.6',
                    tips: '日出日落时分景色最美，适合摄影'
                }
            };
            
            const result = results[sceneName] || {
                name: sceneName || '未知景点',
                confidence: '88%',
                description: '这是一个美丽的景点，具有独特的文化和自然价值。',
                tags: ['景点', '旅游'],
                rating: '4.5',
                tips: '建议提前了解开放时间和门票信息'
            };
            
            resultContent.innerHTML = `
                <div class="bg-green-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h5 class="font-bold text-green-800">${result.name}</h5>
                        <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">置信度 ${result.confidence}</span>
                    </div>
                    <p class="text-sm text-gray-700 mb-3">${result.description}</p>
                    
                    <div class="flex items-center space-x-4 mb-3 text-xs text-gray-600">
                        <span>⭐ ${result.rating}</span>
                        <span>📍 合肥市</span>
                    </div>
                    
                    <div class="flex flex-wrap gap-1 mb-3">
                        ${result.tags.map(tag => `<span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">${tag}</span>`).join('')}
                    </div>
                    
                    <div class="bg-white rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-800 mb-1">💡 游览建议</div>
                        <div class="text-xs text-gray-600">${result.tips}</div>
                    </div>
                    
                    <div class="flex space-x-2 mt-4">
                        <button class="flex-1 bg-green-500 text-white py-2 rounded-lg text-sm">查看详情</button>
                        <button class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm">加入行程</button>
                    </div>
                </div>
            `;
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
            
            // 拖拽上传功能
            const uploadArea = document.getElementById('uploadArea');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    showRecognitionProcess(files[0].name);
                }
            });
        });
    </script>
</body>
</html>
