<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部状态栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <!-- 头部信息 -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮位置（首页隐藏） -->
                    <div class="w-8 h-8 opacity-0">
                        <span class="text-xl">←</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <span class="text-xs">📍</span>
                        </div>
                        <div>
                            <div class="text-sm opacity-90">当前位置</div>
                            <div class="text-base font-medium">合肥市 · 包河区</div>
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🔔</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">⚙️</span>
                    </button>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="relative">
                <input type="text" 
                       placeholder="搜索景点、美食、活动..." 
                       class="w-full bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 rounded-full px-4 py-3 pl-12 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50">
                <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
                    <span class="text-white opacity-70">🔍</span>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 景点推介 Banner -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <!-- Banner 轮播 -->
                    <div class="relative h-48 bg-gradient-to-r from-blue-400 to-purple-500">
                        <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=200&fit=crop&crop=center"
                             alt="包河公园"
                             class="w-full h-full object-cover"
                             onerror="this.src='https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop'">
                        <div class="absolute inset-0 bg-black bg-opacity-30"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <h3 class="text-lg font-bold mb-1">包河公园</h3>
                            <p class="text-sm opacity-90">感受包拯文化的历史底蕴</p>
                        </div>
                        <div class="absolute top-4 right-4 bg-white bg-opacity-20 backdrop-blur-sm rounded-full px-3 py-1">
                            <span class="text-white text-xs">4.8⭐</span>
                        </div>
                        <!-- 轮播指示器 -->
                        <div class="absolute bottom-4 right-4 flex space-x-1">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                            <div class="w-2 h-2 bg-white bg-opacity-50 rounded-full"></div>
                            <div class="w-2 h-2 bg-white bg-opacity-50 rounded-full"></div>
                        </div>
                    </div>

                    <!-- Banner 底部信息 -->
                    <div class="p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                <span>📍 2.3km</span>
                                <span>🎫 免费</span>
                                <span>⏰ 全天开放</span>
                            </div>
                            <button class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium card-hover">
                                查看详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 合肥概览模块 -->
            <div class="px-4 mb-6">
                <h2 class="text-lg font-bold text-gray-800 mb-4">合肥概览</h2>
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <!-- 地图容器 -->
                    <div class="relative h-48 bg-gray-100">
                        <div id="mapContainer" class="w-full h-full rounded-t-2xl"></div>
                        <!-- 地图加载提示 -->
                        <div class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-t-2xl" id="mapLoading">
                            <div class="text-center">
                                <div class="text-4xl mb-2">🗺️</div>
                                <div class="text-sm text-gray-600">合肥市景点分布图</div>
                                <div class="text-xs text-gray-500 mt-1">点击查看详细地图</div>
                            </div>
                        </div>
                    </div>

                    <!-- 景点快览 -->
                    <div class="p-4">
                        <div class="grid grid-cols-3 gap-3">
                            <div class="text-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <span class="text-blue-600 text-lg">🏛️</span>
                                </div>
                                <div class="text-xs font-medium text-gray-800">包河公园</div>
                                <div class="text-xs text-gray-500">历史文化</div>
                            </div>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <span class="text-green-600 text-lg">🏞️</span>
                                </div>
                                <div class="text-xs font-medium text-gray-800">巢湖</div>
                                <div class="text-xs text-gray-500">自然风光</div>
                            </div>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <span class="text-purple-600 text-lg">🏘️</span>
                                </div>
                                <div class="text-xs font-medium text-gray-800">三河古镇</div>
                                <div class="text-xs text-gray-500">古镇水乡</div>
                            </div>
                        </div>

                        <button class="w-full mt-4 bg-blue-50 text-blue-600 py-2 rounded-lg text-sm font-medium" onclick="openMap()">
                            📍 查看完整地图
                        </button>
                    </div>
                </div>
            </div>

            <!-- 智能服务 -->
            <div class="px-4 mb-6">
                <h2 class="text-lg font-bold text-gray-800 mb-4">智能服务</h2>
                <div class="flex space-x-4 hide-scrollbar overflow-x-auto pb-2">
                    <button class="bg-white rounded-2xl p-3 shadow-sm card-hover flex flex-col items-center flex-shrink-0 w-20" onclick="navigateToPage('行程')">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">📅</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">行程规划</span>
                    </button>

                    <button class="bg-white rounded-2xl p-3 shadow-sm card-hover flex flex-col items-center flex-shrink-0 w-20" onclick="openImageRecognition()">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">📷</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">看图识景</span>
                    </button>

                    <button class="bg-white rounded-2xl p-3 shadow-sm card-hover flex flex-col items-center flex-shrink-0 w-20" onclick="openTourGuide()">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🗺️</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">旅游导览</span>
                    </button>

                    <button class="bg-white rounded-2xl p-3 shadow-sm card-hover flex flex-col items-center flex-shrink-0 w-20" onclick="openTravelNotes()">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">📝</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">游记生成</span>
                    </button>

                    <button class="bg-white rounded-2xl p-3 shadow-sm card-hover flex flex-col items-center flex-shrink-0 w-20" onclick="openAIChat()">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-400 to-red-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🤖</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">智能问答</span>
                    </button>
                </div>
            </div>



            <!-- 推荐展示 -->
            <div class="px-4 mb-6">
                <!-- 推荐活动 -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-bold text-gray-800">推荐活动</h2>
                        <button class="text-sm text-blue-500">查看全部</button>
                    </div>
                    
                    <div class="flex space-x-4 hide-scrollbar overflow-x-auto">
                        <div class="flex-shrink-0 w-64 bg-white rounded-2xl shadow-sm overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=300&h=150&fit=crop" 
                                 alt="活动" 
                                 class="w-full h-32 object-cover">
                            <div class="p-3">
                                <h3 class="font-medium text-gray-800 mb-1">合肥樱花节</h3>
                                <p class="text-xs text-gray-600 mb-2">4月1日-4月30日</p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">进行中</span>
                                    <span class="text-xs text-gray-500">📍 植物园</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex-shrink-0 w-64 bg-white rounded-2xl shadow-sm overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1414016642750-7fdd78dc33d9?w=300&h=150&fit=crop" 
                                 alt="活动" 
                                 class="w-full h-32 object-cover">
                            <div class="p-3">
                                <h3 class="font-medium text-gray-800 mb-1">美食文化节</h3>
                                <p class="text-xs text-gray-600 mb-2">5月15日-5月20日</p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full">即将开始</span>
                                    <span class="text-xs text-gray-500">📍 淮河路</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 热门景点 -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-bold text-gray-800">热门景点</h2>
                        <button class="text-sm text-blue-500">查看全部</button>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="bg-white rounded-2xl p-4 shadow-sm flex items-center space-x-4">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=80&h=80&fit=crop" 
                                 alt="三河古镇" 
                                 class="w-16 h-16 rounded-xl object-cover">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-800 mb-1">三河古镇</h3>
                                <p class="text-xs text-gray-600 mb-2">江南水乡风情，历史文化名镇</p>
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>📍 45km</span>
                                    <span>⭐ 4.6</span>
                                    <span>🎫 ¥80</span>
                                </div>
                            </div>
                            <button class="text-blue-500 text-sm">详情</button>
                        </div>
                        
                        <div class="bg-white rounded-2xl p-4 shadow-sm flex items-center space-x-4">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=80&h=80&fit=crop" 
                                 alt="巢湖" 
                                 class="w-16 h-16 rounded-xl object-cover">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-800 mb-1">巢湖风景区</h3>
                                <p class="text-xs text-gray-600 mb-2">中国五大淡水湖之一，湖光山色</p>
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>📍 35km</span>
                                    <span>⭐ 4.4</span>
                                    <span>🎫 免费</span>
                                </div>
                            </div>
                            <button class="text-blue-500 text-sm">详情</button>
                        </div>
                    </div>
                </div>

                <!-- 旅游攻略 -->
                <div class="mb-20">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-bold text-gray-800">旅游攻略</h2>
                        <button class="text-sm text-blue-500">查看全部</button>
                    </div>
                    
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop" 
                             alt="攻略" 
                             class="w-full h-40 object-cover">
                        <div class="p-4">
                            <h3 class="font-medium text-gray-800 mb-2">合肥两日游完美攻略</h3>
                            <p class="text-sm text-gray-600 mb-3">包公园→三河古镇→巢湖→科技馆，带你玩转合肥经典景点</p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <div class="flex items-center space-x-3">
                                    <span>👤 旅行达人小王</span>
                                    <span>👁️ 2.3k</span>
                                    <span>❤️ 156</span>
                                </div>
                                <span>2天前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-blue-500 text-xl mb-1">🏠</span>
                    <span class="text-xs text-blue-500 font-medium">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            // 根据页面名称跳转到对应页面
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };

            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }

        // 返回上一页功能（首页通常不需要，但保持一致性）
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 首页是根页面，可以不做任何操作或显示提示
                console.log('已经在首页了');
            }
        }

        // 打开地图功能
        function openMap() {
            // 这里可以集成高德地图API
            alert('正在打开高德地图，展示合肥市景点分布...');
            // 实际项目中可以跳转到地图页面或打开地图应用
            // window.open('https://ditu.amap.com/search?query=合肥市景点&city=340100');
        }

        // 看图识景功能
        function openImageRecognition() {
            // 创建新页面内容
            const newPageContent = `
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>看图识景</title>
                    <script src="https://cdn.tailwindcss.com"></script>
                </head>
                <body class="bg-gray-50">
                    <div class="min-h-screen">
                        <div class="bg-gradient-to-r from-green-400 to-green-600 text-white p-4 pt-12">
                            <div class="flex items-center mb-4">
                                <button onclick="history.back()" class="mr-3">
                                    <span class="text-2xl">←</span>
                                </button>
                                <h1 class="text-xl font-bold">看图识景</h1>
                            </div>
                            <p class="text-sm opacity-90">拍照或上传图片，AI帮您识别景点信息</p>
                        </div>

                        <div class="p-4">
                            <div class="bg-white rounded-2xl p-6 text-center shadow-sm mb-6">
                                <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-4xl">📷</span>
                                </div>
                                <h3 class="text-lg font-bold text-gray-800 mb-2">智能识景</h3>
                                <p class="text-sm text-gray-600 mb-6">上传景点照片，获取详细信息和游玩攻略</p>

                                <div class="space-y-3">
                                    <button class="w-full bg-green-500 text-white py-3 rounded-xl font-medium">
                                        📸 拍照识别
                                    </button>
                                    <button class="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium">
                                        🖼️ 从相册选择
                                    </button>
                                </div>
                            </div>

                            <div class="bg-white rounded-2xl p-4 shadow-sm">
                                <h4 class="font-medium text-gray-800 mb-3">🔥 热门识别</h4>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="text-center">
                                        <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=150&h=100&fit=crop" class="w-full h-20 object-cover rounded-lg mb-2">
                                        <span class="text-xs text-gray-600">包河公园</span>
                                    </div>
                                    <div class="text-center">
                                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=150&h=100&fit=crop" class="w-full h-20 object-cover rounded-lg mb-2">
                                        <span class="text-xs text-gray-600">巢湖风景</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;

            // 打开新窗口显示页面
            const newWindow = window.open('', '_blank');
            newWindow.document.write(newPageContent);
            newWindow.document.close();
        }

        // 旅游导览功能
        function openTourGuide() {
            const newPageContent = `
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>旅游导览</title>
                    <script src="https://cdn.tailwindcss.com"></script>
                </head>
                <body class="bg-gray-50">
                    <div class="min-h-screen">
                        <div class="bg-gradient-to-r from-purple-400 to-purple-600 text-white p-4 pt-12">
                            <div class="flex items-center mb-4">
                                <button onclick="history.back()" class="mr-3">
                                    <span class="text-2xl">←</span>
                                </button>
                                <h1 class="text-xl font-bold">旅游导览</h1>
                            </div>
                            <p class="text-sm opacity-90">智能语音导览，深度了解景点文化</p>
                        </div>

                        <div class="p-4">
                            <div class="bg-white rounded-2xl p-6 text-center shadow-sm mb-6">
                                <div class="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-4xl">🗺️</span>
                                </div>
                                <h3 class="text-lg font-bold text-gray-800 mb-2">智能导览</h3>
                                <p class="text-sm text-gray-600 mb-6">基于位置的智能语音导览服务</p>

                                <button class="w-full bg-purple-500 text-white py-3 rounded-xl font-medium mb-3">
                                    🎧 开始语音导览
                                </button>
                                <button class="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium">
                                    📍 选择景点导览
                                </button>
                            </div>

                            <div class="space-y-3">
                                <div class="bg-white rounded-2xl p-4 shadow-sm">
                                    <h4 class="font-medium text-gray-800 mb-2">包河公园导览</h4>
                                    <p class="text-sm text-gray-600 mb-3">了解包拯文化，探索历史故事</p>
                                    <button class="text-purple-500 text-sm">🎵 播放导览</button>
                                </div>
                                <div class="bg-white rounded-2xl p-4 shadow-sm">
                                    <h4 class="font-medium text-gray-800 mb-2">三河古镇导览</h4>
                                    <p class="text-sm text-gray-600 mb-3">江南水乡风情，古镇历史文化</p>
                                    <button class="text-purple-500 text-sm">🎵 播放导览</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;

            const newWindow = window.open('', '_blank');
            newWindow.document.write(newPageContent);
            newWindow.document.close();
        }

        // 游记生成功能
        function openTravelNotes() {
            const newPageContent = `
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>游记生成</title>
                    <script src="https://cdn.tailwindcss.com"></script>
                </head>
                <body class="bg-gray-50">
                    <div class="min-h-screen">
                        <div class="bg-gradient-to-r from-orange-400 to-orange-600 text-white p-4 pt-12">
                            <div class="flex items-center mb-4">
                                <button onclick="history.back()" class="mr-3">
                                    <span class="text-2xl">←</span>
                                </button>
                                <h1 class="text-xl font-bold">游记生成</h1>
                            </div>
                            <p class="text-sm opacity-90">AI帮您生成精美游记，记录美好时光</p>
                        </div>

                        <div class="p-4">
                            <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
                                <h3 class="text-lg font-bold text-gray-800 mb-4">创建游记</h3>

                                <div class="space-y-4">
                                    <div>
                                        <label class="text-sm font-medium text-gray-700 block mb-2">游记标题</label>
                                        <input type="text" class="w-full p-3 border border-gray-200 rounded-lg" placeholder="输入您的游记标题...">
                                    </div>

                                    <div>
                                        <label class="text-sm font-medium text-gray-700 block mb-2">选择照片</label>
                                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                            <span class="text-4xl mb-2 block">📸</span>
                                            <p class="text-sm text-gray-600">点击上传照片或拖拽到此处</p>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="text-sm font-medium text-gray-700 block mb-2">游记风格</label>
                                        <div class="grid grid-cols-2 gap-2">
                                            <button class="p-2 border border-orange-200 bg-orange-50 text-orange-600 rounded-lg text-sm">文艺清新</button>
                                            <button class="p-2 border border-gray-200 bg-gray-50 text-gray-600 rounded-lg text-sm">详细攻略</button>
                                            <button class="p-2 border border-gray-200 bg-gray-50 text-gray-600 rounded-lg text-sm">简约日记</button>
                                            <button class="p-2 border border-gray-200 bg-gray-50 text-gray-600 rounded-lg text-sm">幽默风趣</button>
                                        </div>
                                    </div>

                                    <button class="w-full bg-orange-500 text-white py-3 rounded-xl font-medium">
                                        ✨ AI生成游记
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;

            const newWindow = window.open('', '_blank');
            newWindow.document.write(newPageContent);
            newWindow.document.close();
        }

        // 智能问答功能
        function openAIChat() {
            const newPageContent = `
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>智能问答</title>
                    <script src="https://cdn.tailwindcss.com"></script>
                </head>
                <body class="bg-gray-50">
                    <div class="min-h-screen flex flex-col">
                        <div class="bg-gradient-to-r from-red-400 to-red-600 text-white p-4 pt-12">
                            <div class="flex items-center mb-4">
                                <button onclick="history.back()" class="mr-3">
                                    <span class="text-2xl">←</span>
                                </button>
                                <h1 class="text-xl font-bold">智能问答</h1>
                            </div>
                            <p class="text-sm opacity-90">旅游小助手，随时为您答疑解惑</p>
                        </div>

                        <div class="flex-1 p-4 space-y-4">
                            <div class="bg-white rounded-2xl p-4 shadow-sm">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <span class="text-red-600">🤖</span>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-800">您好！我是合肥旅游小助手，有什么可以帮助您的吗？</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-2xl p-4 shadow-sm">
                                <h4 class="font-medium text-gray-800 mb-3">💡 常见问题</h4>
                                <div class="space-y-2">
                                    <button class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700">合肥有哪些必去景点？</button>
                                    <button class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700">三河古镇怎么去？</button>
                                    <button class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700">合肥有什么特色美食？</button>
                                    <button class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700">包河公园门票多少钱？</button>
                                </div>
                            </div>
                        </div>

                        <div class="p-4 bg-white border-t border-gray-200">
                            <div class="flex space-x-2">
                                <input type="text" class="flex-1 p-3 border border-gray-200 rounded-xl" placeholder="输入您的问题...">
                                <button class="bg-red-500 text-white px-6 py-3 rounded-xl">发送</button>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;

            const newWindow = window.open('', '_blank');
            newWindow.document.write(newPageContent);
            newWindow.document.close();
        }

        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有按钮添加点击反馈
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });

            // 模拟地图加载
            setTimeout(() => {
                const mapLoading = document.getElementById('mapLoading');
                if (mapLoading) {
                    mapLoading.style.opacity = '0.8';
                }
            }, 1000);
        });
    </script>
</body>
</html>
