<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 社区</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 标签选中状态 */
        .tab-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-xl font-bold">旅游社区</h1>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">✏️</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🔔</span>
                    </button>
                </div>
            </div>
            
            <!-- 分类标签 -->
            <div class="flex space-x-3">
                <button class="tab-active px-4 py-2 rounded-full text-sm font-medium" onclick="switchTab('travel-notes')">
                    游记
                </button>
                <button class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm font-medium" onclick="switchTab('guides')">
                    攻略
                </button>
                <button class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm font-medium" onclick="switchTab('qa')">
                    问答
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 热门话题 -->
            <div class="px-4 -mt-4 mb-4">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">🔥 热门话题</span>
                        <button class="text-xs text-blue-500">更多</button>
                    </div>
                    
                    <div class="flex space-x-2 hide-scrollbar overflow-x-auto">
                        <div class="flex-shrink-0 bg-gradient-to-r from-pink-100 to-purple-100 px-3 py-2 rounded-full">
                            <span class="text-xs text-purple-700">#合肥樱花季</span>
                        </div>
                        <div class="flex-shrink-0 bg-gradient-to-r from-blue-100 to-cyan-100 px-3 py-2 rounded-full">
                            <span class="text-xs text-blue-700">#三河古镇游</span>
                        </div>
                        <div class="flex-shrink-0 bg-gradient-to-r from-green-100 to-emerald-100 px-3 py-2 rounded-full">
                            <span class="text-xs text-green-700">#巢湖风光</span>
                        </div>
                        <div class="flex-shrink-0 bg-gradient-to-r from-orange-100 to-red-100 px-3 py-2 rounded-full">
                            <span class="text-xs text-orange-700">#美食探店</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 游记内容 -->
            <div id="travel-notes-content" class="px-4 mb-20">
                <div class="space-y-4">
                    <!-- 游记1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <!-- 用户信息 -->
                        <div class="flex items-center justify-between p-4 pb-3">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                                     alt="用户头像" 
                                     class="w-10 h-10 rounded-full object-cover">
                                <div>
                                    <div class="text-sm font-medium text-gray-800">旅行达人小王</div>
                                    <div class="text-xs text-gray-500">2小时前 · 合肥</div>
                                </div>
                            </div>
                            <button class="text-gray-400">
                                <span class="text-lg">⋯</span>
                            </button>
                        </div>
                        
                        <!-- 游记内容 -->
                        <div class="px-4 pb-3">
                            <h3 class="font-medium text-gray-800 mb-2">合肥两日游完美攻略 🌸</h3>
                            <p class="text-sm text-gray-600 mb-3">包公园→三河古镇→巢湖→科技馆，带你玩转合肥经典景点。春天的合肥真的太美了，樱花盛开的季节，每一处风景都让人流连忘返...</p>
                        </div>
                        
                        <!-- 图片展示 -->
                        <div class="px-4 pb-3">
                            <div class="grid grid-cols-3 gap-2">
                                <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=120&h=120&fit=crop" 
                                     alt="包公园" 
                                     class="w-full h-24 object-cover rounded-lg">
                                <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=120&h=120&fit=crop" 
                                     alt="三河古镇" 
                                     class="w-full h-24 object-cover rounded-lg">
                                <img src="https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=120&h=120&fit=crop" 
                                     alt="樱花" 
                                     class="w-full h-24 object-cover rounded-lg">
                            </div>
                        </div>
                        
                        <!-- 互动区域 -->
                        <div class="flex items-center justify-between px-4 py-3 border-t border-gray-100">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">❤️</span>
                                    <span class="text-xs">156</span>
                                </button>
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">💬</span>
                                    <span class="text-xs">23</span>
                                </button>
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">👁️</span>
                                    <span class="text-xs">2.3k</span>
                                </button>
                            </div>
                            <button class="text-gray-500">
                                <span class="text-lg">🔗</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 游记2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex items-center justify-between p-4 pb-3">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                                     alt="用户头像" 
                                     class="w-10 h-10 rounded-full object-cover">
                                <div>
                                    <div class="text-sm font-medium text-gray-800">摄影师老李</div>
                                    <div class="text-xs text-gray-500">5小时前 · 巢湖</div>
                                </div>
                            </div>
                            <button class="text-gray-400">
                                <span class="text-lg">⋯</span>
                            </button>
                        </div>
                        
                        <div class="px-4 pb-3">
                            <h3 class="font-medium text-gray-800 mb-2">巢湖日出绝美瞬间 📸</h3>
                            <p class="text-sm text-gray-600 mb-3">凌晨5点起床赶到巢湖看日出，真的值了！湖面波光粼粼，远山如黛，这一刻的美景让人终生难忘...</p>
                        </div>
                        
                        <div class="px-4 pb-3">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop" 
                                 alt="巢湖日出" 
                                 class="w-full h-48 object-cover rounded-lg">
                        </div>
                        
                        <div class="flex items-center justify-between px-4 py-3 border-t border-gray-100">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">❤️</span>
                                    <span class="text-xs">89</span>
                                </button>
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">💬</span>
                                    <span class="text-xs">12</span>
                                </button>
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">👁️</span>
                                    <span class="text-xs">1.2k</span>
                                </button>
                            </div>
                            <button class="text-gray-500">
                                <span class="text-lg">🔗</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 游记3 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex items-center justify-between p-4 pb-3">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face" 
                                     alt="用户头像" 
                                     class="w-10 h-10 rounded-full object-cover">
                                <div>
                                    <div class="text-sm font-medium text-gray-800">美食探索家</div>
                                    <div class="text-xs text-gray-500">1天前 · 淮河路</div>
                                </div>
                            </div>
                            <button class="text-gray-400">
                                <span class="text-lg">⋯</span>
                            </button>
                        </div>
                        
                        <div class="px-4 pb-3">
                            <h3 class="font-medium text-gray-800 mb-2">合肥美食打卡指南 🍜</h3>
                            <p class="text-sm text-gray-600 mb-3">庐州烤鸭、小笼包、臭鳜鱼...合肥的美食真的太丰富了！今天带大家打卡几家必吃的老字号...</p>
                        </div>
                        
                        <div class="px-4 pb-3">
                            <div class="grid grid-cols-2 gap-2">
                                <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=150&h=120&fit=crop" 
                                     alt="烤鸭" 
                                     class="w-full h-24 object-cover rounded-lg">
                                <img src="https://images.unsplash.com/photo-1551218808-94e220e084d2?w=150&h=120&fit=crop" 
                                     alt="小笼包" 
                                     class="w-full h-24 object-cover rounded-lg">
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between px-4 py-3 border-t border-gray-100">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">❤️</span>
                                    <span class="text-xs">234</span>
                                </button>
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">💬</span>
                                    <span class="text-xs">45</span>
                                </button>
                                <button class="flex items-center space-x-1 text-gray-500">
                                    <span class="text-lg">👁️</span>
                                    <span class="text-xs">3.1k</span>
                                </button>
                            </div>
                            <button class="text-gray-500">
                                <span class="text-lg">🔗</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 加载更多 -->
                <div class="text-center mt-6">
                    <button class="text-blue-500 text-sm">加载更多游记</button>
                </div>
            </div>

            <!-- 攻略内容（隐藏） -->
            <div id="guides-content" class="px-4 mb-20 hidden">
                <div class="space-y-4">
                    <!-- 攻略1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=150&fit=crop" 
                             alt="攻略封面" 
                             class="w-full h-32 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h3 class="font-medium text-gray-800">合肥一日游最佳路线</h3>
                                <span class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">精华</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">包公园→逍遥津→李鸿章故居→三河古镇，一天时间玩转合肥经典景点</p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <div class="flex items-center space-x-3">
                                    <span>👤 旅游专家</span>
                                    <span>👁️ 5.2k</span>
                                    <span>⭐ 4.8</span>
                                </div>
                                <span>3天前</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 攻略2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=150&fit=crop" 
                             alt="攻略封面" 
                             class="w-full h-32 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h3 class="font-medium text-gray-800">三河古镇深度游攻略</h3>
                                <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">推荐</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">古镇历史、必看景点、特色美食、住宿推荐，一篇攻略玩转三河</p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <div class="flex items-center space-x-3">
                                    <span>👤 古镇达人</span>
                                    <span>👁️ 3.8k</span>
                                    <span>⭐ 4.6</span>
                                </div>
                                <span>1周前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 问答内容（隐藏） -->
            <div id="qa-content" class="px-4 mb-20 hidden">
                <div class="space-y-4">
                    <!-- 问答1 -->
                    <div class="bg-white rounded-2xl shadow-sm p-4 card-hover">
                        <div class="flex items-start space-x-3 mb-3">
                            <span class="text-lg">❓</span>
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-800 mb-2">合肥有哪些适合亲子游的景点？</h3>
                                <p class="text-sm text-gray-600 mb-3">计划带5岁的孩子去合肥玩，求推荐适合亲子游的景点和活动</p>
                                <div class="text-xs text-gray-500">
                                    <span>👤 新手爸爸</span>
                                    <span class="mx-2">•</span>
                                    <span>2小时前</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-3 ml-8">
                            <div class="flex items-start space-x-2 mb-2">
                                <span class="text-sm">💡</span>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-700">推荐安徽省科技馆，有很多互动体验项目，孩子会很喜欢。还有合肥野生动物园，可以近距离接触小动物...</p>
                                    <div class="text-xs text-gray-500 mt-2">
                                        <span>👤 亲子游达人</span>
                                        <span class="mx-2">•</span>
                                        <span>1小时前</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between mt-3 text-xs text-gray-500">
                            <div class="flex items-center space-x-3">
                                <span>💬 3个回答</span>
                                <span>👁️ 156</span>
                            </div>
                            <button class="text-blue-500">查看全部</button>
                        </div>
                    </div>
                    
                    <!-- 问答2 -->
                    <div class="bg-white rounded-2xl shadow-sm p-4 card-hover">
                        <div class="flex items-start space-x-3 mb-3">
                            <span class="text-lg">❓</span>
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-800 mb-2">三河古镇门票多少钱？</h3>
                                <p class="text-sm text-gray-600 mb-3">想去三河古镇，请问门票价格和开放时间是怎样的？</p>
                                <div class="text-xs text-gray-500">
                                    <span>👤 旅行新手</span>
                                    <span class="mx-2">•</span>
                                    <span>6小时前</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-3 ml-8">
                            <div class="flex items-start space-x-2 mb-2">
                                <span class="text-sm">💡</span>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-700">三河古镇门票80元/人，开放时间8:30-17:30。建议提前网上购票有优惠...</p>
                                    <div class="text-xs text-gray-500 mt-2">
                                        <span>👤 本地向导</span>
                                        <span class="mx-2">•</span>
                                        <span>5小时前</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between mt-3 text-xs text-gray-500">
                            <div class="flex items-center space-x-3">
                                <span>💬 2个回答</span>
                                <span>👁️ 89</span>
                            </div>
                            <button class="text-blue-500">查看全部</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布按钮 -->
        <div class="fixed bottom-20 right-4 z-10">
            <button class="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg flex items-center justify-center">
                <span class="text-xl">✏️</span>
            </button>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-blue-500 text-xl mb-1">👥</span>
                    <span class="text-xs text-blue-500 font-medium">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 标签切换功能
        function switchTab(tabName) {
            // 隐藏所有内容
            document.getElementById('travel-notes-content').classList.add('hidden');
            document.getElementById('guides-content').classList.add('hidden');
            document.getElementById('qa-content').classList.add('hidden');
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // 更新标签样式
            const tabs = document.querySelectorAll('.gradient-bg button');
            tabs.forEach(tab => {
                tab.classList.remove('tab-active');
                tab.classList.add('bg-white', 'bg-opacity-20');
            });
            
            // 设置当前标签为选中状态
            event.target.classList.add('tab-active');
            event.target.classList.remove('bg-white', 'bg-opacity-20');
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
