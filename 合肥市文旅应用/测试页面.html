<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅应用 - 功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- 顶部导航 -->
        <div class="gradient-bg text-white p-4 pt-12">
            <div class="flex items-center mb-4">
                <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span class="text-xl">←</span>
                </button>
                <h1 class="text-xl font-bold">功能测试页面</h1>
            </div>
            <p class="text-sm opacity-90">测试所有页面的导航和返回功能</p>
        </div>
        
        <!-- 测试内容 -->
        <div class="p-4 space-y-4">
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-bold text-gray-800 mb-3">📱 页面导航测试</h3>
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="navigateToPage('首页')" class="bg-blue-500 text-white py-2 rounded-lg card-hover">
                        🏠 首页
                    </button>
                    <button onclick="navigateToPage('探索')" class="bg-green-500 text-white py-2 rounded-lg card-hover">
                        🔍 探索
                    </button>
                    <button onclick="navigateToPage('社区')" class="bg-purple-500 text-white py-2 rounded-lg card-hover">
                        👥 社区
                    </button>
                    <button onclick="navigateToPage('行程')" class="bg-orange-500 text-white py-2 rounded-lg card-hover">
                        📅 行程
                    </button>
                    <button onclick="navigateToPage('我的')" class="bg-red-500 text-white py-2 rounded-lg card-hover">
                        👤 我的
                    </button>
                </div>
            </div>
            
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-bold text-gray-800 mb-3">🔙 返回功能测试</h3>
                <p class="text-sm text-gray-600 mb-3">每个页面左上角都有返回按钮，点击可返回上一页</p>
                <button onclick="goBack()" class="w-full bg-gray-500 text-white py-2 rounded-lg card-hover">
                    ← 测试返回功能
                </button>
            </div>
            
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-bold text-gray-800 mb-3">✨ 智能服务测试</h3>
                <p class="text-sm text-gray-600 mb-3">首页智能服务模块支持横向滑动，点击各项服务会打开新页面</p>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="text-blue-500">📅</span>
                        <span class="text-sm">行程规划 - 跳转到行程页面</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-green-500">📷</span>
                        <span class="text-sm">看图识景 - 打开新窗口</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-purple-500">🗺️</span>
                        <span class="text-sm">旅游导览 - 打开新窗口</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-orange-500">📝</span>
                        <span class="text-sm">游记生成 - 打开新窗口</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-red-500">🤖</span>
                        <span class="text-sm">智能问答 - 打开新窗口</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-bold text-gray-800 mb-3">🎯 行程创建测试</h3>
                <p class="text-sm text-gray-600 mb-3">行程页面的"创建新行程"功能，支持AI智能生成</p>
                <ol class="text-sm text-gray-600 space-y-1">
                    <li>1. 进入行程页面</li>
                    <li>2. 点击"创建新行程"</li>
                    <li>3. 选择旅行偏好</li>
                    <li>4. AI生成个性化行程</li>
                </ol>
            </div>
            
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-bold text-gray-800 mb-3">🗺️ 合肥概览测试</h3>
                <p class="text-sm text-gray-600 mb-3">首页新增合肥概览模块，展示地图和景点信息</p>
                <button onclick="alert('地图功能演示')" class="w-full bg-blue-100 text-blue-600 py-2 rounded-lg">
                    📍 查看合肥地图
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
