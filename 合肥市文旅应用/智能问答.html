<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 智能问答</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 消息气泡样式 */
        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
        }
        
        .user-message {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            margin-left: auto;
            border-radius: 18px 18px 4px 18px;
        }
        
        .bot-message {
            background: #f3f4f6;
            color: #374151;
            margin-right: auto;
            border-radius: 18px 18px 18px 4px;
        }
        
        /* 打字动画 */
        .typing-indicator {
            display: inline-block;
        }
        
        .typing-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #9ca3af;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        /* 输入框聚焦效果 */
        .input-focus:focus {
            outline: none;
            ring: 2px;
            ring-color: #ef4444;
            border-color: transparent;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">智能问答</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🔊</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📋</span>
                    </button>
                </div>
            </div>
            <p class="text-sm opacity-90">旅游小助手，随时为您答疑解惑</p>
        </div>

        <!-- 聊天区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto" id="chatArea">
            <!-- 欢迎消息 -->
            <div class="px-4 py-6">
                <div class="flex items-start space-x-3 mb-6">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-red-600">🤖</span>
                    </div>
                    <div class="message-bubble bot-message p-3">
                        <p class="text-sm">您好！我是合肥旅游小助手小肥，很高兴为您服务！🎉</p>
                        <p class="text-sm mt-2">我可以帮您解答关于合肥旅游的各种问题，比如景点推荐、交通指南、美食攻略等。有什么想了解的吗？</p>
                    </div>
                </div>
                
                <!-- 常见问题 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h4 class="font-medium text-gray-800 mb-3">💡 常见问题</h4>
                    <div class="space-y-2">
                        <button onclick="askQuestion('合肥有哪些必去景点？')" 
                                class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700 card-hover">
                            合肥有哪些必去景点？
                        </button>
                        <button onclick="askQuestion('三河古镇怎么去？')" 
                                class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700 card-hover">
                            三河古镇怎么去？
                        </button>
                        <button onclick="askQuestion('合肥有什么特色美食？')" 
                                class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700 card-hover">
                            合肥有什么特色美食？
                        </button>
                        <button onclick="askQuestion('包河公园门票多少钱？')" 
                                class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700 card-hover">
                            包河公园门票多少钱？
                        </button>
                        <button onclick="askQuestion('合肥适合几月份去旅游？')" 
                                class="w-full text-left p-3 bg-gray-50 rounded-lg text-sm text-gray-700 card-hover">
                            合肥适合几月份去旅游？
                        </button>
                    </div>
                </div>
                
                <!-- 聊天消息容器 -->
                <div id="messagesContainer">
                    <!-- 聊天消息将在这里显示 -->
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="bg-white border-t border-gray-200 p-4 safe-area-bottom">
            <div class="flex items-end space-x-3">
                <!-- 语音输入按钮 -->
                <button onclick="startVoiceInput()" 
                        class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-gray-600">🎤</span>
                </button>
                
                <!-- 文本输入框 -->
                <div class="flex-1 relative">
                    <textarea id="messageInput" 
                              class="w-full p-3 pr-12 border border-gray-200 rounded-2xl resize-none input-focus" 
                              rows="1"
                              placeholder="输入您的问题..."
                              onkeypress="handleKeyPress(event)"
                              oninput="adjustTextareaHeight(this)"></textarea>
                    
                    <!-- 表情按钮 -->
                    <button onclick="showEmoji()" 
                            class="absolute right-3 bottom-3 w-6 h-6 flex items-center justify-center">
                        <span class="text-gray-400 text-sm">😊</span>
                    </button>
                </div>
                
                <!-- 发送按钮 -->
                <button onclick="sendMessage()" 
                        id="sendButton"
                        class="w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-sm">📤</span>
                </button>
            </div>
            
            <!-- 快捷回复 -->
            <div class="flex space-x-2 mt-3 hide-scrollbar overflow-x-auto">
                <button onclick="askQuestion('推荐路线')" 
                        class="flex-shrink-0 px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                    推荐路线
                </button>
                <button onclick="askQuestion('交通指南')" 
                        class="flex-shrink-0 px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                    交通指南
                </button>
                <button onclick="askQuestion('住宿推荐')" 
                        class="flex-shrink-0 px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                    住宿推荐
                </button>
                <button onclick="askQuestion('天气情况')" 
                        class="flex-shrink-0 px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                    天气情况
                </button>
                <button onclick="askQuestion('购物攻略')" 
                        class="flex-shrink-0 px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                    购物攻略
                </button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        let messageHistory = [];
        
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage(message, 'user');
            
            // 清空输入框
            input.value = '';
            adjustTextareaHeight(input);
            
            // 显示机器人正在输入
            showTypingIndicator();
            
            // 模拟AI回复
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateResponse(message);
                addMessage(response, 'bot');
            }, 1500);
        }
        
        // 快速提问
        function askQuestion(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }
        
        // 添加消息到聊天区域
        function addMessage(message, sender) {
            const container = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            
            if (sender === 'user') {
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3 mb-4 justify-end">
                        <div class="message-bubble user-message p-3">
                            <p class="text-sm">${message}</p>
                        </div>
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-xs">👤</span>
                        </div>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3 mb-4">
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-red-600 text-xs">🤖</span>
                        </div>
                        <div class="message-bubble bot-message p-3">
                            <p class="text-sm">${message}</p>
                        </div>
                    </div>
                `;
            }
            
            container.appendChild(messageDiv);
            
            // 滚动到底部
            const chatArea = document.getElementById('chatArea');
            chatArea.scrollTop = chatArea.scrollHeight;
            
            // 保存消息历史
            messageHistory.push({ message, sender, timestamp: new Date() });
        }
        
        // 显示正在输入指示器
        function showTypingIndicator() {
            const container = document.getElementById('messagesContainer');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typingIndicator';
            typingDiv.innerHTML = `
                <div class="flex items-start space-x-3 mb-4">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-red-600 text-xs">🤖</span>
                    </div>
                    <div class="message-bubble bot-message p-3">
                        <div class="typing-indicator">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(typingDiv);
            
            // 滚动到底部
            const chatArea = document.getElementById('chatArea');
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 隐藏正在输入指示器
        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            if (indicator) {
                indicator.remove();
            }
        }
        
        // 生成AI回复
        function generateResponse(question) {
            const responses = {
                '合肥有哪些必去景点？': '合肥的必去景点包括：\n\n🏛️ **包河公园** - 包拯文化主题公园，免费开放\n🏘️ **三河古镇** - 江南水乡风情，门票80元\n🌊 **巢湖风景区** - 五大淡水湖之一，自然风光秀美\n🏛️ **逍遥津公园** - 三国历史文化，免费开放\n🏢 **安徽省科技馆** - 现代科技展示，门票30元\n\n建议您根据兴趣和时间安排选择2-3个景点深度游览哦！',
                
                '三河古镇怎么去？': '去三河古镇有以下几种方式：\n\n🚌 **公交路线**：\n• 从合肥市区乘坐旅游专线直达\n• 或先到肥西县城，再转乘当地公交\n\n🚗 **自驾路线**：\n• 从合肥出发约45公里，车程1小时\n• 导航搜索"三河古镇"即可\n\n🚄 **高铁+公交**：\n• 高铁到肥西站，再转公交约30分钟\n\n💡 **小贴士**：建议周末避开高峰期，古镇内有停车场',
                
                '合肥有什么特色美食？': '合肥的特色美食丰富多样：\n\n🦆 **庐州烤鸭** - 合肥招牌菜，皮脆肉嫩\n🥟 **小笼包** - 传统手工制作，汤汁鲜美\n🐟 **臭鳜鱼** - 徽菜经典，闻臭吃香\n🍜 **牛肉汤** - 早餐首选，汤鲜肉嫩\n🥮 **烘糕** - 传统糕点，香甜可口\n🍲 **李鸿章杂烩** - 历史名菜，营养丰富\n\n推荐去淮河路步行街品尝各种小吃！',
                
                '包河公园门票多少钱？': '包河公园是**免费开放**的！🎉\n\n📍 **开放时间**：全天24小时开放\n🚇 **交通方式**：地铁1号线包公园站\n🅿️ **停车信息**：有免费停车位\n\n🏛️ **园内景点**：\n• 包公祠 - 了解包拯文化\n• 包公墓 - 历史文物保护单位\n• 清风阁 - 观景好去处\n\n💡 **游览建议**：春秋季节最佳，建议游览2-3小时',
                
                '合肥适合几月份去旅游？': '合肥四季分明，各有特色：\n\n🌸 **春季（3-5月）**：\n• 樱花盛开，气候宜人\n• 推荐指数：⭐⭐⭐⭐⭐\n\n☀️ **夏季（6-8月）**：\n• 绿树成荫，但较炎热\n• 推荐指数：⭐⭐⭐\n\n🍂 **秋季（9-11月）**：\n• 秋高气爽，层林尽染\n• 推荐指数：⭐⭐⭐⭐⭐\n\n❄️ **冬季（12-2月）**：\n• 人少景美，但较寒冷\n• 推荐指数：⭐⭐\n\n**最佳旅游时间**：4-5月和9-10月'
            };
            
            // 检查是否有预设回复
            for (const key in responses) {
                if (question.includes(key.replace('？', '').replace('?', ''))) {
                    return responses[key];
                }
            }
            
            // 关键词匹配回复
            if (question.includes('天气')) {
                return '今天合肥天气晴朗，气温18-25°C，适合出游！☀️\n\n您可以下载天气APP获取更详细的天气预报。出门建议带上轻薄外套，注意防晒哦！';
            }
            
            if (question.includes('住宿') || question.includes('酒店')) {
                return '合肥住宿推荐：\n\n🏨 **市中心区域**：\n• 万达文华酒店 - 豪华型\n• 希尔顿酒店 - 高端商务\n• 如家快捷酒店 - 经济实惠\n\n🏨 **景区附近**：\n• 包河区有多家民宿\n• 三河古镇有特色客栈\n\n💡 建议提前预订，周末价格会有所上涨';
            }
            
            if (question.includes('交通') || question.includes('地铁') || question.includes('公交')) {
                return '合肥交通出行指南：\n\n🚇 **地铁**：\n• 1号线：连接主要景点\n• 2号线：贯穿南北\n• 支持支付宝、微信支付\n\n🚌 **公交**：\n• 覆盖全市，票价2元\n• 推荐使用"合肥通"APP\n\n🚗 **打车**：\n• 滴滴、高德等平台都可用\n• 市区内一般20-40元';
            }
            
            // 默认回复
            return '感谢您的提问！我正在学习更多关于合肥旅游的知识。\n\n您可以尝试问我：\n• 景点推荐和门票信息\n• 交通路线和出行方式\n• 美食推荐和餐厅位置\n• 住宿建议和价格区间\n• 天气情况和穿衣指南\n\n有其他问题随时问我哦！😊';
        }
        
        // 语音输入
        function startVoiceInput() {
            if ('webkitSpeechRecognition' in window) {
                const recognition = new webkitSpeechRecognition();
                recognition.lang = 'zh-CN';
                recognition.start();
                
                recognition.onresult = function(event) {
                    const result = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = result;
                };
                
                recognition.onerror = function() {
                    alert('语音识别失败，请重试');
                };
            } else {
                alert('您的浏览器不支持语音输入功能');
            }
        }
        
        // 显示表情
        function showEmoji() {
            alert('表情功能开发中...\n常用表情：😊 😍 🤔 👍 ❤️');
        }
        
        // 处理键盘事件
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }
        
        // 自动调整输入框高度
        function adjustTextareaHeight(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
