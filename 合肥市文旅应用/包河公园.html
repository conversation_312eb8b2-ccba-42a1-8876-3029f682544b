<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 包河公园</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 视差滚动效果 */
        .parallax {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">包河公园</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">❤️</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📤</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 景点头图 -->
            <div class="relative h-64 -mt-4">
                <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=300&fit=crop&crop=center" 
                     alt="包河公园" 
                     class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-black bg-opacity-30"></div>
                <div class="absolute bottom-4 left-4 text-white">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">免费</span>
                        <span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs">4.8⭐</span>
                    </div>
                    <h2 class="text-2xl font-bold mb-1">包河公园</h2>
                    <p class="text-sm opacity-90">感受包拯文化的历史底蕴</p>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="px-4 py-6">
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">📍 基本信息</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-2xl mb-1">🎫</div>
                            <div class="text-sm font-medium text-gray-800">门票价格</div>
                            <div class="text-xs text-gray-600">免费开放</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">⏰</div>
                            <div class="text-sm font-medium text-gray-800">开放时间</div>
                            <div class="text-xs text-gray-600">全天开放</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">🚇</div>
                            <div class="text-sm font-medium text-gray-800">交通方式</div>
                            <div class="text-xs text-gray-600">地铁1号线</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">📍</div>
                            <div class="text-sm font-medium text-gray-800">距离位置</div>
                            <div class="text-xs text-gray-600">2.3km</div>
                        </div>
                    </div>
                </div>

                <!-- 景点介绍 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">📖 景点介绍</h3>
                    <div class="text-sm text-gray-700 leading-relaxed space-y-3">
                        <p>包河公园位于合肥市包河区，是一座以包拯文化为主题的大型城市公园。公园占地34.5公顷，其中水面15公顷，是合肥市重要的文化旅游景点。</p>
                        
                        <p>公园内主要景点包括包公祠、包公墓、清风阁等，完整地展现了包拯"清心为治本，直道是身谋"的人生理念和为官风范。</p>
                        
                        <p>园内绿树成荫，湖水清澈，春季樱花盛开，秋季桂花飘香，是市民休闲娱乐和了解历史文化的绝佳去处。</p>
                    </div>
                </div>

                <!-- 主要景点 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">🏛️ 主要景点</h3>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">包公祠</div>
                                <div class="text-sm text-gray-600">纪念包拯的祠堂建筑</div>
                            </div>
                            <button class="text-blue-500 text-sm">详情</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">清风阁</div>
                                <div class="text-sm text-gray-600">登高望远的观景建筑</div>
                            </div>
                            <button class="text-blue-500 text-sm">详情</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">包河</div>
                                <div class="text-sm text-gray-600">清澈的湖水环绕公园</div>
                            </div>
                            <button class="text-blue-500 text-sm">详情</button>
                        </div>
                    </div>
                </div>

                <!-- 游览建议 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">💡 游览建议</h3>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-blue-600 text-xs">1</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">最佳游览时间</div>
                                <div class="text-xs text-gray-600">春秋两季，气候宜人，景色优美</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs">2</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">建议游览时长</div>
                                <div class="text-xs text-gray-600">2-3小时，可以充分了解包拯文化</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-purple-600 text-xs">3</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">特色体验</div>
                                <div class="text-xs text-gray-600">参观包公祠，了解包拯生平事迹</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户评价 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-bold text-gray-800">⭐ 用户评价</h3>
                        <div class="flex items-center space-x-1">
                            <span class="text-yellow-500">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600">4.8分</span>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="border-l-4 border-blue-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">旅行达人小王</span>
                                <span class="text-xs text-gray-500">3天前</span>
                            </div>
                            <p class="text-sm text-gray-700">包河公园真的很美，特别是春天樱花盛开的时候。包公祠里的文物展示很丰富，能深入了解包拯的历史。</p>
                        </div>
                        
                        <div class="border-l-4 border-green-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">历史爱好者</span>
                                <span class="text-xs text-gray-500">1周前</span>
                            </div>
                            <p class="text-sm text-gray-700">作为合肥的标志性景点，包河公园承载着深厚的历史文化底蕴。免费开放，值得一游！</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="px-4 pb-20">
                    <div class="grid grid-cols-2 gap-3">
                        <button class="bg-blue-500 text-white py-3 rounded-xl font-medium card-hover">
                            🗺️ 语音导览
                        </button>
                        <button class="bg-green-500 text-white py-3 rounded-xl font-medium card-hover">
                            📅 加入行程
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3 mt-3">
                        <button class="bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                            📍 查看地图
                        </button>
                        <button class="bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                            📞 联系电话
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
