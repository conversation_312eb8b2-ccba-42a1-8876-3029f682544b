<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 合肥创新馆</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 科技感动画 */
        .tech-glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
            to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">合肥创新馆</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">❤️</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📤</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 景点头图 -->
            <div class="relative h-64 -mt-4">
                <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=300&fit=crop&crop=center" 
                     alt="合肥创新馆" 
                     class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-black bg-opacity-30"></div>
                <div class="absolute bottom-4 left-4 text-white">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs">科技</span>
                        <span class="bg-purple-500 text-white px-2 py-1 rounded-full text-xs">4.7⭐</span>
                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">互动</span>
                    </div>
                    <h2 class="text-2xl font-bold mb-1">合肥创新馆</h2>
                    <p class="text-sm opacity-90">科技创新成果展示中心</p>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="px-4 py-6">
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">📍 基本信息</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-2xl mb-1">🎫</div>
                            <div class="text-sm font-medium text-gray-800">门票价格</div>
                            <div class="text-xs text-gray-600">成人30元</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">⏰</div>
                            <div class="text-sm font-medium text-gray-800">开放时间</div>
                            <div class="text-xs text-gray-600">9:00-17:00</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">🚇</div>
                            <div class="text-sm font-medium text-gray-800">交通方式</div>
                            <div class="text-xs text-gray-600">地铁3号线</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-1">📱</div>
                            <div class="text-sm font-medium text-gray-800">预约方式</div>
                            <div class="text-xs text-gray-600">微信预约</div>
                        </div>
                    </div>
                </div>

                <!-- 景点介绍 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">📖 景点介绍</h3>
                    <div class="text-sm text-gray-700 leading-relaxed space-y-3">
                        <p>合肥创新馆位于合肥市滨湖新区，是安徽省首个以创新为主题的场馆。场馆总建筑面积约3万平方米，集展示、体验、教育、交流等功能于一体。</p>
                        
                        <p>场馆以"创新安徽"为主题，全面展示安徽省在科技创新、产业发展、人才培养等方面的成就，是了解安徽创新发展历程的重要窗口。</p>
                        
                        <p>馆内运用VR、AR、全息投影等现代科技手段，为观众提供沉浸式的参观体验，是科普教育和创新文化传播的重要平台。</p>
                    </div>
                </div>

                <!-- 主要展区 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">🔬 主要展区</h3>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg tech-glow">
                            <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">科技创新展区</div>
                                <div class="text-sm text-gray-600">展示前沿科技成果</div>
                            </div>
                            <button class="text-blue-500 text-sm">体验</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1559028006-448665bd7c7f?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">产业发展展区</div>
                                <div class="text-sm text-gray-600">安徽产业转型升级</div>
                            </div>
                            <button class="text-purple-500 text-sm">了解</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">人才创新展区</div>
                                <div class="text-sm text-gray-600">创新人才培养成果</div>
                            </div>
                            <button class="text-green-500 text-sm">探索</button>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">未来科技展区</div>
                                <div class="text-sm text-gray-600">前瞻性科技展望</div>
                            </div>
                            <button class="text-orange-500 text-sm">预览</button>
                        </div>
                    </div>
                </div>

                <!-- 互动体验 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">🎮 互动体验</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-blue-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">🥽</div>
                            <div class="text-sm font-medium text-blue-800">VR体验</div>
                            <div class="text-xs text-blue-600">虚拟现实科技</div>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">📱</div>
                            <div class="text-sm font-medium text-purple-800">AR互动</div>
                            <div class="text-xs text-purple-600">增强现实展示</div>
                        </div>
                        <div class="bg-green-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">🎬</div>
                            <div class="text-sm font-medium text-green-800">全息投影</div>
                            <div class="text-xs text-green-600">立体影像技术</div>
                        </div>
                        <div class="bg-orange-50 rounded-lg p-3 text-center">
                            <div class="text-2xl mb-2">🤖</div>
                            <div class="text-sm font-medium text-orange-800">AI互动</div>
                            <div class="text-xs text-orange-600">人工智能体验</div>
                        </div>
                    </div>
                </div>

                <!-- 参观须知 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <h3 class="font-bold text-gray-800 mb-4">📋 参观须知</h3>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-blue-600 text-xs">1</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">预约参观</div>
                                <div class="text-xs text-gray-600">需提前通过微信公众号预约，每日限流</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs">2</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">身份验证</div>
                                <div class="text-xs text-gray-600">入馆需携带身份证，配合安检</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-purple-600 text-xs">3</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">参观时长</div>
                                <div class="text-xs text-gray-600">建议参观时间2-3小时，深度体验</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-orange-600 text-xs">4</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-800">特殊服务</div>
                                <div class="text-xs text-gray-600">提供免费讲解服务，团体可预约专场</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户评价 -->
                <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-bold text-gray-800">⭐ 用户评价</h3>
                        <div class="flex items-center space-x-1">
                            <span class="text-yellow-500">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600">4.7分</span>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="border-l-4 border-blue-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">科技爱好者</span>
                                <span class="text-xs text-gray-500">1天前</span>
                            </div>
                            <p class="text-sm text-gray-700">创新馆的科技展示非常震撼！VR体验和全息投影技术让人印象深刻，能深入了解安徽的科技发展成就。</p>
                        </div>
                        
                        <div class="border-l-4 border-green-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">教育工作者</span>
                                <span class="text-xs text-gray-500">3天前</span>
                            </div>
                            <p class="text-sm text-gray-700">带学生来参观，孩子们对互动体验项目很感兴趣。这里是很好的科普教育基地，值得推荐！</p>
                        </div>
                        
                        <div class="border-l-4 border-purple-500 pl-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium text-gray-800">创业者</span>
                                <span class="text-xs text-gray-500">1周前</span>
                            </div>
                            <p class="text-sm text-gray-700">从产业发展展区了解到很多创新政策和发展机遇，对创业很有启发。场馆设计现代化，体验感很好。</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="px-4 pb-20">
                    <div class="grid grid-cols-2 gap-3">
                        <button class="bg-blue-500 text-white py-3 rounded-xl font-medium card-hover">
                            📱 在线预约
                        </button>
                        <button class="bg-green-500 text-white py-3 rounded-xl font-medium card-hover">
                            📅 加入行程
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3 mt-3">
                        <button class="bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                            🗺️ 语音导览
                        </button>
                        <button class="bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                            📞 咨询电话
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
