<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 旅游导览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 播放按钮动画 */
        .play-button {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        /* 音频波形动画 */
        .audio-wave {
            display: inline-block;
            width: 4px;
            height: 20px;
            background: #8b5cf6;
            margin: 0 1px;
            animation: wave 1s infinite ease-in-out;
        }
        
        .audio-wave:nth-child(2) { animation-delay: 0.1s; }
        .audio-wave:nth-child(3) { animation-delay: 0.2s; }
        .audio-wave:nth-child(4) { animation-delay: 0.3s; }
        
        @keyframes wave {
            0%, 40%, 100% { transform: scaleY(0.4); }
            20% { transform: scaleY(1); }
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">旅游导览</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🎧</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">⚙️</span>
                    </button>
                </div>
            </div>
            <p class="text-sm opacity-90">智能语音导览，深度了解景点文化</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 当前位置导览 -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl p-6 shadow-sm">
                    <div class="text-center mb-6">
                        <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-4xl">🗺️</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">智能导览</h3>
                        <p class="text-sm text-gray-600 mb-6">基于您的位置提供个性化语音导览服务</p>
                        
                        <!-- 当前位置 -->
                        <div class="bg-purple-50 rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-center space-x-2 mb-2">
                                <span class="text-purple-600">📍</span>
                                <span class="text-sm font-medium text-purple-800">当前位置：包河公园</span>
                            </div>
                            <div class="text-xs text-purple-600">距离您最近的导览点：包公祠 (50米)</div>
                        </div>
                        
                        <div class="space-y-3">
                            <button onclick="startLocationGuide()" class="w-full bg-purple-500 text-white py-3 rounded-xl font-medium card-hover play-button">
                                🎧 开始位置导览
                            </button>
                            <button onclick="showGuideList()" class="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium card-hover">
                                📍 选择景点导览
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 正在播放 -->
            <div id="playingArea" class="px-4 mb-6 hidden">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">🎵 正在播放</h4>
                        <button onclick="stopGuide()" class="text-sm text-red-500">停止</button>
                    </div>
                    
                    <div class="bg-purple-50 rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=60&h=60&fit=crop" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800" id="currentGuideTitle">包河公园历史文化</div>
                                <div class="text-sm text-gray-600" id="currentGuideTime">03:45 / 08:30</div>
                            </div>
                        </div>
                        
                        <!-- 音频波形 -->
                        <div class="flex items-center justify-center space-x-1 mb-4">
                            <div class="audio-wave"></div>
                            <div class="audio-wave"></div>
                            <div class="audio-wave"></div>
                            <div class="audio-wave"></div>
                        </div>
                        
                        <!-- 播放控制 -->
                        <div class="flex items-center justify-center space-x-6">
                            <button onclick="previousGuide()" class="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                                <span class="text-purple-600">⏮️</span>
                            </button>
                            <button onclick="togglePlay()" class="w-12 h-12 bg-purple-500 text-white rounded-full flex items-center justify-center">
                                <span id="playIcon">⏸️</span>
                            </button>
                            <button onclick="nextGuide()" class="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                                <span class="text-purple-600">⏭️</span>
                            </button>
                        </div>
                        
                        <!-- 进度条 -->
                        <div class="mt-4">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门导览 -->
            <div class="px-4 mb-6">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">🔥 热门导览</h4>
                        <button class="text-sm text-purple-500">查看全部</button>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="bg-gray-50 rounded-lg p-4 card-hover" onclick="playGuide('包河公园')">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=60&h=60&fit=crop" 
                                     class="w-12 h-12 rounded-lg object-cover">
                                <div class="flex-1">
                                    <div class="font-medium text-gray-800">包河公园导览</div>
                                    <div class="text-sm text-gray-600">了解包拯文化，探索历史故事</div>
                                    <div class="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                                        <span>⏱️ 8分30秒</span>
                                        <span>👥 2.3k人听过</span>
                                        <span>⭐ 4.8</span>
                                    </div>
                                </div>
                                <button class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 text-sm">▶️</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4 card-hover" onclick="playGuide('三河古镇')">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=60&h=60&fit=crop" 
                                     class="w-12 h-12 rounded-lg object-cover">
                                <div class="flex-1">
                                    <div class="font-medium text-gray-800">三河古镇导览</div>
                                    <div class="text-sm text-gray-600">江南水乡风情，古镇历史文化</div>
                                    <div class="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                                        <span>⏱️ 12分15秒</span>
                                        <span>👥 1.8k人听过</span>
                                        <span>⭐ 4.9</span>
                                    </div>
                                </div>
                                <button class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 text-sm">▶️</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4 card-hover" onclick="playGuide('巢湖风景')">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=60&h=60&fit=crop" 
                                     class="w-12 h-12 rounded-lg object-cover">
                                <div class="flex-1">
                                    <div class="font-medium text-gray-800">巢湖风景导览</div>
                                    <div class="text-sm text-gray-600">五大淡水湖之一，自然风光秀美</div>
                                    <div class="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                                        <span>⏱️ 6分45秒</span>
                                        <span>👥 1.5k人听过</span>
                                        <span>⭐ 4.7</span>
                                    </div>
                                </div>
                                <button class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 text-sm">▶️</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导览设置 -->
            <div class="px-4 mb-20">
                <div class="bg-white rounded-2xl p-4 shadow-sm">
                    <h4 class="font-medium text-gray-800 mb-4">⚙️ 导览设置</h4>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-gray-800">语音速度</div>
                                <div class="text-xs text-gray-600">调整播放速度</div>
                            </div>
                            <select class="bg-gray-100 rounded-lg px-3 py-1 text-sm">
                                <option>0.8x</option>
                                <option selected>1.0x</option>
                                <option>1.2x</option>
                                <option>1.5x</option>
                            </select>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-gray-800">自动播放</div>
                                <div class="text-xs text-gray-600">到达景点时自动开始导览</div>
                            </div>
                            <div class="w-12 h-6 bg-purple-500 rounded-full relative">
                                <div class="w-4 h-4 bg-white rounded-full absolute top-1 right-1 transition-transform"></div>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-gray-800">离线下载</div>
                                <div class="text-xs text-gray-600">下载导览内容离线使用</div>
                            </div>
                            <button class="text-purple-500 text-sm">管理</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        let isPlaying = false;
        let currentGuide = null;
        
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };
            
            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }
        
        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '首页.html';
            }
        }
        
        // 开始位置导览
        function startLocationGuide() {
            playGuide('包河公园', true);
        }
        
        // 显示导览列表
        function showGuideList() {
            alert('显示完整的景点导览列表\n包含合肥所有主要景点的语音导览');
        }
        
        // 播放导览
        function playGuide(guideName, isLocationBased = false) {
            currentGuide = guideName;
            isPlaying = true;
            
            // 显示播放区域
            document.getElementById('playingArea').classList.remove('hidden');
            
            // 更新播放信息
            const guides = {
                '包河公园': {
                    title: '包河公园历史文化',
                    duration: '08:30',
                    image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=60&h=60&fit=crop'
                },
                '三河古镇': {
                    title: '三河古镇水乡风情',
                    duration: '12:15',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=60&h=60&fit=crop'
                },
                '巢湖风景': {
                    title: '巢湖自然风光',
                    duration: '06:45',
                    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=60&h=60&fit=crop'
                }
            };
            
            const guide = guides[guideName] || guides['包河公园'];
            document.getElementById('currentGuideTitle').textContent = guide.title;
            document.getElementById('currentGuideTime').textContent = `00:00 / ${guide.duration}`;
            document.getElementById('playIcon').textContent = '⏸️';
            
            // 模拟播放进度
            simulatePlayback();
            
            if (isLocationBased) {
                alert(`🎧 开始播放位置导览\n正在为您播放：${guide.title}`);
            }
        }
        
        // 停止导览
        function stopGuide() {
            isPlaying = false;
            document.getElementById('playingArea').classList.add('hidden');
            document.getElementById('playIcon').textContent = '▶️';
        }
        
        // 切换播放/暂停
        function togglePlay() {
            isPlaying = !isPlaying;
            document.getElementById('playIcon').textContent = isPlaying ? '⏸️' : '▶️';
        }
        
        // 上一个导览
        function previousGuide() {
            alert('切换到上一个导览内容');
        }
        
        // 下一个导览
        function nextGuide() {
            alert('切换到下一个导览内容');
        }
        
        // 模拟播放进度
        function simulatePlayback() {
            // 这里可以实现真实的音频播放逻辑
            console.log('模拟音频播放进度');
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
