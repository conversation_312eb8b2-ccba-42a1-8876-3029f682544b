<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 我的</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部用户信息 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">我的</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">🔔</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">⚙️</span>
                    </button>
                </div>
            </div>
            
            <!-- 用户信息卡片 -->
            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-4">
                <div class="flex items-center space-x-4 mb-4">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face" 
                         alt="用户头像" 
                         class="w-16 h-16 rounded-full object-cover border-2 border-white border-opacity-50">
                    <div class="flex-1">
                        <h3 class="text-lg font-bold mb-1">旅行达人小王</h3>
                        <p class="text-sm opacity-90">合肥本地向导 · LV.8</p>
                        <div class="flex items-center space-x-3 mt-2 text-xs opacity-80">
                            <span>📍 合肥市</span>
                            <span>🎂 90后</span>
                        </div>
                    </div>
                    <button class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-xs">
                        编辑
                    </button>
                </div>
                
                <!-- 统计数据 -->
                <div class="grid grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-lg font-bold">12</div>
                        <div class="text-xs opacity-80">游记</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold">156</div>
                        <div class="text-xs opacity-80">粉丝</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold">89</div>
                        <div class="text-xs opacity-80">关注</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold">2.3k</div>
                        <div class="text-xs opacity-80">获赞</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 积分与等级 -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl shadow-sm p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🏆</span>
                            <span class="font-medium text-gray-800">积分与等级</span>
                        </div>
                        <button class="text-sm text-blue-500">签到</button>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-2xl font-bold text-blue-600">2,580</div>
                            <div class="text-xs text-gray-600">当前积分</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-800">LV.8 资深旅行者</div>
                            <div class="text-xs text-gray-600">距离下一级还需420积分</div>
                            <div class="w-24 h-2 bg-gray-200 rounded-full mt-1">
                                <div class="w-16 h-2 bg-blue-500 rounded-full"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的服务 -->
            <div class="px-4 mb-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4">我的服务</h3>
                <div class="grid grid-cols-4 gap-4">
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-400 to-red-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">❤️</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">我的收藏</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">📝</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">我的游记</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">📅</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">我的行程</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🎫</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">我的订单</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">💰</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">我的钱包</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-pink-400 to-pink-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🏷️</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">优惠券</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">👥</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">邀请好友</span>
                    </button>
                    
                    <button class="bg-white rounded-2xl p-4 shadow-sm card-hover flex flex-col items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-teal-400 to-teal-600 rounded-xl flex items-center justify-center mb-2">
                            <span class="text-white text-xl">🎁</span>
                        </div>
                        <span class="text-xs text-gray-700 text-center">积分商城</span>
                    </button>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">最近活动</h3>
                    <button class="text-sm text-blue-500">查看全部</button>
                </div>
                
                <div class="space-y-3">
                    <!-- 活动1 -->
                    <div class="bg-white rounded-2xl shadow-sm p-4 card-hover">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 text-sm">📝</span>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">发布了游记《合肥两日游完美攻略》</div>
                                <div class="text-xs text-gray-500 mt-1">获得了156个赞和23条评论</div>
                            </div>
                            <div class="text-xs text-gray-400">2小时前</div>
                        </div>
                    </div>
                    
                    <!-- 活动2 -->
                    <div class="bg-white rounded-2xl shadow-sm p-4 card-hover">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 text-sm">✅</span>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">完成了行程《合肥两日游》</div>
                                <div class="text-xs text-gray-500 mt-1">获得了50积分奖励</div>
                            </div>
                            <div class="text-xs text-gray-400">1天前</div>
                        </div>
                    </div>
                    
                    <!-- 活动3 -->
                    <div class="bg-white rounded-2xl shadow-sm p-4 card-hover">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                <span class="text-orange-600 text-sm">❤️</span>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">收藏了景点《三河古镇》</div>
                                <div class="text-xs text-gray-500 mt-1">添加到了"想去的地方"收藏夹</div>
                            </div>
                            <div class="text-xs text-gray-400">3天前</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设置与帮助 -->
            <div class="px-4 mb-20">
                <h3 class="text-lg font-bold text-gray-800 mb-4">设置与帮助</h3>
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <!-- 设置项目 -->
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100 card-hover">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">🔔</span>
                            <span class="text-sm text-gray-800">消息通知</span>
                        </div>
                        <span class="text-gray-400">›</span>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100 card-hover">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">🔒</span>
                            <span class="text-sm text-gray-800">隐私设置</span>
                        </div>
                        <span class="text-gray-400">›</span>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100 card-hover">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">🌙</span>
                            <span class="text-sm text-gray-800">深色模式</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-500">关闭</span>
                            <div class="w-10 h-6 bg-gray-200 rounded-full relative">
                                <div class="w-4 h-4 bg-white rounded-full absolute top-1 left-1 transition-transform"></div>
                            </div>
                        </div>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100 card-hover">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">❓</span>
                            <span class="text-sm text-gray-800">帮助与反馈</span>
                        </div>
                        <span class="text-gray-400">›</span>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100 card-hover">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">📋</span>
                            <span class="text-sm text-gray-800">关于我们</span>
                        </div>
                        <span class="text-gray-400">›</span>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 card-hover">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">🚪</span>
                            <span class="text-sm text-gray-800">退出登录</span>
                        </div>
                        <span class="text-gray-400">›</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-gray-400 text-xl mb-1">📅</span>
                    <span class="text-xs text-gray-400">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-blue-500 text-xl mb-1">👤</span>
                    <span class="text-xs text-blue-500 font-medium">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };

            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }

        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，返回首页
                window.location.href = '首页.html';
            }
        }

        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
