<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 行程</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 时间轴样式 */
        .timeline-item {
            position: relative;
            padding-left: 2rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }
        .timeline-item:last-child::before {
            display: none;
        }
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: #3b82f6;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-xl font-bold">我的行程</h1>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📅</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">➕</span>
                    </button>
                </div>
            </div>
            
            <!-- 快速规划 -->
            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium">🚀 智能规划</span>
                    <span class="text-xs opacity-80">个性定制</span>
                </div>
                <button class="w-full bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 rounded-xl py-3 text-center">
                    <span class="text-sm font-medium">创建新行程</span>
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 当前行程 -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <!-- 行程头部 -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-bold">合肥两日游</h3>
                            <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">进行中</span>
                        </div>
                        <div class="flex items-center space-x-4 text-sm opacity-90">
                            <span>📅 4月15日-4月16日</span>
                            <span>👥 2人</span>
                            <span>💰 ¥680</span>
                        </div>
                    </div>
                    
                    <!-- 今日行程 -->
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-medium text-gray-800">今日行程 - Day 1</h4>
                            <span class="text-xs text-blue-500">编辑</span>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- 时间轴项目1 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-green-500" style="box-shadow: 0 0 0 2px #10b981;"></div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">包公园</span>
                                        <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已完成</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 09:00-11:00 | 📍 2.3km | 🎫 免费</div>
                                    <p class="text-xs text-gray-500">感受包拯文化的历史底蕴</p>
                                </div>
                            </div>
                            
                            <!-- 时间轴项目2 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-blue-500"></div>
                                <div class="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-500">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">逍遥津公园</span>
                                        <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">进行中</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 11:30-13:00 | 📍 3.1km | 🎫 免费</div>
                                    <p class="text-xs text-gray-500">三国历史文化主题公园</p>
                                    <div class="flex items-center space-x-2 mt-2">
                                        <button class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">导航</button>
                                        <button class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full">详情</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 时间轴项目3 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-gray-300" style="box-shadow: 0 0 0 2px #d1d5db;"></div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">李鸿章故居</span>
                                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">待开始</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 14:00-16:00 | 📍 1.8km | 🎫 ¥20</div>
                                    <p class="text-xs text-gray-500">晚清名臣李鸿章的故居</p>
                                </div>
                            </div>
                            
                            <!-- 时间轴项目4 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-gray-300" style="box-shadow: 0 0 0 2px #d1d5db;"></div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">淮河路步行街</span>
                                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">待开始</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 18:00-21:00 | 📍 2.5km | 🎫 免费</div>
                                    <p class="text-xs text-gray-500">品尝合肥特色美食</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 明日预览 -->
            <div class="px-4 mb-6">
                <div class="bg-white rounded-2xl shadow-sm p-4">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">明日预览 - Day 2</h4>
                        <span class="text-xs text-blue-500">查看详情</span>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=60&h=60&fit=crop" 
                                 alt="三河古镇" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">三河古镇</div>
                                <div class="text-xs text-gray-600">09:00-15:00 | 45km | ¥80</div>
                            </div>
                            <span class="text-xs text-gray-500">全天</span>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=60&h=60&fit=crop" 
                                 alt="巢湖" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">巢湖风景区</div>
                                <div class="text-xs text-gray-600">16:00-18:00 | 35km | 免费</div>
                            </div>
                            <span class="text-xs text-gray-500">下午</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 历史行程 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">历史行程</h3>
                    <button class="text-sm text-blue-500">查看全部</button>
                </div>
                
                <div class="space-y-3">
                    <!-- 历史行程1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=80&h=80&fit=crop" 
                                 alt="春游" 
                                 class="w-20 h-20 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h4 class="font-medium text-gray-800">合肥春游之旅</h4>
                                    <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已完成</span>
                                </div>
                                <div class="text-xs text-gray-600 mb-2">3月20日-3月21日 | 2天1夜</div>
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>📍 5个景点</span>
                                    <span>💰 ¥520</span>
                                    <span>⭐ 4.8分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史行程2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1414016642750-7fdd78dc33d9?w=80&h=80&fit=crop" 
                                 alt="美食" 
                                 class="w-20 h-20 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h4 class="font-medium text-gray-800">合肥美食探索</h4>
                                    <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已完成</span>
                                </div>
                                <div class="text-xs text-gray-600 mb-2">2月14日 | 1天</div>
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>🍜 8家餐厅</span>
                                    <span>💰 ¥280</span>
                                    <span>⭐ 4.6分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐行程 -->
            <div class="px-4 mb-20">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">推荐行程</h3>
                    <button class="text-sm text-blue-500">更多推荐</button>
                </div>
                
                <div class="space-y-4">
                    <!-- 推荐行程1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=120&fit=crop" 
                             alt="推荐行程" 
                             class="w-full h-24 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="font-medium text-gray-800">合肥文化深度游</h4>
                                <span class="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full">热门</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">包公园→李鸿章故居→安徽博物院→逍遥津，感受合肥深厚的历史文化底蕴</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>⏰ 1天</span>
                                    <span>💰 ¥120</span>
                                    <span>⭐ 4.7</span>
                                </div>
                                <button class="text-blue-500 text-xs bg-blue-50 px-3 py-1 rounded-full">使用模板</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 推荐行程2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=120&fit=crop" 
                             alt="推荐行程" 
                             class="w-full h-24 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="font-medium text-gray-800">亲子科技探索之旅</h4>
                                <span class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">亲子</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">科技馆→野生动物园→植物园，寓教于乐的亲子游体验</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>⏰ 1天</span>
                                    <span>💰 ¥180</span>
                                    <span>⭐ 4.9</span>
                                </div>
                                <button class="text-blue-500 text-xs bg-blue-50 px-3 py-1 rounded-full">使用模板</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-blue-500 text-xl mb-1">📅</span>
                    <span class="text-xs text-blue-500 font-medium">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
