<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅 - 行程</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条但保持滚动功能 */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        
        /* 自定义渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:active {
            transform: scale(0.98);
        }
        
        /* 安全区域适配 */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 时间轴样式 */
        .timeline-item {
            position: relative;
            padding-left: 2rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }
        .timeline-item:last-child::before {
            display: none;
        }
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: #3b82f6;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="gradient-bg text-white px-4 pt-12 pb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <!-- 返回按钮 -->
                    <button onclick="goBack()" class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-xl">←</span>
                    </button>
                    <h1 class="text-xl font-bold">我的行程</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">📅</span>
                    </button>
                    <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-sm">➕</span>
                    </button>
                </div>
            </div>
            
            <!-- 快速规划 -->
            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium">🚀 智能规划</span>
                    <span class="text-xs opacity-80">个性定制</span>
                </div>
                <button class="w-full bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 rounded-xl py-3 text-center" onclick="showCreateItinerary()">
                    <span class="text-sm font-medium">创建新行程</span>
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 hide-scrollbar overflow-y-auto">
            <!-- 当前行程 -->
            <div class="px-4 -mt-4 mb-6">
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <!-- 行程头部 -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-bold">合肥两日游</h3>
                            <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">进行中</span>
                        </div>
                        <div class="flex items-center space-x-4 text-sm opacity-90">
                            <span>📅 4月15日-4月16日</span>
                            <span>👥 2人</span>
                            <span>💰 ¥680</span>
                        </div>
                    </div>
                    
                    <!-- 今日行程 -->
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-medium text-gray-800">今日行程 - Day 1</h4>
                            <span class="text-xs text-blue-500">编辑</span>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- 时间轴项目1 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-green-500" style="box-shadow: 0 0 0 2px #10b981;"></div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">包公园</span>
                                        <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已完成</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 09:00-11:00 | 📍 2.3km | 🎫 免费</div>
                                    <p class="text-xs text-gray-500">感受包拯文化的历史底蕴</p>
                                </div>
                            </div>
                            
                            <!-- 时间轴项目2 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-blue-500"></div>
                                <div class="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-500">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">逍遥津公园</span>
                                        <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">进行中</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 11:30-13:00 | 📍 3.1km | 🎫 免费</div>
                                    <p class="text-xs text-gray-500">三国历史文化主题公园</p>
                                    <div class="flex items-center space-x-2 mt-2">
                                        <button class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">导航</button>
                                        <button class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full">详情</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 时间轴项目3 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-gray-300" style="box-shadow: 0 0 0 2px #d1d5db;"></div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">李鸿章故居</span>
                                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">待开始</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 14:00-16:00 | 📍 1.8km | 🎫 ¥20</div>
                                    <p class="text-xs text-gray-500">晚清名臣李鸿章的故居</p>
                                </div>
                            </div>
                            
                            <!-- 时间轴项目4 -->
                            <div class="timeline-item">
                                <div class="timeline-dot bg-gray-300" style="box-shadow: 0 0 0 2px #d1d5db;"></div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-800">淮河路步行街</span>
                                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">待开始</span>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2">⏰ 18:00-21:00 | 📍 2.5km | 🎫 免费</div>
                                    <p class="text-xs text-gray-500">品尝合肥特色美食</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 明日预览 -->
            <div class="px-4 mb-6">
                <div class="bg-white rounded-2xl shadow-sm p-4">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-800">明日预览 - Day 2</h4>
                        <span class="text-xs text-blue-500">查看详情</span>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=60&h=60&fit=crop" 
                                 alt="三河古镇" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">三河古镇</div>
                                <div class="text-xs text-gray-600">09:00-15:00 | 45km | ¥80</div>
                            </div>
                            <span class="text-xs text-gray-500">全天</span>
                        </div>
                        
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=60&h=60&fit=crop" 
                                 alt="巢湖" 
                                 class="w-12 h-12 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">巢湖风景区</div>
                                <div class="text-xs text-gray-600">16:00-18:00 | 35km | 免费</div>
                            </div>
                            <span class="text-xs text-gray-500">下午</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 历史行程 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">历史行程</h3>
                    <button class="text-sm text-blue-500">查看全部</button>
                </div>
                
                <div class="space-y-3">
                    <!-- 历史行程1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=80&h=80&fit=crop" 
                                 alt="春游" 
                                 class="w-20 h-20 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h4 class="font-medium text-gray-800">合肥春游之旅</h4>
                                    <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已完成</span>
                                </div>
                                <div class="text-xs text-gray-600 mb-2">3月20日-3月21日 | 2天1夜</div>
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>📍 5个景点</span>
                                    <span>💰 ¥520</span>
                                    <span>⭐ 4.8分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史行程2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1414016642750-7fdd78dc33d9?w=80&h=80&fit=crop" 
                                 alt="美食" 
                                 class="w-20 h-20 object-cover">
                            <div class="flex-1 p-3">
                                <div class="flex items-start justify-between mb-2">
                                    <h4 class="font-medium text-gray-800">合肥美食探索</h4>
                                    <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已完成</span>
                                </div>
                                <div class="text-xs text-gray-600 mb-2">2月14日 | 1天</div>
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>🍜 8家餐厅</span>
                                    <span>💰 ¥280</span>
                                    <span>⭐ 4.6分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐行程 -->
            <div class="px-4 mb-20">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">推荐行程</h3>
                    <button class="text-sm text-blue-500">更多推荐</button>
                </div>
                
                <div class="space-y-4">
                    <!-- 推荐行程1 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=120&fit=crop" 
                             alt="推荐行程" 
                             class="w-full h-24 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="font-medium text-gray-800">合肥文化深度游</h4>
                                <span class="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full">热门</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">包公园→李鸿章故居→安徽博物院→逍遥津，感受合肥深厚的历史文化底蕴</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>⏰ 1天</span>
                                    <span>💰 ¥120</span>
                                    <span>⭐ 4.7</span>
                                </div>
                                <button class="text-blue-500 text-xs bg-blue-50 px-3 py-1 rounded-full">使用模板</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 推荐行程2 -->
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden card-hover">
                        <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=120&fit=crop" 
                             alt="推荐行程" 
                             class="w-full h-24 object-cover">
                        <div class="p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="font-medium text-gray-800">亲子科技探索之旅</h4>
                                <span class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">亲子</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">科技馆→野生动物园→植物园，寓教于乐的亲子游体验</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <span>⏰ 1天</span>
                                    <span>💰 ¥180</span>
                                    <span>⭐ 4.9</span>
                                </div>
                                <button class="text-blue-500 text-xs bg-blue-50 px-3 py-1 rounded-full">使用模板</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bg-white border-t border-gray-200 safe-area-bottom">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('首页')">
                    <span class="text-gray-400 text-xl mb-1">🏠</span>
                    <span class="text-xs text-gray-400">首页</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('探索')">
                    <span class="text-gray-400 text-xl mb-1">🔍</span>
                    <span class="text-xs text-gray-400">探索</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('社区')">
                    <span class="text-gray-400 text-xl mb-1">👥</span>
                    <span class="text-xs text-gray-400">社区</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('行程')">
                    <span class="text-blue-500 text-xl mb-1">📅</span>
                    <span class="text-xs text-blue-500 font-medium">行程</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3" onclick="navigateToPage('我的')">
                    <span class="text-gray-400 text-xl mb-1">👤</span>
                    <span class="text-xs text-gray-400">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 创建新行程模态框 -->
    <div id="createItineraryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-xl max-w-md w-full max-h-screen overflow-y-auto">
                <!-- 模态框头部 -->
                <div class="gradient-bg text-white p-6 rounded-t-2xl">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-bold">创建新行程</h3>
                        <button onclick="hideCreateItinerary()" class="text-white opacity-80 hover:opacity-100">
                            <span class="text-2xl">×</span>
                        </button>
                    </div>
                    <p class="text-sm opacity-90 mt-2">告诉我们您的偏好，AI为您定制专属行程</p>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6 space-y-6">
                    <!-- 旅行时长 -->
                    <div>
                        <label class="text-sm font-medium text-gray-800 mb-3 block">🕐 旅行时长</label>
                        <div class="grid grid-cols-3 gap-2">
                            <button class="duration-btn py-2 px-3 bg-blue-50 text-blue-600 rounded-lg text-sm font-medium border border-blue-200" data-value="半天">半天</button>
                            <button class="duration-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="1天">1天</button>
                            <button class="duration-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="2天">2天</button>
                            <button class="duration-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="3天">3天</button>
                            <button class="duration-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="一周">一周</button>
                            <button class="duration-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="自定义">自定义</button>
                        </div>
                    </div>

                    <!-- 旅游目的 -->
                    <div>
                        <label class="text-sm font-medium text-gray-800 mb-3 block">🎯 旅游目的</label>
                        <div class="grid grid-cols-2 gap-2">
                            <button class="purpose-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="休闲放松">休闲放松</button>
                            <button class="purpose-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="文化体验">文化体验</button>
                            <button class="purpose-btn py-2 px-3 bg-purple-50 text-purple-600 rounded-lg text-sm font-medium border border-purple-200" data-value="美食探索">美食探索</button>
                            <button class="purpose-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="自然风光">自然风光</button>
                            <button class="purpose-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="亲子游玩">亲子游玩</button>
                            <button class="purpose-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="拍照打卡">拍照打卡</button>
                        </div>
                    </div>

                    <!-- 同行人员 -->
                    <div>
                        <label class="text-sm font-medium text-gray-800 mb-3 block">👥 同行人员</label>
                        <div class="grid grid-cols-3 gap-2">
                            <button class="companion-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="单人">单人</button>
                            <button class="companion-btn py-2 px-3 bg-pink-50 text-pink-600 rounded-lg text-sm font-medium border border-pink-200" data-value="情侣">情侣</button>
                            <button class="companion-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="家庭">家庭</button>
                            <button class="companion-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="朋友">朋友</button>
                            <button class="companion-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="团队">团队</button>
                            <button class="companion-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="其他">其他</button>
                        </div>
                    </div>

                    <!-- 预算范围 -->
                    <div>
                        <label class="text-sm font-medium text-gray-800 mb-3 block">💰 预算范围（每人）</label>
                        <div class="grid grid-cols-2 gap-2">
                            <button class="budget-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="100以下">¥100以下</button>
                            <button class="budget-btn py-2 px-3 bg-green-50 text-green-600 rounded-lg text-sm font-medium border border-green-200" data-value="100-300">¥100-300</button>
                            <button class="budget-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="300-500">¥300-500</button>
                            <button class="budget-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="500-1000">¥500-1000</button>
                            <button class="budget-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="1000以上">¥1000以上</button>
                            <button class="budget-btn py-2 px-3 bg-gray-100 text-gray-600 rounded-lg text-sm border border-gray-200" data-value="不限">不限</button>
                        </div>
                    </div>

                    <!-- 特殊需求 -->
                    <div>
                        <label class="text-sm font-medium text-gray-800 mb-3 block">✨ 特殊需求（可选）</label>
                        <textarea class="w-full p-3 border border-gray-200 rounded-lg text-sm resize-none"
                                  rows="3"
                                  placeholder="如：无障碍设施、素食餐厅、儿童友好等特殊需求..."></textarea>
                    </div>
                </div>

                <!-- 模态框底部 -->
                <div class="p-6 border-t border-gray-100">
                    <div class="flex space-x-3">
                        <button onclick="hideCreateItinerary()" class="flex-1 py-3 text-gray-600 bg-gray-100 rounded-xl font-medium">
                            取消
                        </button>
                        <button onclick="generateItinerary()" class="flex-1 py-3 text-white bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl font-medium">
                            🚀 AI生成行程
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成中模态框 -->
    <div id="generatingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-xl p-8 text-center max-w-sm w-full">
                <div class="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <h3 class="text-lg font-bold text-gray-800 mb-2">AI正在生成行程</h3>
                <p class="text-sm text-gray-600">根据您的偏好定制专属行程...</p>
                <div class="mt-4 text-xs text-gray-500">
                    <div id="generatingText">分析您的需求...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageName) {
            const pageMap = {
                '首页': '首页.html',
                '探索': '探索.html',
                '社区': '社区.html',
                '行程': '行程.html',
                '我的': '我的.html'
            };

            if (pageMap[pageName]) {
                window.location.href = pageMap[pageName];
            }
        }

        // 返回上一页功能
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，返回首页
                window.location.href = '首页.html';
            }
        }

        // 显示创建新行程模态框
        function showCreateItinerary() {
            document.getElementById('createItineraryModal').classList.remove('hidden');
        }

        // 隐藏创建新行程模态框
        function hideCreateItinerary() {
            document.getElementById('createItineraryModal').classList.add('hidden');
        }

        // 选择按钮功能
        function setupSelectionButtons() {
            // 旅行时长选择
            const durationBtns = document.querySelectorAll('.duration-btn');
            durationBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    durationBtns.forEach(b => {
                        b.classList.remove('bg-blue-50', 'text-blue-600', 'border-blue-200', 'font-medium');
                        b.classList.add('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    });
                    this.classList.remove('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    this.classList.add('bg-blue-50', 'text-blue-600', 'border-blue-200', 'font-medium');
                });
            });

            // 旅游目的选择
            const purposeBtns = document.querySelectorAll('.purpose-btn');
            purposeBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    purposeBtns.forEach(b => {
                        b.classList.remove('bg-purple-50', 'text-purple-600', 'border-purple-200', 'font-medium');
                        b.classList.add('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    });
                    this.classList.remove('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    this.classList.add('bg-purple-50', 'text-purple-600', 'border-purple-200', 'font-medium');
                });
            });

            // 同行人员选择
            const companionBtns = document.querySelectorAll('.companion-btn');
            companionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    companionBtns.forEach(b => {
                        b.classList.remove('bg-pink-50', 'text-pink-600', 'border-pink-200', 'font-medium');
                        b.classList.add('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    });
                    this.classList.remove('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    this.classList.add('bg-pink-50', 'text-pink-600', 'border-pink-200', 'font-medium');
                });
            });

            // 预算范围选择
            const budgetBtns = document.querySelectorAll('.budget-btn');
            budgetBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    budgetBtns.forEach(b => {
                        b.classList.remove('bg-green-50', 'text-green-600', 'border-green-200', 'font-medium');
                        b.classList.add('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    });
                    this.classList.remove('bg-gray-100', 'text-gray-600', 'border-gray-200');
                    this.classList.add('bg-green-50', 'text-green-600', 'border-green-200', 'font-medium');
                });
            });
        }

        // 生成行程
        function generateItinerary() {
            // 获取选择的值
            const duration = document.querySelector('.duration-btn.font-medium')?.dataset.value || '1天';
            const purpose = document.querySelector('.purpose-btn.font-medium')?.dataset.value || '休闲放松';
            const companion = document.querySelector('.companion-btn.font-medium')?.dataset.value || '情侣';
            const budget = document.querySelector('.budget-btn.font-medium')?.dataset.value || '100-300';
            const specialNeeds = document.querySelector('textarea').value;

            // 隐藏创建模态框，显示生成中模态框
            hideCreateItinerary();
            document.getElementById('generatingModal').classList.remove('hidden');

            // 模拟AI生成过程
            const steps = [
                '分析您的需求...',
                '匹配合适景点...',
                '规划最佳路线...',
                '计算时间安排...',
                '生成详细行程...'
            ];

            let currentStep = 0;
            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    document.getElementById('generatingText').textContent = steps[currentStep];
                    currentStep++;
                } else {
                    clearInterval(interval);
                    // 生成完成，隐藏模态框并显示结果
                    setTimeout(() => {
                        document.getElementById('generatingModal').classList.add('hidden');
                        showGeneratedItinerary(duration, purpose, companion, budget, specialNeeds);
                    }, 1000);
                }
            }, 800);
        }

        // 显示生成的行程
        function showGeneratedItinerary(duration, purpose, companion, budget, specialNeeds) {
            alert(`🎉 行程生成成功！\n\n📅 时长：${duration}\n🎯 目的：${purpose}\n👥 人员：${companion}\n💰 预算：${budget}\n\n您的专属合肥${duration}游行程已生成，包含精选景点和美食推荐！`);
            // 这里可以跳转到新生成的行程详情页面
        }

        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            setupSelectionButtons();

            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.7';
                });
                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });
            });

            // 点击模态框外部关闭
            document.getElementById('createItineraryModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideCreateItinerary();
                }
            });
        });
    </script>
</body>
</html>
