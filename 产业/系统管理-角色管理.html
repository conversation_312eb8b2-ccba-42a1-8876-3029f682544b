<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 角色管理 | 产业大脑系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        background: '#f8fafc',
                        sidebar: '#1e293b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 h-screen flex overflow-hidden">
    <!-- 侧边栏 -->
    <aside class="bg-sidebar w-64 text-white flex flex-col">
        <!-- 系统标题 -->
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold">产业大脑系统</h1>
            <p class="text-sm text-gray-400">管理后台</p>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="flex-1 overflow-y-auto py-4">
            <ul class="space-y-1">
                <li class="px-4">
                    <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        <span>首页</span>
                    </a>
                </li>
                
                <!-- 企业管理 -->
                <li class="px-4 mt-6">
                    <h2 class="text-xs uppercase tracking-wide text-gray-400 font-semibold mb-2">企业管理</h2>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                                </svg>
                                <span>企业登记</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
                                    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
                                </svg>
                                <span>企业云图</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- 产业管理 -->
                <li class="px-4 mt-6">
                    <h2 class="text-xs uppercase tracking-wide text-gray-400 font-semibold mb-2">产业管理</h2>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                </svg>
                                <span>产业链管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                                </svg>
                                <span>产业资源管理</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- 项目管理 -->
                <li class="px-4 mt-6">
                    <h2 class="text-xs uppercase tracking-wide text-gray-400 font-semibold mb-2">项目管理</h2>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                                </svg>
                                <span>招商项目管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>重点项目管理</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- 产业招商 -->
                <li class="px-4 mt-6">
                    <h2 class="text-xs uppercase tracking-wide text-gray-400 font-semibold mb-2">产业招商</h2>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z" clip-rule="evenodd" />
                                </svg>
                                <span>招商企业地图</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                                </svg>
                                <span>招商企业档案</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- 产业运营 -->
                <li class="px-4 mt-6">
                    <h2 class="text-xs uppercase tracking-wide text-gray-400 font-semibold mb-2">产业运营</h2>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                                </svg>
                                <span>产业经济看板</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                                </svg>
                                <span>产业图谱</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                <span>产业预警</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                                </svg>
                                <span>产业报告</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- 系统管理 -->
                <li class="px-4 mt-6">
                    <h2 class="text-xs uppercase tracking-wide text-gray-400 font-semibold mb-2">系统管理</h2>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                </svg>
                                <span>账号管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg bg-blue-700 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v1h8v-1zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-1a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v1h-3zM4.75 12.094A5.973 5.973 0 004 15v1H1v-1a3 3 0 013.75-2.906z" />
                                </svg>
                                <span>角色管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                </svg>
                                <span>日志管理</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
        
        <!-- 用户信息 -->
        <div class="p-4 border-t border-gray-700">
            <div class="flex items-center">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="w-8 h-8 rounded-full mr-3">
                <div>
                    <p class="text-sm font-medium">管理员</p>
                    <p class="text-xs text-gray-400"><EMAIL></p>
                </div>
            </div>
        </div>
    </aside>

    <!-- 主内容区 -->
    <main class="flex-1 flex overflow-hidden">
        <!-- 左侧角色列表 -->
        <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
            <!-- 角色列表头部 -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">角色列表</h2>
                    <button class="bg-blue-600 text-white rounded-md px-3 py-1 text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        新增角色
                    </button>
                </div>
                <div class="mt-3">
                    <input type="text" placeholder="搜索角色..." class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
            </div>

            <!-- 角色列表内容 -->
            <div class="flex-1 overflow-y-auto">
                <!-- 超级管理员角色 -->
                <div class="p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 bg-blue-50 border-l-4 border-l-blue-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">超级管理员</h3>
                            <p class="text-xs text-gray-500 mt-1">系统最高权限，可管理所有功能</p>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">系统角色</span>
                                <span class="text-xs text-gray-500 ml-2">1个用户</span>
                            </div>
                        </div>
                        <div class="text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 系统管理员角色 -->
                <div class="p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">系统管理员</h3>
                            <p class="text-xs text-gray-500 mt-1">管理系统配置和用户权限</p>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">系统角色</span>
                                <span class="text-xs text-gray-500 ml-2">3个用户</span>
                            </div>
                        </div>
                        <div class="text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 业务管理员角色 -->
                <div class="p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">业务管理员</h3>
                            <p class="text-xs text-gray-500 mt-1">管理业务数据和流程</p>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">业务角色</span>
                                <span class="text-xs text-gray-500 ml-2">15个用户</span>
                            </div>
                        </div>
                        <div class="text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 普通用户角色 -->
                <div class="p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">普通用户</h3>
                            <p class="text-xs text-gray-500 mt-1">查看和使用基础功能</p>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">业务角色</span>
                                <span class="text-xs text-gray-500 ml-2">26个用户</span>
                            </div>
                        </div>
                        <div class="text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧权限配置 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm z-10">
                <div class="flex items-center justify-between h-16 px-6">
                    <div class="flex items-center">
                        <nav class="flex items-center space-x-2 text-sm">
                            <a href="#" class="text-gray-500 hover:text-gray-700">系统管理</a>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span class="text-gray-900 font-medium">角色管理</span>
                        </nav>
                    </div>

                    <div class="flex items-center space-x-4">
                        <button class="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            保存权限
                        </button>
                        <button class="bg-gray-300 text-gray-700 rounded-md px-4 py-2 text-sm font-medium hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            重置
                        </button>
                    </div>
                </div>
            </header>

            <!-- 权限配置内容 -->
            <div class="flex-1 overflow-auto p-6 bg-gray-50">
                <!-- 角色信息 -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">超级管理员</h2>
                            <p class="text-sm text-gray-600 mt-1">系统最高权限，可管理所有功能模块</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">系统角色</span>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑角色</button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">角色名称</label>
                            <input type="text" value="超级管理员" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">角色类型</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option selected>系统角色</option>
                                <option>业务角色</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option selected>启用</option>
                                <option>禁用</option>
                            </select>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">角色描述</label>
                        <textarea rows="2" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入角色描述...">系统最高权限角色，拥有所有功能模块的完整访问和管理权限，包括用户管理、系统配置、数据管理等。</textarea>
                    </div>
                </div>

                <!-- 权限配置 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">权限配置</h3>
                        <div class="flex items-center space-x-4">
                            <button class="text-blue-600 hover:text-blue-800 text-sm">全选</button>
                            <button class="text-gray-500 hover:text-gray-700 text-sm">全不选</button>
                            <button class="text-green-600 hover:text-green-800 text-sm">展开全部</button>
                        </div>
                    </div>

                    <!-- 权限树 -->
                    <div class="space-y-4">
                        <!-- 企业管理权限 -->
                        <div class="border border-gray-200 rounded-lg">
                            <div class="p-4 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <span class="font-medium text-gray-900">企业管理</span>
                                    <span class="ml-2 text-sm text-gray-500">(2/2)</span>
                                </div>
                            </div>
                            <div class="p-4 space-y-3">
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">企业登记</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">新增</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">企业云图</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 产业管理权限 -->
                        <div class="border border-gray-200 rounded-lg">
                            <div class="p-4 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                    </svg>
                                    <span class="font-medium text-gray-900">产业管理</span>
                                    <span class="ml-2 text-sm text-gray-500">(2/2)</span>
                                </div>
                            </div>
                            <div class="p-4 space-y-3">
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">产业链管理</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">新增</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">产业资源管理</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">新增</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 项目管理权限 -->
                        <div class="border border-gray-200 rounded-lg">
                            <div class="p-4 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="font-medium text-gray-900">项目管理</span>
                                    <span class="ml-2 text-sm text-gray-500">(2/2)</span>
                                </div>
                            </div>
                            <div class="p-4 space-y-3">
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">招商项目管理</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">新增</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">重点项目管理</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">新增</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 系统管理权限 -->
                        <div class="border border-gray-200 rounded-lg">
                            <div class="p-4 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="font-medium text-gray-900">系统管理</span>
                                    <span class="ml-2 text-sm text-gray-500">(3/3)</span>
                                </div>
                            </div>
                            <div class="p-4 space-y-3">
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">账号管理</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">新增</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">角色管理</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">新增</span>
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">编辑</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                                <div class="flex items-center pl-6">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    <span class="text-sm text-gray-700">日志管理</span>
                                    <div class="ml-auto flex space-x-2">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">查看</span>
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">删除</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
