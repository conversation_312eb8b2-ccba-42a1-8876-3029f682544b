
二、单一景点应用（以包公园、融创乐园等为例 ）



### （一）简单景点（以博物馆等为例 ）&#xA;



1.  **信息展示**：通过 banner、图文等形式，展示景点介绍、景区地图、主要展区、游览信息等 。


2.  **智能导览**：支持游园导览、景点介绍，基于 LBS 推送周边关联资源（如停车场、美食、就餐点 ） 。


3.  **旅游导览**

*   **智能问答**：提供游园攻略、景点介绍问答服务 。


*   **代客停车**：关联停车场服务，支持预约、查询停车状态 。


1.  **门票预约**：提供线上门票预约功能，可选择预约时段、数量，关联订单管理 。


### （二）复杂景点（以综合性公园等为例 ，如融创乐园 ）&#xA;



1.  **首页**

*   展示景点介绍（含地图、景区、设施 ）、游玩攻略（推荐路线、热门活动 ）、特色景点（如北区景点、打卡点 ） 。


*   关联智能导览、预约停车、伴手礼推荐、套票联票等服务 。


1.  **探索**

*   分景点、活动、美食、游记、攻略板块，展示公园内对应资源，支持查询、预订、创作分享 。


1.  **社区**

*   支持用户创作、分享关于该景点的游记、攻略，经审核后上架展示，作者获积分 。


1.  **行程**

*   同市级行程规划逻辑，支持景点内行程定制（选时长、人数、类型 ），自动规划路径，行程可收藏、支付 。


*   展示行程列表（含规划中、已完成 ），支持行程调整（修改、删除、交换执行顺序 ） 。