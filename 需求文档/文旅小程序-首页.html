<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        /* 确保页面可以正常滚动 */
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        /* 底部安全区域适配 */
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }

        /* 隐藏滚动条但保持滚动功能 */
        body::-webkit-scrollbar {
            display: none;
        }

        body {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        /* 虚拟人动画 */
        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .chat-bubble {
            animation: fadeInOut 4s ease-in-out infinite;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: scale(0.8); }
            25%, 75% { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部状态栏和搜索 -->
    <header class="bg-white px-4 py-3 shadow-sm">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-lg font-bold text-gray-900">合肥文旅</h1>
                    <p class="text-xs text-gray-500">智能旅游助手</p>
                </div>
            </div>
            <button class="p-2 text-gray-400">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                </svg>
            </button>
        </div>
        
        <!-- 搜索框 -->
        <div class="relative">
            <input type="text" placeholder="搜索合肥景点、酒店、美食..."
                   class="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white">
            <svg class="absolute left-3 top-3.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="px-4 py-4 space-y-6 pb-24">
        <!-- 热门景点轮播 -->
        <section>
            <div onclick="openChaohuScenic()" class="relative h-52 rounded-2xl overflow-hidden cursor-pointer hover:shadow-lg transition-shadow">
                <img src="https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400&h=220&fit=crop&crop=center"
                     alt="巢湖风景区" class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div class="absolute bottom-4 left-4 text-white">
                    <h3 class="text-xl font-bold mb-1">巢湖风景区</h3>
                    <p class="text-sm opacity-90">八百里巢湖，烟波浩渺</p>
                    <div class="flex items-center mt-2 space-x-2">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <span class="text-sm">4.7</span>
                        </div>
                        <span class="text-sm opacity-75">免费</span>
                    </div>
                </div>
                <div class="absolute top-4 right-4">
                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">热门</span>
                </div>
            </div>
        </section>

        <!-- 快速入口 -->
        <section>
            <h2 class="text-lg font-semibold text-gray-900 mb-4">快速服务</h2>
            <div class="grid grid-cols-4 gap-4">
                <button onclick="openAIPlanning()" class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-700">智能规划</span>
                </button>
                
                <button class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm">
                    <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-700">酒店预订</span>
                </button>
                
                <button class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm">
                    <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 10h6m-6 4h6"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-700">门票预订</span>
                </button>
                
                <button class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm">
                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-700">语音导游</span>
                </button>
            </div>
        </section>

        <!-- 推荐活动 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">推荐活动</h2>
                <button class="text-sm text-primary">查看更多</button>
            </div>
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <div class="flex space-x-3">
                    <img src="https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=80&h=80&fit=crop&crop=center"
                         alt="巢湖" class="w-20 h-20 rounded-xl object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 mb-1">巢湖春季踏青节</h3>
                        <p class="text-sm text-gray-500 mb-2">环湖骑行，感受春日巢湖美景</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-red-500 font-medium">¥88起</span>
                            <button class="bg-primary text-white text-sm px-4 py-1 rounded-full">立即预订</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 热门景点 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">热门景点</h2>
                <button class="text-sm text-primary">查看全部</button>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div onclick="openLuogangPark()" class="bg-white rounded-xl overflow-hidden shadow-sm cursor-pointer hover:shadow-md transition-shadow">
                    <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=200&h=120&fit=crop&crop=center"
                         alt="骆岗公园" class="w-full h-28 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-gray-900 mb-1">骆岗公园</h3>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span class="text-xs text-gray-600">4.8</span>
                            </div>
                            <span class="text-xs text-gray-500">免费</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=200&h=120&fit=crop&crop=center"
                         alt="安徽博物院" class="w-full h-28 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-gray-900 mb-1">安徽博物院</h3>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span class="text-xs text-gray-600">4.5</span>
                            </div>
                            <span class="text-xs text-gray-500">免费</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 附近推荐 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">附近推荐</h2>
                <button class="text-sm text-primary">查看更多</button>
            </div>
            <div class="space-y-3">
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex space-x-3">
                        <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=80&h=80&fit=crop&crop=center"
                             alt="庐州烤鸭店" class="w-16 h-16 rounded-lg object-cover">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 mb-1">庐州烤鸭店</h3>
                            <p class="text-sm text-gray-500 mb-2">距离您 500m · 正宗合肥菜</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        <span class="text-xs text-gray-600">4.6</span>
                                    </div>
                                    <span class="text-xs text-gray-500">人均¥68</span>
                                </div>
                                <button class="bg-green-500 text-white text-xs px-3 py-1 rounded-full">预订</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex space-x-3">
                        <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=80&h=80&fit=crop&crop=center"
                             alt="合肥万达文华酒店" class="w-16 h-16 rounded-lg object-cover">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 mb-1">万达文华酒店</h3>
                            <p class="text-sm text-gray-500 mb-2">距离您 800m · 豪华商务酒店</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        <span class="text-xs text-gray-600">4.7</span>
                                    </div>
                                    <span class="text-xs text-gray-500">¥458/晚</span>
                                </div>
                                <button class="bg-blue-500 text-white text-xs px-3 py-1 rounded-full">预订</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 旅游资讯 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">旅游资讯</h2>
                <button class="text-sm text-primary">查看更多</button>
            </div>
            <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                <img src="https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400&h=160&fit=crop&crop=center"
                     alt="合肥春季旅游" class="w-full h-36 object-cover">
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-2">合肥春季旅游攻略：巢湖赏花正当时</h3>
                    <p class="text-sm text-gray-500 mb-3">春暖花开，巢湖畔樱花盛开。为您整理了合肥各大景点的最佳赏花时间和路线...</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-gray-400">2024-03-15</span>
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>👁 2.1k</span>
                            <span>❤️ 156</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 合肥特色 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">合肥特色</h2>
                <button class="text-sm text-primary">查看更多</button>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div class="bg-white rounded-xl p-3 shadow-sm">
                    <div class="flex items-center space-x-2 mb-2">
                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                            <span class="text-orange-600 text-sm">🥟</span>
                        </div>
                        <span class="font-medium text-gray-900 text-sm">庐州烤鸭</span>
                    </div>
                    <p class="text-xs text-gray-500">合肥传统名菜</p>
                </div>

                <div class="bg-white rounded-xl p-3 shadow-sm">
                    <div class="flex items-center space-x-2 mb-2">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <span class="text-green-600 text-sm">🌸</span>
                        </div>
                        <span class="font-medium text-gray-900 text-sm">三河古镇</span>
                    </div>
                    <p class="text-xs text-gray-500">千年古镇风韵</p>
                </div>

                <div class="bg-white rounded-xl p-3 shadow-sm">
                    <div class="flex items-center space-x-2 mb-2">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <span class="text-blue-600 text-sm">🌳</span>
                        </div>
                        <span class="font-medium text-gray-900 text-sm">骆岗公园</span>
                    </div>
                    <p class="text-xs text-gray-500">城市绿肺，生态乐园</p>
                </div>

                <div class="bg-white rounded-xl p-3 shadow-sm">
                    <div class="flex items-center space-x-2 mb-2">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <span class="text-purple-600 text-sm">🎓</span>
                        </div>
                        <span class="font-medium text-gray-900 text-sm">中科大校园</span>
                    </div>
                    <p class="text-xs text-gray-500">科教名城象征</p>
                </div>
            </div>
        </section>
    </main>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <!-- 对话气泡 -->
            <div class="chat-bubble absolute -top-16 -left-20 bg-white rounded-2xl px-4 py-2 shadow-lg border border-gray-200 max-w-32">
                <p class="text-xs text-gray-700">我是小肥，有什么可以帮您的吗？</p>
                <div class="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200"></div>
            </div>

            <!-- 虚拟人形象 -->
            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <!-- 卡通头像 -->
                <div class="relative">
                    <!-- 脸部 -->
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <!-- 眼睛 -->
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <!-- 嘴巴 -->
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <!-- 腮红 -->
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <!-- 帽子 -->
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>

            <!-- 在线状态指示器 -->
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div class="grid grid-cols-6 py-2 pb-safe">
            <button onclick="navigateTo('首页')" class="flex flex-col items-center py-2 text-primary">
                <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button onclick="navigateTo('发现')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="text-xs">发现</span>
            </button>
            <button onclick="navigateTo('行程')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                </svg>
                <span class="text-xs">行程</span>
            </button>
            <button onclick="navigateTo('服务')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="text-xs">服务</span>
            </button>
            <button onclick="navigateTo('社区')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="text-xs">社区</span>
            </button>
            <button onclick="navigateTo('我的')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </nav>

    <script>
        let chatMessages = [
            "我是小肥，有什么可以帮您的吗？",
            "想了解合肥的景点吗？",
            "需要推荐美食或酒店吗？",
            "我可以为您规划行程哦！",
            "点击我开始对话吧～"
        ];
        let currentMessageIndex = 0;

        // 页面导航功能
        function navigateTo(pageName) {
            const pageMap = {
                '首页': '文旅小程序-首页.html',
                '发现': '文旅小程序-发现.html',
                '行程': '文旅小程序-行程.html',
                '服务': '文旅小程序-服务.html',
                '社区': '文旅小程序-社区.html',
                '我的': '文旅小程序-我的.html'
            };

            if (pageMap[pageName]) {
                // 添加页面切换动画
                document.body.style.opacity = '0.8';
                document.body.style.transform = 'scale(0.95)';
                document.body.style.transition = 'all 0.3s ease-out';

                setTimeout(() => {
                    window.location.href = pageMap[pageName];
                }, 300);
            } else {
                // 如果页面不存在，显示提示
                showToast(`${pageName}页面正在开发中...`);
            }
        }

        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        function openChat() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        function toggleChat() {
            // 保留原有功能作为备用
            openChat();
        }

        function openLuogangPark() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-骆岗公园.html';
            }, 300);
        }

        function openChaohuScenic() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-巢湖风景区.html';
            }, 300);
        }

        function openAIPlanning() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-行程规划.html';
            }, 300);
        }

        // 自动切换对话内容
        function rotateChatMessage() {
            const chatBubble = document.querySelector('.chat-bubble p');
            if (chatBubble) {
                currentMessageIndex = (currentMessageIndex + 1) % chatMessages.length;
                chatBubble.textContent = chatMessages[currentMessageIndex];
            }
        }

        // 每6秒切换一次消息
        setInterval(rotateChatMessage, 6000);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟显示虚拟助手
            setTimeout(() => {
                const assistant = document.querySelector('.virtual-assistant').parentElement;
                assistant.style.opacity = '0';
                assistant.style.transform = 'translateY(20px)';
                assistant.style.transition = 'all 0.5s ease-out';

                setTimeout(() => {
                    assistant.style.opacity = '1';
                    assistant.style.transform = 'translateY(0)';
                }, 100);
            }, 1000);

            // 设置当前页面的导航状态
            setActiveNavigation('首页');
        });

        // 设置当前激活的导航项
        function setActiveNavigation(currentPage) {
            const navButtons = document.querySelectorAll('nav button');
            navButtons.forEach(button => {
                const span = button.querySelector('span');
                if (span && span.textContent === currentPage) {
                    button.className = button.className.replace('text-gray-400', 'text-primary');
                    span.className = span.className.includes('font-medium') ? span.className : span.className + ' font-medium';
                } else {
                    button.className = button.className.replace('text-primary', 'text-gray-400');
                    span.className = span.className.replace(' font-medium', '');
                }
            });
        }
    </script>
</body>
</html>
