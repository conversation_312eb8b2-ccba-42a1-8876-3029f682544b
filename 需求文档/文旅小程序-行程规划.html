<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 行程规划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body::-webkit-scrollbar {
            display: none;
        }
        
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
        
        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .typing-animation {
            animation: typing 1.5s infinite;
        }
        
        @keyframes typing {
            0%, 60%, 100% { opacity: 1; }
            30% { opacity: 0.4; }
        }
        
        .message-slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-6 text-white">
        <div class="flex items-center space-x-3 mb-4">
            <button onclick="history.back()" class="p-2 text-white/80 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-lg font-bold">AI行程规划</h1>
        </div>
        
        <!-- 虚拟人和标语 -->
        <div class="flex items-center space-x-4">
            <!-- 虚拟人形象 -->
            <div class="virtual-assistant w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <div class="relative">
                    <!-- 脸部 -->
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <!-- 眼睛 -->
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <!-- 嘴巴 -->
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <!-- 腮红 -->
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <!-- 帽子 -->
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>
            
            <!-- 标语文字 -->
            <div class="flex-1">
                <h2 class="text-xl font-bold mb-1">你的旅程节奏</h2>
                <p class="text-white/90 text-sm">智能体为你校准刻度</p>
            </div>
        </div>
    </header>

    <!-- 规划步骤指示 -->
    <section class="px-4 py-4 bg-white border-b border-gray-100">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold" id="step1">1</div>
                <span class="text-sm font-medium text-primary" id="step1Text">偏好设置</span>
            </div>
            <div class="flex-1 h-0.5 bg-gray-200 mx-3">
                <div class="h-full bg-primary w-0 transition-all duration-500" id="progressBar"></div>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-gray-200 text-gray-500 rounded-full flex items-center justify-center text-xs" id="step2">2</div>
                <span class="text-sm text-gray-500" id="step2Text">生成行程</span>
            </div>
        </div>
    </section>

    <!-- 偏好设置页卡 -->
    <main id="preferencesCard" class="px-4 py-6">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <div class="text-center mb-6">
                <h2 class="text-xl font-bold text-gray-900 mb-2">设置你的旅行偏好</h2>
                <p class="text-gray-600 text-sm">告诉我你的需求，我来为你定制专属行程</p>
            </div>

            <!-- 旅游时长 -->
            <div class="mb-6">
                <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs mr-2">⏰</span>
                    旅游时长
                </h3>
                <div class="grid grid-cols-3 gap-3">
                    <button onclick="selectPreference('time', '半天游', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-blue-300 transition-colors">
                        <div class="text-2xl mb-1">🌅</div>
                        <div class="text-sm font-medium text-gray-700">半天游</div>
                        <div class="text-xs text-gray-500">3-4小时</div>
                    </button>
                    <button onclick="selectPreference('time', '一日游', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-blue-300 transition-colors">
                        <div class="text-2xl mb-1">☀️</div>
                        <div class="text-sm font-medium text-gray-700">一日游</div>
                        <div class="text-xs text-gray-500">8-10小时</div>
                    </button>
                    <button onclick="selectPreference('time', '两日游', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-blue-300 transition-colors">
                        <div class="text-2xl mb-1">🌙</div>
                        <div class="text-sm font-medium text-gray-700">两日游</div>
                        <div class="text-xs text-gray-500">1晚2天</div>
                    </button>
                </div>
            </div>

            <!-- 旅游目的 -->
            <div class="mb-6">
                <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                    <span class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs mr-2">🎯</span>
                    旅游目的
                </h3>
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="selectPreference('purpose', '休闲放松', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-green-300 transition-colors">
                        <div class="text-2xl mb-1">🧘</div>
                        <div class="text-sm font-medium text-gray-700">休闲放松</div>
                        <div class="text-xs text-gray-500">慢节奏体验</div>
                    </button>
                    <button onclick="selectPreference('purpose', '文化体验', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-green-300 transition-colors">
                        <div class="text-2xl mb-1">🏛️</div>
                        <div class="text-sm font-medium text-gray-700">文化体验</div>
                        <div class="text-xs text-gray-500">历史人文</div>
                    </button>
                    <button onclick="selectPreference('purpose', '美食探索', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-green-300 transition-colors">
                        <div class="text-2xl mb-1">🍜</div>
                        <div class="text-sm font-medium text-gray-700">美食探索</div>
                        <div class="text-xs text-gray-500">品尝特色</div>
                    </button>
                    <button onclick="selectPreference('purpose', '拍照打卡', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-green-300 transition-colors">
                        <div class="text-2xl mb-1">📸</div>
                        <div class="text-sm font-medium text-gray-700">拍照打卡</div>
                        <div class="text-xs text-gray-500">网红景点</div>
                    </button>
                </div>
            </div>

            <!-- 同行人员 -->
            <div class="mb-6">
                <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                    <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-xs mr-2">👥</span>
                    同行人员
                </h3>
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="selectPreference('companions', '独自一人', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-purple-300 transition-colors">
                        <div class="text-2xl mb-1">🚶</div>
                        <div class="text-sm font-medium text-gray-700">独自一人</div>
                        <div class="text-xs text-gray-500">自由自在</div>
                    </button>
                    <button onclick="selectPreference('companions', '情侣出行', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-purple-300 transition-colors">
                        <div class="text-2xl mb-1">💑</div>
                        <div class="text-sm font-medium text-gray-700">情侣出行</div>
                        <div class="text-xs text-gray-500">浪漫二人</div>
                    </button>
                    <button onclick="selectPreference('companions', '家庭亲子', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-purple-300 transition-colors">
                        <div class="text-2xl mb-1">👨‍👩‍👧‍👦</div>
                        <div class="text-sm font-medium text-gray-700">家庭亲子</div>
                        <div class="text-xs text-gray-500">老少皆宜</div>
                    </button>
                    <button onclick="selectPreference('companions', '朋友聚会', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-purple-300 transition-colors">
                        <div class="text-2xl mb-1">👫</div>
                        <div class="text-sm font-medium text-gray-700">朋友聚会</div>
                        <div class="text-xs text-gray-500">热闹有趣</div>
                    </button>
                </div>
            </div>

            <!-- 预算范围 -->
            <div class="mb-8">
                <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                    <span class="w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-xs mr-2">💰</span>
                    预算范围
                </h3>
                <div class="grid grid-cols-3 gap-3">
                    <button onclick="selectPreference('budget', '经济实惠', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-orange-300 transition-colors">
                        <div class="text-2xl mb-1">💵</div>
                        <div class="text-sm font-medium text-gray-700">经济实惠</div>
                        <div class="text-xs text-gray-500">100元内</div>
                    </button>
                    <button onclick="selectPreference('budget', '中等消费', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-orange-300 transition-colors">
                        <div class="text-2xl mb-1">💳</div>
                        <div class="text-sm font-medium text-gray-700">中等消费</div>
                        <div class="text-xs text-gray-500">100-300元</div>
                    </button>
                    <button onclick="selectPreference('budget', '豪华体验', this)" class="preference-btn p-4 border-2 border-gray-200 rounded-xl text-center hover:border-orange-300 transition-colors">
                        <div class="text-2xl mb-1">💎</div>
                        <div class="text-sm font-medium text-gray-700">豪华体验</div>
                        <div class="text-xs text-gray-500">300元以上</div>
                    </button>
                </div>
            </div>

            <!-- 生成按钮 -->
            <button onclick="generateItinerary()" id="generateBtn" class="w-full bg-gray-300 text-gray-500 py-4 rounded-xl font-semibold text-lg transition-all duration-300 cursor-not-allowed" disabled>
                请完成所有选择
            </button>
        </div>
    </main>

    <!-- 对话区域 (初始隐藏) -->
    <main id="chatContainer" class="hidden flex-1 px-4 py-4 space-y-4 pb-24 min-h-96">
    </main>



    <!-- 输入区域 (对话模式时显示) -->
    <div id="inputArea" class="hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3 pb-safe">
        <div class="flex items-end space-x-3">
            <div class="flex-1 relative">
                <textarea id="messageInput"
                         placeholder="有什么想调整的吗..."
                         class="w-full px-4 py-3 pr-12 bg-gray-100 rounded-2xl text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white"
                         rows="1"
                         maxlength="500"></textarea>
                <button onclick="sendMessage()"
                        class="absolute right-2 bottom-2 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        let userPreferences = {
            time: '',
            purpose: '',
            companions: '',
            budget: ''
        };

        // 选择偏好
        function selectPreference(type, value, element) {
            // 更新数据
            userPreferences[type] = value;

            // 更新UI - 移除同类型其他按钮的选中状态
            const sameTypeButtons = element.parentElement.querySelectorAll('.preference-btn');
            sameTypeButtons.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50', 'border-green-500', 'bg-green-50',
                                   'border-purple-500', 'bg-purple-50', 'border-orange-500', 'bg-orange-50');
                btn.classList.add('border-gray-200');
            });

            // 添加选中状态
            element.classList.remove('border-gray-200');
            if (type === 'time') {
                element.classList.add('border-blue-500', 'bg-blue-50');
            } else if (type === 'purpose') {
                element.classList.add('border-green-500', 'bg-green-50');
            } else if (type === 'companions') {
                element.classList.add('border-purple-500', 'bg-purple-50');
            } else if (type === 'budget') {
                element.classList.add('border-orange-500', 'bg-orange-50');
            }

            // 检查是否所有选项都已选择
            checkAllSelected();
        }

        // 检查是否所有选项都已选择
        function checkAllSelected() {
            const { time, purpose, companions, budget } = userPreferences;
            const generateBtn = document.getElementById('generateBtn');

            if (time && purpose && companions && budget) {
                generateBtn.disabled = false;
                generateBtn.classList.remove('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                generateBtn.classList.add('bg-gradient-to-r', 'from-blue-500', 'to-purple-600', 'text-white', 'hover:from-blue-600', 'hover:to-purple-700');
                generateBtn.textContent = '🚀 开始生成专属行程';
            } else {
                generateBtn.disabled = true;
                generateBtn.classList.add('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                generateBtn.classList.remove('bg-gradient-to-r', 'from-blue-500', 'to-purple-600', 'text-white', 'hover:from-blue-600', 'hover:to-purple-700');
                generateBtn.textContent = '请完成所有选择';
            }
        }

        // 生成行程
        function generateItinerary() {
            // 隐藏偏好设置页卡
            document.getElementById('preferencesCard').style.display = 'none';

            // 显示对话区域和输入区域
            document.getElementById('chatContainer').classList.remove('hidden');
            document.getElementById('inputArea').classList.remove('hidden');

            // 更新进度条和步骤指示器
            updateProgressToStep2();

            // 添加用户选择总结
            addUserSummary();

            // 显示小肥正在生成
            showTyping();

            setTimeout(() => {
                hideTyping();
                const itinerary = createCustomItinerary();
                addBotMessage(itinerary);

                // 显示操作按钮
                setTimeout(() => {
                    addActionButtons();
                }, 1000);
            }, 3000);
        }

        // 更新到第二步
        function updateProgressToStep2() {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = '100%';

            // 更新步骤指示器
            document.getElementById('step1').innerHTML = '✓';
            document.getElementById('step1').className = 'w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs';
            document.getElementById('step1Text').textContent = '偏好设置完成';
            document.getElementById('step1Text').className = 'text-sm font-medium text-green-600';

            document.getElementById('step2').innerHTML = '2';
            document.getElementById('step2').className = 'w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold';
            document.getElementById('step2Text').textContent = '生成行程';
            document.getElementById('step2Text').className = 'text-sm font-medium text-primary';
        }

        // 添加用户选择总结
        function addUserSummary() {
            const { time, purpose, companions, budget } = userPreferences;
            const summary = `我的选择：${time} | ${purpose} | ${companions} | ${budget}`;
            addUserMessage(summary);
        }

        
        function createCustomItinerary() {
            const { time, purpose, companions, budget } = userPreferences;

            let itinerary = `🎉 **太棒了！根据你的偏好，我为你定制了专属的${time}行程**\n\n`;

            if (time === '半天游') {
                itinerary += `**上午 9:00-12:00**\n`;
                if (purpose === '文化体验') {
                    itinerary += `🏛️ 安徽博物院 - 了解安徽历史文化\n`;
                } else if (purpose === '休闲放松') {
                    itinerary += `🌳 骆岗公园 - 晨练散步，呼吸新鲜空气\n`;
                } else if (purpose === '美食探索') {
                    itinerary += `🍜 合肥老街 - 探索地道小吃\n`;
                } else {
                    itinerary += `🌊 巢湖风景区 - 湖光山色，拍照打卡\n`;
                }

                itinerary += `\n**中午 12:00-13:00**\n`;
                if (budget === '经济实惠') {
                    itinerary += `🍜 当地小吃街 - 品尝合肥特色小吃\n`;
                } else {
                    itinerary += `🦆 庐州烤鸭店 - 品尝正宗合肥美食\n`;
                }
            } else if (time === '一日游') {
                itinerary += `**上午 9:00-11:30**\n`;
                if (purpose === '休闲放松') {
                    itinerary += `🌳 骆岗公园 - 晨练散步，享受自然\n`;
                } else {
                    itinerary += `🏛️ 安徽博物院 - 了解历史文化\n`;
                }

                itinerary += `\n**中午 12:00-13:30**\n`;
                if (budget === '经济实惠') {
                    itinerary += `🍜 合肥小吃街 - 经济美味\n`;
                } else if (budget === '豪华体验') {
                    itinerary += `🦆 高档餐厅 - 精致合肥菜\n`;
                } else {
                    itinerary += `🦆 庐州烤鸭店 - 品尝美食\n`;
                }

                itinerary += `\n**下午 14:00-16:30**\n`;
                if (purpose === '文化体验') {
                    itinerary += `⚖️ 包公园 - 感受清廉文化\n`;
                } else {
                    itinerary += `🌊 巢湖风景区 - 湖光山色\n`;
                }

                itinerary += `\n**傍晚 17:00-19:00**\n🏮 三河古镇 - 古镇风情，拍照留念\n`;
            } else {
                itinerary += `**Day 1:**\n`;
                itinerary += `• 上午：🌊 巢湖风景区 - 湖光山色\n`;
                itinerary += `• 下午：🏮 三河古镇 - 古镇风情\n`;
                itinerary += `• 晚上：🦆 庐州烤鸭 - 特色美食\n\n`;

                itinerary += `**Day 2:**\n`;
                itinerary += `• 上午：🌳 骆岗公园 - 生态休闲\n`;
                itinerary += `• 下午：🏛️ 安徽博物院 - 历史文化\n`;
                itinerary += `• 傍晚：⚖️ 包公园 - 清廉文化\n`;
            }

            // 添加个性化建议
            itinerary += `\n💡 **个性化建议：**\n`;
            if (companions === '家庭亲子') {
                itinerary += `• 建议选择适合儿童的景点和餐厅\n• 安排充足的休息时间\n`;
            } else if (companions === '情侣出行') {
                itinerary += `• 推荐浪漫的湖边漫步和特色餐厅\n• 选择适合拍照的景点\n`;
            } else if (companions === '朋友聚会') {
                itinerary += `• 安排互动性强的活动\n• 选择适合聚餐的餐厅\n`;
            } else {
                itinerary += `• 自由安排时间，随心所欲\n• 可以深度体验每个景点\n`;
            }

            if (budget === '豪华体验') {
                itinerary += `• 可升级住宿和餐饮标准\n• 考虑包车或专车服务\n`;
            } else if (budget === '经济实惠') {
                itinerary += `• 多选择免费景点和经济餐厅\n• 使用公共交通出行\n`;
            }

            itinerary += `\n📍 **交通提示：**\n• 建议使用地铁+公交组合出行\n• 各景点间距离适中，交通便利\n• 可下载合肥地铁APP查看实时信息`;

            return itinerary;
        }
        
        function addActionButtons() {
            const container = document.getElementById('chatContainer');
            const buttonDiv = document.createElement('div');
            buttonDiv.className = 'flex space-x-3 message-slide-in';
            
            buttonDiv.innerHTML = `
                <div class="w-10 h-10 flex-shrink-0"></div>
                <div class="flex-1">
                    <div class="flex space-x-2">
                        <button onclick="saveItinerary()" class="flex-1 bg-primary text-white py-3 px-4 rounded-xl text-sm font-medium hover:bg-blue-600 transition-colors">
                            💾 保存行程
                        </button>
                        <button onclick="regenerateItinerary()" class="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">
                            🔄 重新生成
                        </button>
                    </div>
                </div>
            `;
            
            container.appendChild(buttonDiv);
            scrollToBottom();
        }
        
        function saveItinerary() {
            addBotMessage('行程已保存到你的行程列表中！🎉\n\n你可以在"我的行程"中查看和管理。需要我为你做其他安排吗？');
        }
        
        function regenerateItinerary() {
            addBotMessage('好的，让我重新为你规划一个行程方案！');
            setTimeout(() => {
                const newItinerary = createCustomItinerary();
                addBotMessage('🔄 **重新生成的行程方案：**\n\n' + newItinerary);
                setTimeout(() => {
                    addActionButtons();
                }, 1000);
            }, 2000);
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                addUserMessage(message);
                input.value = '';
                input.style.height = 'auto';
                
                // 显示小肥正在思考
                showTyping();
                
                setTimeout(() => {
                    hideTyping();
                    handleUserMessage(message);
                }, 1500);
            }
        }
        
        function handleUserMessage(message) {
            // 处理用户在对话阶段的输入
            addBotMessage('感谢你的反馈！如果需要调整行程，我可以重新为你规划。还有其他问题吗？');
        }
        
        function addUserMessage(message) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 justify-end message-slide-in';
            
            messageDiv.innerHTML = `
                <div class="flex-1 max-w-xs">
                    <div class="bg-primary text-white rounded-2xl rounded-tr-md p-4">
                        <p class="text-sm leading-relaxed">${message}</p>
                    </div>
                    <div class="text-xs text-gray-400 mt-1 mr-2 text-right">刚刚</div>
                </div>
                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            `;
            
            container.appendChild(messageDiv);
            scrollToBottom();
        }
        
        function addBotMessage(message) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 message-slide-in';
            
            // 处理换行和格式
            const formattedMessage = message.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            
            messageDiv.innerHTML = `
                <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-2 left-1.5 w-1.5 h-1.5 bg-black rounded-full"></div>
                        <div class="absolute top-2 right-1.5 w-1.5 h-1.5 bg-black rounded-full"></div>
                        <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-1.5 border-2 border-black border-t-0 rounded-b-full"></div>
                    </div>
                </div>
                <div class="flex-1 max-w-sm">
                    <div class="bg-white rounded-2xl rounded-tl-md p-4 shadow-sm border border-gray-100">
                        <p class="text-gray-800 text-sm leading-relaxed">${formattedMessage}</p>
                    </div>
                    <div class="text-xs text-gray-400 mt-1 ml-2">刚刚</div>
                </div>
            `;
            
            container.appendChild(messageDiv);
            scrollToBottom();
        }
        
        function showTyping() {
            const container = document.getElementById('chatContainer');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typingIndicator';
            typingDiv.className = 'flex items-start space-x-3';
            
            typingDiv.innerHTML = `
                <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-2 left-1.5 w-1.5 h-1.5 bg-black rounded-full"></div>
                        <div class="absolute top-2 right-1.5 w-1.5 h-1.5 bg-black rounded-full"></div>
                        <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-1.5 border-2 border-black border-t-0 rounded-b-full"></div>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="bg-white rounded-2xl rounded-tl-md p-4 shadow-sm border border-gray-100">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-gray-400 rounded-full typing-animation"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full typing-animation" style="animation-delay: 0.2s;"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full typing-animation" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1 ml-2">正在思考...</div>
                </div>
            `;
            
            container.appendChild(typingDiv);
            scrollToBottom();
        }
        
        function hideTyping() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }
        
        function scrollToBottom() {
            setTimeout(() => {
                window.scrollTo(0, document.body.scrollHeight);
            }, 100);
        }
        
        // 输入框自动调整高度
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 页面已经显示偏好设置卡片，无需额外初始化
        });
    </script>
</body>
</html>
