<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 我的</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        body::-webkit-scrollbar {
            display: none;
        }

        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }

        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .chat-bubble {
            animation: fadeInOut 4s ease-in-out infinite;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: scale(0.8); }
            25%, 75% { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部用户信息 -->
    <header class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-6 text-white">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div class="flex-1">
                <h2 class="text-xl font-bold">张三</h2>
                <p class="text-sm opacity-90">合肥旅游爱好者</p>
                <div class="flex items-center space-x-4 mt-2 text-sm">
                    <span>🏆 积分: 1,280</span>
                    <span>📍 合肥市</span>
                </div>
            </div>
            <button class="p-2 bg-white/20 rounded-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="px-4 py-4 space-y-6 pb-24">
        <!-- 快捷功能 -->
        <section class="grid grid-cols-4 gap-4">
            <button onclick="openFunction('我的订单')" class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-700">我的订单</span>
            </button>

            <button onclick="openMyTrips()" class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-700">我的行程</span>
            </button>

            <button onclick="openFunction('我的收藏')" class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-700">我的收藏</span>
            </button>

            <button onclick="openFunction('积分中心')" class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mb-2">
                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <span class="text-xs text-gray-700">积分中心</span>
            </button>
        </section>

        <!-- 我的游记 -->
        <section class="mt-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">我的游记</h2>
                <button onclick="openFunction('我的游记')" class="text-sm text-primary">查看全部</button>
            </div>

            <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-3 mb-3">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">合肥春日游记</h3>
                        <p class="text-sm text-gray-500">记录美好的旅行时光</p>
                    </div>
                    <span class="text-xs text-gray-400">3天前</span>
                </div>
                <p class="text-sm text-gray-600 line-clamp-2">春天的合肥格外美丽，樱花盛开的季节里，我们游览了骆岗公园和巢湖风景区...</p>
            </div>
        </section>

        <!-- 我的订单 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">我的订单</h2>
                <button class="text-sm text-primary">查看全部</button>
            </div>

            <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-3 mb-3">
                    <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=60&h=60&fit=crop&crop=center"
                         alt="万达文华酒店" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">万达文华酒店</h3>
                        <p class="text-sm text-gray-500">豪华大床房 · 1晚</p>
                    </div>
                    <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">已完成</span>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">2024-03-15</span>
                    <span class="font-semibold text-gray-900">¥368</span>
                </div>
            </div>
        </section>

        <!-- 我的收藏 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">我的收藏</h2>
                <button class="text-sm text-primary">查看全部</button>
            </div>

            <div class="grid grid-cols-2 gap-3">
                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <img src="https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=200&h=120&fit=crop&crop=center"
                         alt="巢湖风景区" class="w-full h-24 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-gray-900 mb-1">巢湖风景区</h3>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">⭐ 4.7</span>
                            <span class="text-xs text-gray-500">免费</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=120&fit=crop&crop=center"
                         alt="包公园" class="w-full h-24 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-gray-900 mb-1">包公园</h3>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">⭐ 4.6</span>
                            <span class="text-xs text-gray-500">¥20</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能菜单 -->
        <section>
            <div class="space-y-3">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                    <button onclick="openFunction('个人信息')" class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <span class="font-medium text-gray-900">个人信息</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                    <button onclick="openFunction('消息通知')" class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                                </svg>
                            </div>
                            <span class="font-medium text-gray-900">消息通知</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</span>
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </button>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                    <button onclick="openFunction('帮助中心')" class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <span class="font-medium text-gray-900">帮助中心</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                    <button onclick="openFunction('关于我们')" class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <span class="font-medium text-gray-900">关于我们</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <div class="chat-bubble absolute -top-16 -left-20 bg-white rounded-2xl px-4 py-2 shadow-lg border border-gray-200 max-w-32">
                <p class="text-xs text-gray-700">有什么可以帮您的？</p>
                <div class="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200"></div>
            </div>

            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <div class="relative">
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>

            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div class="grid grid-cols-6 py-2 pb-safe">
            <button onclick="navigateTo('首页')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="text-xs">首页</span>
            </button>
            <button onclick="navigateTo('发现')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="text-xs">发现</span>
            </button>
            <button onclick="navigateTo('行程')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                </svg>
                <span class="text-xs">行程</span>
            </button>
            <button onclick="navigateTo('服务')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="text-xs">服务</span>
            </button>
            <button onclick="navigateTo('社区')" class="flex flex-col items-center py-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="text-xs">社区</span>
            </button>
            <button onclick="navigateTo('我的')" class="flex flex-col items-center py-2 text-primary">
                <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-xs font-medium">我的</span>
            </button>
        </div>
    </nav>

    <script>
        function navigateTo(pageName) {
            const pageMap = {
                '首页': '文旅小程序-首页.html',
                '发现': '文旅小程序-发现.html',
                '行程': '文旅小程序-行程.html',
                '服务': '文旅小程序-服务.html',
                '社区': '文旅小程序-社区.html',
                '我的': '文旅小程序-我的.html'
            };

            if (pageMap[pageName]) {
                document.body.style.opacity = '0.8';
                document.body.style.transform = 'scale(0.95)';
                document.body.style.transition = 'all 0.3s ease-out';

                setTimeout(() => {
                    window.location.href = pageMap[pageName];
                }, 300);
            } else {
                showToast(`${pageName}页面正在开发中...`);
            }
        }

        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        function openFunction(functionName) {
            showToast(`${functionName}功能开发中...`);
        }

        function openChat() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        function toggleChat() {
            openChat();
        }

        function openMyTrips() {
            // 页面切换动画
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-行程规划.html';
            }, 300);
        }

        document.addEventListener('DOMContentLoaded', function() {
            setActiveNavigation('我的');
        });

        function setActiveNavigation(currentPage) {
            const navButtons = document.querySelectorAll('nav button');
            navButtons.forEach(button => {
                const span = button.querySelector('span');
                if (span && span.textContent === currentPage) {
                    button.className = button.className.replace('text-gray-400', 'text-primary');
                    span.className = span.className.includes('font-medium') ? span.className : span.className + ' font-medium';
                } else {
                    button.className = button.className.replace('text-primary', 'text-gray-400');
                    span.className = span.className.replace(' font-medium', '');
                }
            });
        }
    </script>
</body>
</html>