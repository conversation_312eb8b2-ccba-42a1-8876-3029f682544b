<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 骆岗公园</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 高德地图API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=e6efa820f689e18d7a5150372060df85&plugin=AMap.Scale,AMap.ToolBar,AMap.ControlBar,AMap.Geolocation"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body::-webkit-scrollbar {
            display: none;
        }
        
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
        
        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .map-container {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        #amapContainer {
            width: 100%;
            height: 400px;
            border-radius: 20px;
        }

        .amap-marker {
            background: rgba(59, 130, 246, 0.9);
            border: 3px solid white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .amap-marker:hover {
            transform: scale(1.2);
            background: rgba(16, 185, 129, 0.9);
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }

        .amap-info-window {
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .map-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .map-control-btn {
            background: white;
            border: none;
            border-radius: 8px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .map-control-btn:hover {
            background: #f3f4f6;
            transform: scale(1.05);
        }

        .map-legend {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: white;
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            max-width: 200px;
            z-index: 10;
        }
        
        .info-popup {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 300px;
            z-index: 1000;
            display: none;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white px-4 py-3 shadow-sm sticky top-0 z-30">
        <div class="flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            
            <div class="flex-1">
                <h1 class="text-lg font-bold text-gray-900">骆岗公园</h1>
                <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>合肥市包河区骆岗街道</span>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <button onclick="shareLocation()" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                    </svg>
                </button>
                <button onclick="addToFavorites()" class="p-2 text-gray-400 hover:text-red-500">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- 公园信息卡片 -->
    <section class="px-4 py-4">
        <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-green-600 text-lg">🌳</span>
                    </div>
                    <div>
                        <h2 class="font-semibold text-gray-900">城市绿肺，生态乐园</h2>
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                4.8分
                            </span>
                            <span>免费开放</span>
                            <span>全天24小时</span>
                        </div>
                    </div>
                </div>
                <button onclick="openNavigation()" class="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium">
                    导航
                </button>
            </div>
        </div>
    </section>

    <!-- 高德地图区域 -->
    <section class="px-4 pb-4">
        <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold text-gray-900">公园导览图</h3>
                <div class="flex items-center space-x-2">
                    <button onclick="toggleMapType()" class="text-sm text-primary flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"></path>
                        </svg>
                        <span id="mapTypeText">卫星图</span>
                    </button>
                    <button onclick="toggleMapMode()" class="text-sm text-primary flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                        </svg>
                        全屏查看
                    </button>
                </div>
            </div>

            <!-- 高德地图容器 -->
            <div class="map-container" style="position: relative;">
                <div id="amapContainer"></div>

                <!-- 地图控制按钮 -->
                <div class="map-controls">
                    <button class="map-control-btn" onclick="locateUser()" title="定位到我的位置">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </button>
                    <button class="map-control-btn" onclick="resetMapView()" title="重置视图">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </button>
                    <button class="map-control-btn" onclick="showTrafficLayer()" title="交通状况">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>

                <!-- 地图图例 -->
                <div class="map-legend">
                    <h4 class="text-xs font-semibold text-gray-700 mb-2">图例说明</h4>
                    <div class="space-y-1 text-xs">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-gray-600">景点位置</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-gray-600">服务设施</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                            <span class="text-gray-600">出入口</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图例 -->
            <div class="mt-4 p-3 bg-gray-50 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">图例说明</h4>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-primary rounded-full"></div>
                        <span class="text-gray-600">景点位置</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-1 border-2 border-dashed border-green-500"></div>
                        <span class="text-gray-600">步行道路</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                        <span class="text-gray-600">绿化区域</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-gray-600">点击景点查看详情</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 景点信息弹窗 -->
    <div id="infoPopup" class="info-popup">
        <div class="flex items-start justify-between mb-3">
            <h4 id="popupTitle" class="font-semibold text-gray-900"></h4>
            <button onclick="closePopup()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <p id="popupDescription" class="text-sm text-gray-600 mb-3"></p>
        <div class="flex space-x-2">
            <button onclick="getDirections()" class="flex-1 bg-primary text-white py-2 px-3 rounded-lg text-sm">
                前往
            </button>
            <button onclick="closePopup()" class="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm">
                关闭
            </button>
        </div>
    </div>

    <!-- 加入行程按钮 -->
    <div class="fixed left-4 bottom-24 z-40">
        <button onclick="addToTrip()" class="bg-purple-500 text-white px-4 py-3 rounded-full shadow-lg hover:bg-purple-600 transition-colors flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span class="text-sm font-medium">加入行程</span>
        </button>
    </div>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <div class="relative">
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>
            
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <script>
        // 高德地图实例
        let map;
        let currentMapType = 'standard'; // 'standard' 或 'satellite'
        let trafficLayer;
        let geolocation;

        // 骆岗公园中心坐标 (合肥市包河区)
        const parkCenter = [117.2461, 31.8206];

        // 景点信息数据
        const pointsInfo = {
            'main-entrance': {
                title: '主入口',
                description: '骆岗公园主要入口，设有游客服务中心、停车指引和公园导览图。这里是开始游览的最佳起点。',
                position: [117.2461, 31.8186],
                type: 'entrance'
            },
            'lake-pavilion': {
                title: '湖心亭',
                description: '公园的标志性建筑，位于人工湖中央。亭内可休憩观景，是拍照留念的热门地点，尤其适合日出日落时分。',
                position: [117.2471, 31.8206],
                type: 'attraction'
            },
            'fitness-area': {
                title: '健身区',
                description: '配备各种户外健身器材，适合晨练和日常锻炼。周围绿树成荫，空气清新，是市民健身的好去处。',
                position: [117.2441, 31.8226],
                type: 'facility'
            },
            'children-playground': {
                title: '儿童乐园',
                description: '专为儿童设计的游乐区域，有滑梯、秋千、攀爬架等设施。地面铺设安全软垫，家长可放心让孩子玩耍。',
                position: [117.2481, 31.8226],
                type: 'facility'
            },
            'cherry-forest': {
                title: '樱花林',
                description: '春季樱花盛开时是公园最美的景观之一。粉色花海浪漫迷人，是情侣约会和摄影爱好者的首选地点。',
                position: [117.2441, 31.8186],
                type: 'attraction'
            },
            'viewing-platform': {
                title: '观景台',
                description: '公园制高点，可俯瞰整个公园全景。设有休息座椅和望远镜，是观赏日落和城市夜景的绝佳位置。',
                position: [117.2481, 31.8186],
                type: 'attraction'
            },
            'parking': {
                title: '停车场',
                description: '免费停车场，可容纳200余辆车。设有电动车充电桩和无障碍停车位，为游客提供便利的停车服务。',
                position: [117.2461, 31.8246],
                type: 'facility'
            }
        };

        // 初始化地图
        function initMap() {
            map = new AMap.Map('amapContainer', {
                center: parkCenter,
                zoom: 16,
                mapStyle: 'amap://styles/normal',
                features: ['bg', 'road', 'building', 'point'],
                viewMode: '2D'
            });

            // 添加地图控件
            map.addControl(new AMap.Scale());
            map.addControl(new AMap.ToolBar({
                visible: false
            }));

            // 初始化定位
            geolocation = new AMap.Geolocation({
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0,
                convert: true,
                showButton: false,
                showMarker: false,
                showCircle: false
            });

            // 添加景点标记
            addParkMarkers();

            // 添加公园边界
            addParkBoundary();
        }

        // 添加景点标记
        function addParkMarkers() {
            Object.keys(pointsInfo).forEach(pointId => {
                const point = pointsInfo[pointId];
                const markerColor = getMarkerColor(point.type);

                const marker = new AMap.Marker({
                    position: point.position,
                    title: point.title,
                    content: createMarkerContent(markerColor, point.type),
                    anchor: 'center'
                });

                // 添加点击事件
                marker.on('click', () => {
                    showPointInfo(pointId);
                });

                map.add(marker);
            });
        }

        // 创建标记内容
        function createMarkerContent(color, type) {
            const icon = getTypeIcon(type);
            return `
                <div class="amap-marker" style="background: ${color};">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        ${icon}
                    </svg>
                </div>
            `;
        }

        // 获取标记颜色
        function getMarkerColor(type) {
            const colors = {
                'attraction': 'rgba(59, 130, 246, 0.9)',
                'facility': 'rgba(16, 185, 129, 0.9)',
                'entrance': 'rgba(245, 158, 11, 0.9)'
            };
            return colors[type] || 'rgba(59, 130, 246, 0.9)';
        }

        // 获取类型图标
        function getTypeIcon(type) {
            const icons = {
                'attraction': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>',
                'facility': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>',
                'entrance': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>'
            };
            return icons[type] || icons['attraction'];
        }

        // 添加公园边界
        function addParkBoundary() {
            const parkBounds = [
                [117.2421, 31.8266],
                [117.2501, 31.8266],
                [117.2501, 31.8146],
                [117.2421, 31.8146],
                [117.2421, 31.8266]
            ];

            const polygon = new AMap.Polygon({
                path: parkBounds,
                strokeColor: '#10B981',
                strokeWeight: 2,
                strokeOpacity: 0.8,
                fillColor: '#10B981',
                fillOpacity: 0.1,
                strokeStyle: 'dashed'
            });

            map.add(polygon);
        }
        
        // 切换地图类型
        function toggleMapType() {
            if (currentMapType === 'standard') {
                map.setMapStyle('amap://styles/satellite');
                currentMapType = 'satellite';
                document.getElementById('mapTypeText').textContent = '标准图';
            } else {
                map.setMapStyle('amap://styles/normal');
                currentMapType = 'standard';
                document.getElementById('mapTypeText').textContent = '卫星图';
            }
        }

        // 定位用户位置
        function locateUser() {
            geolocation.getCurrentPosition((status, result) => {
                if (status === 'complete') {
                    map.setCenter(result.position);
                    map.setZoom(18);

                    // 添加用户位置标记
                    const userMarker = new AMap.Marker({
                        position: result.position,
                        content: `
                            <div style="background: #ef4444; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3);"></div>
                        `,
                        anchor: 'center'
                    });
                    map.add(userMarker);

                    showToast('已定位到您的位置');
                } else {
                    showToast('定位失败，请检查定位权限');
                }
            });
        }

        // 重置地图视图
        function resetMapView() {
            map.setCenter(parkCenter);
            map.setZoom(16);
            showToast('已重置到公园视图');
        }

        // 显示交通图层
        function showTrafficLayer() {
            if (!trafficLayer) {
                trafficLayer = new AMap.TileLayer.Traffic({
                    zIndex: 10
                });
                map.add(trafficLayer);
                showToast('已显示实时交通');
            } else {
                map.remove(trafficLayer);
                trafficLayer = null;
                showToast('已关闭交通图层');
            }
        }

        // 显示景点信息
        function showPointInfo(pointId) {
            const info = pointsInfo[pointId];
            if (info) {
                document.getElementById('popupTitle').textContent = info.title;
                document.getElementById('popupDescription').textContent = info.description;
                document.getElementById('infoPopup').style.display = 'block';

                // 地图中心移动到该景点
                map.setCenter(info.position);
                map.setZoom(18);
            }
        }
        
        // 关闭弹窗
        function closePopup() {
            document.getElementById('infoPopup').style.display = 'none';
        }
        
        // 获取导航
        function getDirections() {
            const currentPoint = document.getElementById('popupTitle').textContent;
            const pointId = Object.keys(pointsInfo).find(id => pointsInfo[id].title === currentPoint);

            if (pointId && pointsInfo[pointId]) {
                const position = pointsInfo[pointId].position;
                // 调用高德地图导航
                const url = `https://uri.amap.com/navigation?to=${position[0]},${position[1]}&toname=${currentPoint}&src=myapp&coordinate=gaode&callnative=1`;
                window.open(url);
                showToast('正在打开导航...');
            }
            closePopup();
        }

        // 打开导航到公园
        function openNavigation() {
            const url = `https://uri.amap.com/navigation?to=${parkCenter[0]},${parkCenter[1]}&toname=骆岗公园&src=myapp&coordinate=gaode&callnative=1`;
            window.open(url);
            showToast('正在打开导航应用...');
        }
        
        // 分享位置
        function shareLocation() {
            showToast('位置信息已复制到剪贴板');
        }
        
        // 添加收藏
        function addToFavorites() {
            const button = event.currentTarget;
            const icon = button.querySelector('svg');
            
            if (button.classList.contains('favorited')) {
                button.classList.remove('favorited', 'text-red-500');
                button.classList.add('text-gray-400');
                icon.setAttribute('fill', 'none');
                showToast('已取消收藏');
            } else {
                button.classList.add('favorited', 'text-red-500');
                button.classList.remove('text-gray-400');
                icon.setAttribute('fill', 'currentColor');
                showToast('已添加到收藏');
            }
        }
        
        // 切换地图模式（全屏）
        function toggleMapMode() {
            const mapContainer = document.getElementById('amapContainer');
            if (mapContainer.requestFullscreen) {
                mapContainer.requestFullscreen();
            } else if (mapContainer.webkitRequestFullscreen) {
                mapContainer.webkitRequestFullscreen();
            } else if (mapContainer.mozRequestFullScreen) {
                mapContainer.mozRequestFullScreen();
            } else {
                showToast('您的浏览器不支持全屏功能');
                return;
            }

            // 延迟调整地图大小
            setTimeout(() => {
                map.getSize();
            }, 500);

            showToast('已进入全屏模式，按ESC退出');
        }
        
        // 打开智能对话
        function openChat() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        // 加入行程
        function addToTrip() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-行程规划.html';
            }, 300);
        }
        
        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }
        
        // 点击地图空白区域关闭弹窗
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.amap-marker') && !e.target.closest('#infoPopup')) {
                closePopup();
            }
        });

        // 页面加载完成后初始化地图
        window.onload = function() {
            initMap();
        };

        // 监听全屏变化
        document.addEventListener('fullscreenchange', function() {
            setTimeout(() => {
                if (map) {
                    map.getSize();
                }
            }, 100);
        });
    </script>
</body>
</html>
