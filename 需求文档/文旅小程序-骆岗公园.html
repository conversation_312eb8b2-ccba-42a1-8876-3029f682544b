<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅小程序 - 骆岗公园</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444'
                    },
                    spacing: {
                        'safe': 'env(safe-area-inset-bottom)'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body::-webkit-scrollbar {
            display: none;
        }
        
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
        
        .virtual-assistant {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .map-container {
            position: relative;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 50%, #e8f5e8 100%);
            border-radius: 20px;
            overflow: hidden;
        }
        
        .map-point {
            position: absolute;
            width: 40px;
            height: 40px;
            background: rgba(59, 130, 246, 0.9);
            border: 3px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }
        
        .map-point:hover {
            transform: scale(1.2);
            background: rgba(16, 185, 129, 0.9);
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }
        
        .map-label {
            position: absolute;
            background: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: #374151;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            white-space: nowrap;
            pointer-events: none;
        }
        
        .park-path {
            position: absolute;
            border: 2px dashed #10B981;
            border-radius: 20px;
            opacity: 0.6;
        }
        
        .park-area {
            position: absolute;
            background: rgba(16, 185, 129, 0.1);
            border: 2px solid rgba(16, 185, 129, 0.3);
            border-radius: 15px;
        }
        
        .info-popup {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 300px;
            z-index: 1000;
            display: none;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white px-4 py-3 shadow-sm sticky top-0 z-30">
        <div class="flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            
            <div class="flex-1">
                <h1 class="text-lg font-bold text-gray-900">骆岗公园</h1>
                <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>合肥市包河区骆岗街道</span>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <button onclick="shareLocation()" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                    </svg>
                </button>
                <button onclick="addToFavorites()" class="p-2 text-gray-400 hover:text-red-500">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- 公园信息卡片 -->
    <section class="px-4 py-4">
        <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-green-600 text-lg">🌳</span>
                    </div>
                    <div>
                        <h2 class="font-semibold text-gray-900">城市绿肺，生态乐园</h2>
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                4.8分
                            </span>
                            <span>免费开放</span>
                            <span>全天24小时</span>
                        </div>
                    </div>
                </div>
                <button onclick="openNavigation()" class="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium">
                    导航
                </button>
            </div>
        </div>
    </section>

    <!-- 手绘地图区域 -->
    <section class="px-4 pb-4">
        <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold text-gray-900">公园导览图</h3>
                <button onclick="toggleMapMode()" class="text-sm text-primary">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                    </svg>
                    全屏查看
                </button>
            </div>
            
            <!-- 手绘地图容器 -->
            <div class="map-container" style="height: 400px; position: relative;">
                <!-- 公园区域背景 -->
                <div class="park-area" style="top: 10%; left: 10%; width: 80%; height: 80%;"></div>
                
                <!-- 步道路径 -->
                <div class="park-path" style="top: 20%; left: 15%; width: 70%; height: 2px;"></div>
                <div class="park-path" style="top: 20%; left: 15%; width: 2px; height: 60%;"></div>
                <div class="park-path" style="top: 80%; left: 15%; width: 70%; height: 2px;"></div>
                <div class="park-path" style="top: 20%; right: 15%; width: 2px; height: 60%;"></div>
                
                <!-- 景点标注点 -->
                <!-- 主入口 -->
                <div class="map-point" style="top: 85%; left: 45%;" onclick="showPointInfo('main-entrance')">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    </svg>
                </div>
                <div class="map-label" style="top: 92%; left: 35%;">主入口</div>
                
                <!-- 湖心亭 -->
                <div class="map-point" style="top: 45%; left: 50%;" onclick="showPointInfo('lake-pavilion')">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div class="map-label" style="top: 52%; left: 42%;">湖心亭</div>
                
                <!-- 健身区 -->
                <div class="map-point" style="top: 25%; left: 25%;" onclick="showPointInfo('fitness-area')">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="map-label" style="top: 32%; left: 15%;">健身区</div>
                
                <!-- 儿童乐园 -->
                <div class="map-point" style="top: 25%; left: 75%;" onclick="showPointInfo('children-playground')">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-5-10a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div class="map-label" style="top: 32%; left: 65%;">儿童乐园</div>
                
                <!-- 樱花林 -->
                <div class="map-point" style="top: 60%; left: 25%;" onclick="showPointInfo('cherry-forest')">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>
                <div class="map-label" style="top: 67%; left: 15%;">樱花林</div>
                
                <!-- 观景台 -->
                <div class="map-point" style="top: 60%; left: 75%;" onclick="showPointInfo('viewing-platform')">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <div class="map-label" style="top: 67%; left: 65%;">观景台</div>
                
                <!-- 停车场 -->
                <div class="map-point" style="top: 15%; left: 50%;" onclick="showPointInfo('parking')">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div class="map-label" style="top: 8%; left: 42%;">停车场</div>
            </div>
            
            <!-- 图例 -->
            <div class="mt-4 p-3 bg-gray-50 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">图例说明</h4>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-primary rounded-full"></div>
                        <span class="text-gray-600">景点位置</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-1 border-2 border-dashed border-green-500"></div>
                        <span class="text-gray-600">步行道路</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                        <span class="text-gray-600">绿化区域</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-gray-600">点击景点查看详情</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 景点信息弹窗 -->
    <div id="infoPopup" class="info-popup">
        <div class="flex items-start justify-between mb-3">
            <h4 id="popupTitle" class="font-semibold text-gray-900"></h4>
            <button onclick="closePopup()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <p id="popupDescription" class="text-sm text-gray-600 mb-3"></p>
        <div class="flex space-x-2">
            <button onclick="getDirections()" class="flex-1 bg-primary text-white py-2 px-3 rounded-lg text-sm">
                前往
            </button>
            <button onclick="closePopup()" class="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm">
                关闭
            </button>
        </div>
    </div>

    <!-- 加入行程按钮 -->
    <div class="fixed left-4 bottom-24 z-40">
        <button onclick="addToTrip()" class="bg-purple-500 text-white px-4 py-3 rounded-full shadow-lg hover:bg-purple-600 transition-colors flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span class="text-sm font-medium">加入行程</span>
        </button>
    </div>

    <!-- 虚拟人助手 -->
    <div class="fixed right-4 bottom-24 z-40">
        <div class="relative">
            <div class="virtual-assistant w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-shadow" onclick="openChat()">
                <div class="relative">
                    <div class="w-12 h-12 bg-yellow-200 rounded-full relative">
                        <div class="absolute top-3 left-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute top-3 right-2 w-2 h-2 bg-black rounded-full"></div>
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-4 h-2 border-2 border-black border-t-0 rounded-b-full"></div>
                        <div class="absolute top-4 left-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                        <div class="absolute top-4 right-0 w-2 h-1 bg-pink-300 rounded-full opacity-60"></div>
                    </div>
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-500 rounded-t-full"></div>
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-10 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>
            
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div class="w-full h-full bg-green-500 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <script>
        // 景点信息数据
        const pointsInfo = {
            'main-entrance': {
                title: '主入口',
                description: '骆岗公园主要入口，设有游客服务中心、停车指引和公园导览图。这里是开始游览的最佳起点。'
            },
            'lake-pavilion': {
                title: '湖心亭',
                description: '公园的标志性建筑，位于人工湖中央。亭内可休憩观景，是拍照留念的热门地点，尤其适合日出日落时分。'
            },
            'fitness-area': {
                title: '健身区',
                description: '配备各种户外健身器材，适合晨练和日常锻炼。周围绿树成荫，空气清新，是市民健身的好去处。'
            },
            'children-playground': {
                title: '儿童乐园',
                description: '专为儿童设计的游乐区域，有滑梯、秋千、攀爬架等设施。地面铺设安全软垫，家长可放心让孩子玩耍。'
            },
            'cherry-forest': {
                title: '樱花林',
                description: '春季樱花盛开时是公园最美的景观之一。粉色花海浪漫迷人，是情侣约会和摄影爱好者的首选地点。'
            },
            'viewing-platform': {
                title: '观景台',
                description: '公园制高点，可俯瞰整个公园全景。设有休息座椅和望远镜，是观赏日落和城市夜景的绝佳位置。'
            },
            'parking': {
                title: '停车场',
                description: '免费停车场，可容纳200余辆车。设有电动车充电桩和无障碍停车位，为游客提供便利的停车服务。'
            }
        };
        
        // 显示景点信息
        function showPointInfo(pointId) {
            const info = pointsInfo[pointId];
            if (info) {
                document.getElementById('popupTitle').textContent = info.title;
                document.getElementById('popupDescription').textContent = info.description;
                document.getElementById('infoPopup').style.display = 'block';
            }
        }
        
        // 关闭弹窗
        function closePopup() {
            document.getElementById('infoPopup').style.display = 'none';
        }
        
        // 获取导航
        function getDirections() {
            showToast('正在为您规划路线...');
            closePopup();
        }
        
        // 打开导航
        function openNavigation() {
            showToast('正在打开导航应用...');
        }
        
        // 分享位置
        function shareLocation() {
            showToast('位置信息已复制到剪贴板');
        }
        
        // 添加收藏
        function addToFavorites() {
            const button = event.currentTarget;
            const icon = button.querySelector('svg');
            
            if (button.classList.contains('favorited')) {
                button.classList.remove('favorited', 'text-red-500');
                button.classList.add('text-gray-400');
                icon.setAttribute('fill', 'none');
                showToast('已取消收藏');
            } else {
                button.classList.add('favorited', 'text-red-500');
                button.classList.remove('text-gray-400');
                icon.setAttribute('fill', 'currentColor');
                showToast('已添加到收藏');
            }
        }
        
        // 切换地图模式
        function toggleMapMode() {
            showToast('全屏地图功能开发中...');
        }
        
        // 打开智能对话
        function openChat() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-智能对话.html';
            }, 300);
        }

        // 加入行程
        function addToTrip() {
            document.body.style.opacity = '0.8';
            document.body.style.transform = 'scale(0.95)';
            document.body.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                window.location.href = '文旅小程序-行程规划.html';
            }, 300);
        }
        
        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -20px)';
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }
        
        // 点击地图空白区域关闭弹窗
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.map-point') && !e.target.closest('#infoPopup')) {
                closePopup();
            }
        });
    </script>
</body>
</html>
