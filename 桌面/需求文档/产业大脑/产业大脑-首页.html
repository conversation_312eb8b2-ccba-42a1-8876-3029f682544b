<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业大脑 - 首页概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
        }
        .quick-entry:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }
        .news-item:hover {
            background: #f8fafc;
        }
        .chart-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .indicator-up {
            color: #10b981;
        }
        .indicator-down {
            color: #ef4444;
        }
        .data-highlight {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">产业大脑</h1>
                    </div>
                    <nav class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="text-blue-600 border-b-2 border-blue-600 px-3 py-2 text-sm font-medium">首页</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">企业管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">产业管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">项目管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">产业招商</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">产业运营</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">系统管理</a>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                        </svg>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">管理员</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold text-gray-900">产业概览</h2>
            <p class="mt-1 text-sm text-gray-500">实时监控产业发展态势，洞察产业发展机遇</p>
        </div>

        <!-- 关键指标数据 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 产业规模 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">产业规模</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">2,847</p>
                            <p class="ml-2 text-sm text-gray-600">亿元</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 12.5%</span>
                            <span class="text-xs text-gray-500 ml-1">同比增长</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 营收增速 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">营收增速</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">15.8%</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 2.3%</span>
                            <span class="text-xs text-gray-500 ml-1">环比上升</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 招商项目 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">招商项目</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">156</p>
                            <p class="ml-2 text-sm text-gray-600">个</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 23</span>
                            <span class="text-xs text-gray-500 ml-1">本月新增</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创新人才 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">创新人才</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">12,847</p>
                            <p class="ml-2 text-sm text-gray-600">人</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 8.9%</span>
                            <span class="text-xs text-gray-500 ml-1">同比增长</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产业链供需数据 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 产业链上游 -->
            <div class="chart-container p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">产业链上游供给</h3>
                    <button class="text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                </div>
                <div id="upstreamChart" style="height: 300px;"></div>
            </div>

            <!-- 产业链下游 -->
            <div class="chart-container p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">产业链下游需求</h3>
                    <button class="text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                </div>
                <div id="downstreamChart" style="height: 300px;"></div>
            </div>
        </div>

        <!-- 快捷入口和最新动态 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 快捷入口 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">快捷入口</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">企业管理</span>
                        </a>
                        
                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">产业分析</span>
                        </a>
                        
                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">招商项目</span>
                        </a>
                        
                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">产业报告</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 最新动态 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">最新动态</h3>
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">查看更多</a>
                    </div>
                    <div class="space-y-4">
                        <!-- 资讯 -->
                        <div class="news-item p-4 rounded-lg transition-colors duration-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">资讯</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">合肥市发布新一代信息技术产业发展规划</p>
                                    <p class="text-sm text-gray-500 mt-1">规划提出到2025年，新一代信息技术产业规模突破5000亿元...</p>
                                    <p class="text-xs text-gray-400 mt-2">2024-01-15 14:30</p>
                                </div>
                            </div>
                        </div>

                        <!-- 政策 -->
                        <div class="news-item p-4 rounded-lg transition-colors duration-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">政策</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">关于支持制造业高质量发展的若干政策措施</p>
                                    <p class="text-sm text-gray-500 mt-1">对符合条件的制造业企业给予最高1000万元资金支持...</p>
                                    <p class="text-xs text-gray-400 mt-2">2024-01-14 09:15</p>
                                </div>
                            </div>
                        </div>

                        <!-- 产业报告 -->
                        <div class="news-item p-4 rounded-lg transition-colors duration-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">报告</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">2024年第一季度产业发展分析报告</p>
                                    <p class="text-sm text-gray-500 mt-1">本季度产业整体呈现稳中向好态势，重点领域增长强劲...</p>
                                    <p class="text-xs text-gray-400 mt-2">2024-01-13 16:45</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化产业链上游供给图表
        function initUpstreamChart() {
            const upstreamChart = echarts.init(document.getElementById('upstreamChart'));
            const upstreamOption = {
                title: {
                    text: '供给能力分析',
                    textStyle: {
                        fontSize: 14,
                        color: '#374151'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['产能', '供给量', '库存'],
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['原材料', '零部件', '设备', '技术服务', '物流'],
                    axisLabel: {
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 12
                    }
                },
                series: [
                    {
                        name: '产能',
                        type: 'bar',
                        data: [120, 200, 150, 80, 70],
                        itemStyle: {
                            color: '#3b82f6'
                        }
                    },
                    {
                        name: '供给量',
                        type: 'bar',
                        data: [100, 180, 130, 75, 65],
                        itemStyle: {
                            color: '#10b981'
                        }
                    },
                    {
                        name: '库存',
                        type: 'bar',
                        data: [20, 30, 25, 15, 10],
                        itemStyle: {
                            color: '#f59e0b'
                        }
                    }
                ]
            };
            upstreamChart.setOption(upstreamOption);
        }

        // 初始化产业链下游需求图表
        function initDownstreamChart() {
            const downstreamChart = echarts.init(document.getElementById('downstreamChart'));
            const downstreamOption = {
                title: {
                    text: '需求趋势分析',
                    textStyle: {
                        fontSize: 14,
                        color: '#374151'
                    }
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['市场需求', '订单量', '预期需求'],
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['消费电子', '汽车制造', '工业设备', '医疗器械', '新能源'],
                    axisLabel: {
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 12
                    }
                },
                series: [
                    {
                        name: '市场需求',
                        type: 'line',
                        data: [220, 182, 191, 234, 290],
                        smooth: true,
                        itemStyle: {
                            color: '#8b5cf6'
                        },
                        lineStyle: {
                            width: 3
                        }
                    },
                    {
                        name: '订单量',
                        type: 'line',
                        data: [200, 170, 180, 210, 260],
                        smooth: true,
                        itemStyle: {
                            color: '#06b6d4'
                        },
                        lineStyle: {
                            width: 3
                        }
                    },
                    {
                        name: '预期需求',
                        type: 'line',
                        data: [240, 200, 210, 250, 320],
                        smooth: true,
                        itemStyle: {
                            color: '#ef4444'
                        },
                        lineStyle: {
                            width: 3,
                            type: 'dashed'
                        }
                    }
                ]
            };
            downstreamChart.setOption(downstreamOption);
        }

        // 页面加载完成后初始化图表
        window.addEventListener('load', function() {
            initUpstreamChart();
            initDownstreamChart();
        });

        // 响应式图表调整
        window.addEventListener('resize', function() {
            const upstreamChart = echarts.getInstanceByDom(document.getElementById('upstreamChart'));
            const downstreamChart = echarts.getInstanceByDom(document.getElementById('downstreamChart'));

            if (upstreamChart) {
                upstreamChart.resize();
            }
            if (downstreamChart) {
                downstreamChart.resize();
            }
        });

        // 数据刷新功能
        function refreshData() {
            // 模拟数据刷新
            console.log('刷新数据...');
            // 这里可以添加实际的数据刷新逻辑
        }

        // 定时刷新数据（每5分钟）
        setInterval(refreshData, 5 * 60 * 1000);
    </script>
</body>
</html>
