<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业大脑 - 首页概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
        }
        .quick-entry:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }
        .news-item:hover {
            background: #f8fafc;
        }
        .chart-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .indicator-up {
            color: #10b981;
        }
        .indicator-down {
            color: #ef4444;
        }
        .data-highlight {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">产业大脑</h1>
                    </div>
                    <nav class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="text-blue-600 border-b-2 border-blue-600 px-3 py-2 text-sm font-medium">首页</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">企业管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">产业管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">项目管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">产业招商</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">产业运营</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">系统管理</a>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                        </svg>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">管理员</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold text-gray-900">产业概览</h2>
            <p class="mt-1 text-sm text-gray-500">实时监控产业发展态势，洞察产业发展机遇</p>
        </div>

        <!-- 关键指标数据 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 产业规模 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">产业规模</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">2,847</p>
                            <p class="ml-2 text-sm text-gray-600">亿元</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 12.5%</span>
                            <span class="text-xs text-gray-500 ml-1">同比增长</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 企业营收 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">企业营收</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">1,956</p>
                            <p class="ml-2 text-sm text-gray-600">亿元</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 18.2%</span>
                            <span class="text-xs text-gray-500 ml-1">同比增长</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 上市企业 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">上市企业</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">89</p>
                            <p class="ml-2 text-sm text-gray-600">家</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 6</span>
                            <span class="text-xs text-gray-500 ml-1">本年新增</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 龙头企业 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">龙头企业</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">156</p>
                            <p class="ml-2 text-sm text-gray-600">家</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 12</span>
                            <span class="text-xs text-gray-500 ml-1">本年认定</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行指标 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 专精特新企业 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">专精特新企业</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">423</p>
                            <p class="ml-2 text-sm text-gray-600">家</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 67</span>
                            <span class="text-xs text-gray-500 ml-1">本年新增</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 招商项目 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">招商项目</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">234</p>
                            <p class="ml-2 text-sm text-gray-600">个</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 28</span>
                            <span class="text-xs text-gray-500 ml-1">本月新增</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创新人才 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">创新人才</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">12,847</p>
                            <p class="ml-2 text-sm text-gray-600">人</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 8.9%</span>
                            <span class="text-xs text-gray-500 ml-1">同比增长</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 研发投入 -->
            <div class="metric-card card-hover rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">研发投入</p>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold data-highlight">186.5</p>
                            <p class="ml-2 text-sm text-gray-600">亿元</p>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="indicator-up text-xs font-medium">↗ 15.6%</span>
                            <span class="text-xs text-gray-500 ml-1">同比增长</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 快捷入口和最新动态 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 快捷入口 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">快捷入口</h3>
                        <button onclick="openCustomizeModal()" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            自定义
                        </button>
                    </div>
                    <div class="grid grid-cols-2 gap-4" id="quickEntries">
                        <!-- 默认快捷入口，可通过自定义修改 -->
                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md" data-entry="enterprise">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">企业管理</span>
                        </a>

                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md" data-entry="analysis">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">产业分析</span>
                        </a>

                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md" data-entry="investment">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">招商项目</span>
                        </a>

                        <a href="#" class="quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md" data-entry="report">
                            <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-medium">产业报告</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 最新动态 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">最新动态</h3>
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">查看更多</a>
                    </div>
                    <div class="space-y-4">
                        <!-- 资讯 -->
                        <div class="news-item p-4 rounded-lg transition-colors duration-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">资讯</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">合肥市发布新一代信息技术产业发展规划</p>
                                    <p class="text-sm text-gray-500 mt-1">规划提出到2025年，新一代信息技术产业规模突破5000亿元...</p>
                                    <p class="text-xs text-gray-400 mt-2">2024-01-15 14:30</p>
                                </div>
                            </div>
                        </div>

                        <!-- 政策 -->
                        <div class="news-item p-4 rounded-lg transition-colors duration-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">政策</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">关于支持制造业高质量发展的若干政策措施</p>
                                    <p class="text-sm text-gray-500 mt-1">对符合条件的制造业企业给予最高1000万元资金支持...</p>
                                    <p class="text-xs text-gray-400 mt-2">2024-01-14 09:15</p>
                                </div>
                            </div>
                        </div>

                        <!-- 产业报告 -->
                        <div class="news-item p-4 rounded-lg transition-colors duration-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">报告</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">2024年第一季度产业发展分析报告</p>
                                    <p class="text-sm text-gray-500 mt-1">本季度产业整体呈现稳中向好态势，重点领域增长强劲...</p>
                                    <p class="text-xs text-gray-400 mt-2">2024-01-13 16:45</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义快捷入口模态框 -->
    <div id="customizeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">自定义快捷入口</h3>
                        <button onclick="closeCustomizeModal()" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <p class="text-sm text-gray-600 mb-4">选择您常用的功能作为快捷入口（最多4个）</p>

                    <!-- 可选功能列表 -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="enterprise" checked>
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">企业管理</span>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="analysis" checked>
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">产业分析</span>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="investment" checked>
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">招商项目</span>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="report" checked>
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">产业报告</span>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="policy">
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">政策服务</span>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="park">
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">产业园区</span>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="talent">
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">人才服务</span>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="checkbox" class="mr-3" value="finance">
                            <div class="flex items-center">
                                <div class="w-8 h-8 mr-3 text-blue-600">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">金融服务</span>
                            </div>
                        </label>
                    </div>

                    <div class="text-xs text-gray-500 mb-4">
                        <span id="selectedCount">4</span>/4 已选择
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="closeCustomizeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                    <button onclick="saveCustomization()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 快捷入口配置
        const quickEntryOptions = {
            enterprise: {
                name: '企业管理',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>',
                url: '#'
            },
            analysis: {
                name: '产业分析',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>',
                url: '#'
            },
            investment: {
                name: '招商项目',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>',
                url: '#'
            },
            report: {
                name: '产业报告',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
                url: '#'
            },
            policy: {
                name: '政策服务',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>',
                url: '#'
            },
            park: {
                name: '产业园区',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>',
                url: '#'
            },
            talent: {
                name: '人才服务',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>',
                url: '#'
            },
            finance: {
                name: '金融服务',
                icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>',
                url: '#'
            }
        };

        // 当前选中的快捷入口
        let selectedEntries = ['enterprise', 'analysis', 'investment', 'report'];

        // 打开自定义模态框
        function openCustomizeModal() {
            document.getElementById('customizeModal').classList.remove('hidden');
            updateCheckboxes();
        }

        // 关闭自定义模态框
        function closeCustomizeModal() {
            document.getElementById('customizeModal').classList.add('hidden');
        }

        // 更新复选框状态
        function updateCheckboxes() {
            const checkboxes = document.querySelectorAll('#customizeModal input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectedEntries.includes(checkbox.value);
                checkbox.addEventListener('change', updateSelectedCount);
            });
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const checkedBoxes = document.querySelectorAll('#customizeModal input[type="checkbox"]:checked');
            const count = checkedBoxes.length;
            document.getElementById('selectedCount').textContent = count;

            // 限制最多选择4个
            if (count >= 4) {
                const uncheckedBoxes = document.querySelectorAll('#customizeModal input[type="checkbox"]:not(:checked)');
                uncheckedBoxes.forEach(box => box.disabled = true);
            } else {
                const allBoxes = document.querySelectorAll('#customizeModal input[type="checkbox"]');
                allBoxes.forEach(box => box.disabled = false);
            }
        }

        // 保存自定义设置
        function saveCustomization() {
            const checkedBoxes = document.querySelectorAll('#customizeModal input[type="checkbox"]:checked');
            selectedEntries = Array.from(checkedBoxes).map(box => box.value);

            // 更新快捷入口显示
            updateQuickEntries();

            // 保存到本地存储
            localStorage.setItem('quickEntries', JSON.stringify(selectedEntries));

            closeCustomizeModal();

            // 显示成功提示
            showToast('快捷入口已更新');
        }

        // 更新快捷入口显示
        function updateQuickEntries() {
            const container = document.getElementById('quickEntries');
            container.innerHTML = '';

            selectedEntries.forEach(entryKey => {
                const entry = quickEntryOptions[entryKey];
                if (entry) {
                    const entryElement = document.createElement('a');
                    entryElement.href = entry.url;
                    entryElement.className = 'quick-entry p-4 rounded-lg border border-gray-200 text-center transition-all duration-300 hover:shadow-md';
                    entryElement.setAttribute('data-entry', entryKey);
                    entryElement.innerHTML = `
                        <div class="w-8 h-8 mx-auto mb-2 text-blue-600">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                ${entry.icon}
                            </svg>
                        </div>
                        <span class="text-sm font-medium">${entry.name}</span>
                    `;
                    container.appendChild(entryElement);
                }
            });
        }

        // 显示提示消息
        function showToast(message) {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = message;
            document.body.appendChild(toast);

            // 3秒后自动消失
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 数据刷新功能
        function refreshData() {
            // 模拟数据刷新
            console.log('刷新数据...');
            // 这里可以添加实际的数据刷新逻辑
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            // 从本地存储加载快捷入口设置
            const savedEntries = localStorage.getItem('quickEntries');
            if (savedEntries) {
                selectedEntries = JSON.parse(savedEntries);
                updateQuickEntries();
            }
        });

        // 定时刷新数据（每5分钟）
        setInterval(refreshData, 5 * 60 * 1000);

        // 点击模态框外部关闭
        document.getElementById('customizeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCustomizeModal();
            }
        });
    </script>
</body>
</html>
