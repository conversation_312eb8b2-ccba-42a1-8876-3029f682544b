<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>包公园 - 某市文旅小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        html, body {
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 flex flex-col h-screen">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <a href="discover.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-lg font-bold text-gray-800">景点详情</h1>
            <div class="w-6"></div> <!-- 保持对称 -->
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="flex-1 overflow-y-auto pb-20">
        <!-- 景点封面图 -->
        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1470004914212-05527e49370b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1350&q=80&fit=max')"></div>

        <!-- 景点基本信息 -->
        <div class="container mx-auto px-4 mt-4">
            <div class="flex justify-between items-start">
                <div>
                    <h2 class="text-xl font-bold text-gray-800">包公园</h2>
                    <div class="flex items-center mt-1">
                        <i class="fas fa-star text-yellow-400"></i>
                        <span class="text-gray-600 ml-1">4.7 (980条评价)</span>
                    </div>
                </div>
                <button class="text-red-500">
                    <i class="fas fa-heart text-xl"></i>
                </button>
            </div>

            <!-- 景点标签 -->
            <div class="flex flex-wrap gap-2 mt-3">
                <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs">历史文化</span>
                <span class="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs">4A景区</span>
                <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs">适合拍照</span>
            </div>

            <!-- 实用信息 -->
            <div class="mt-6 space-y-3">
                <div class="flex items-center">
                    <i class="fas fa-map-marker-alt text-gray-400 w-6"></i>
                    <span class="text-gray-600">合肥市包河区芜湖路72号</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-clock text-gray-400 w-6"></i>
                    <span class="text-gray-600">08:00-18:00 (17:30停止入场)</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-ticket-alt text-gray-400 w-6"></i>
                    <span class="text-gray-600">门票：免费开放</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-subway text-gray-400 w-6"></i>
                    <span class="text-gray-600">地铁：1号线包公园站D出口步行5分钟</span>
                </div>
            </div>

            <!-- 景点介绍 -->
            <div class="mt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">景点介绍</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    包公园是为纪念北宋著名清官包拯而建的园林建筑群，始建于1066年，占地30.5公顷。
                    园内主要建筑包括包公祠、包公墓、清风阁等，展示了包拯生平事迹和廉政文化。
                    整个园区建筑风格古朴典雅，融合了江南园林特色，是了解包公文化和休闲游览的好去处。
                </p>
            </div>

            <!-- 图片展示 -->
            <div class="mt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">景点图片</h3>
                <div class="grid grid-cols-2 gap-2">
                    <div class="h-32 bg-cover bg-center rounded" style="background-image: url('https://images.unsplash.com/photo-1470004914212-05527e49370b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                    <div class="h-32 bg-cover bg-center rounded" style="background-image: url('https://images.unsplash.com/photo-1553826059-7a090c4bd362?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                    <div class="h-32 bg-cover bg-center rounded" style="background-image: url('https://images.unsplash.com/photo-1506197603052-3cc9c878a1bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                    <div class="h-32 bg-cover bg-center rounded" style="background-image: url('https://images.unsplash.com/photo-1470115636492-6d2b56f9146d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部操作栏 -->
    <div class="bg-white border-t border-gray-200 py-3 px-4 fixed bottom-0 w-full">
        <div class="container mx-auto flex justify-between items-center">
            <div class="text-gray-500 text-sm">
                <i class="fas fa-phone-alt mr-1"></i>
                <span>0551-62865000</span>
            </div>
            <button class="bg-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                导航前往
            </button>
        </div>
    </div>
</body>
</html>