<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>某市文旅小程序 - 行程规划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        html, body {
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 flex flex-col h-screen">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <a href="discover.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-lg font-bold text-gray-800">行程规划</h1>
            <div class="w-6"></div> <!-- 保持对称 -->
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="flex-1 overflow-y-auto pb-20">
        <!-- 智能规划表单 -->
        <div class="container mx-auto px-4 mt-4">
            <div class="bg-white rounded-xl shadow-sm p-4">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">智能行程规划</h2>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">出行日期</label>
                        <input type="date" class="w-full bg-gray-100 rounded-lg px-3 py-2 border border-gray-200">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">游玩天数</label>
                        <select class="w-full bg-gray-100 rounded-lg px-3 py-2 border border-gray-200">
                            <option>1天</option>
                            <option>2天1晚</option>
                            <option>3天2晚</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">兴趣偏好</label>
                        <div class="flex flex-wrap gap-2">
                            <button class="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm">自然风光</button>
                            <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">历史文化</button>
                            <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">美食体验</button>
                            <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">亲子游玩</button>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">同行人数</label>
                        <input type="number" min="1" value="2" class="w-full bg-gray-100 rounded-lg px-3 py-2 border border-gray-200">
                    </div>
                    
                    <button class="w-full bg-blue-500 text-white py-2 rounded-lg mt-4 font-medium">
                        生成智能行程
                    </button>
                </div>
            </div>
        </div>

        <!-- 手动规划 -->
        <div class="container mx-auto px-4 mt-6">
            <div class="bg-white rounded-xl shadow-sm p-4">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">手动添加景点</h2>
                
                <div class="flex items-center bg-gray-100 rounded-lg px-3 py-2">
                    <i class="fas fa-search text-gray-400 mr-2"></i>
                    <input type="text" placeholder="搜索景点..." class="flex-1 bg-transparent focus:outline-none">
                </div>
                
                <!-- 景点搜索结果示例 -->
                <div class="mt-3 space-y-2">
                    <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-cover bg-center rounded" style="background-image: url('https://images.unsplash.com/photo-1518258726560-ed5a8b3f63d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80&fit=max')"></div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-gray-800">三河古镇</h4>
                                <p class="text-xs text-gray-500">距离8.5km · 历史文化</p>
                            </div>
                        </div>
                        <button class="text-blue-500 text-sm font-medium">添加</button>
                    </div>
                    
                    <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-cover bg-center rounded" style="background-image: url('https://images.unsplash.com/photo-1470004914212-05527e49370b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80&fit=max')"></div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-gray-800">包公园</h4>
                                <p class="text-xs text-gray-500">距离5.2km · 历史文化</p>
                            </div>
                        </div>
                        <button class="text-blue-500 text-sm font-medium">添加</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的行程 -->
        <div class="container mx-auto px-4 mt-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm p-4">
                <div class="flex justify-between items-center mb-3">
                    <h2 class="text-lg font-semibold text-gray-800">我的行程</h2>
                    <button class="text-blue-500 text-sm font-medium">清空</button>
                </div>
                
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <!-- 行程日期 -->
                    <div class="bg-gray-50 px-3 py-2 border-b border-gray-200">
                        <h3 class="text-sm font-medium text-gray-700">10月1日 (周日)</h3>
                    </div>
                    
                    <!-- 行程项目 -->
                    <div class="divide-y divide-gray-200">
                        <div class="p-3 flex items-start">
                            <div class="w-12 h-12 bg-cover bg-center rounded flex-shrink-0" style="background-image: url('https://images.unsplash.com/photo-1518258726560-ed5a8b3f63d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80&fit=max')"></div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <h4 class="text-sm font-medium text-gray-800">三河古镇</h4>
                                    <span class="text-xs text-gray-500">09:00-12:00</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">建议游玩3小时 · 国家5A级旅游景区</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex space-x-2">
                                        <button class="text-xs text-blue-500">详情</button>
                                        <button class="text-xs text-gray-500">调整时间</button>
                                    </div>
                                    <button class="text-red-400 text-sm">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-3 flex items-start">
                            <div class="w-12 h-12 bg-cover bg-center rounded flex-shrink-0" style="background-image: url('https://images.unsplash.com/photo-1499591934245-40b55745b905?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80&fit=max')"></div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <h4 class="text-sm font-medium text-gray-800">古镇文化节</h4>
                                    <span class="text-xs text-gray-500">12:30-13:30</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">特色美食体验 · 三河古镇内</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex space-x-2">
                                        <button class="text-xs text-blue-500">详情</button>
                                        <button class="text-xs text-gray-500">调整时间</button>
                                    </div>
                                    <button class="text-red-400 text-sm">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="w-full bg-blue-500 text-white py-2 rounded-lg mt-4 font-medium">
                    完成规划
                </button>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bg-white border-t border-gray-200 fixed bottom-0 w-full">
        <div class="container mx-auto px-4">
            <div class="flex justify-around py-3">
                <a href="index.html" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-home text-lg"></i>
                    <span class="text-xs mt-1">首页</span>
                </a>
                <a href="discover.html" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-compass text-lg"></i>
                    <span class="text-xs mt-1">发现</span>
                </a>
                <a href="#" class="flex flex-col items-center text-blue-500">
                    <i class="fas fa-route text-lg"></i>
                    <span class="text-xs mt-1">行程</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-concierge-bell text-lg"></i>
                    <span class="text-xs mt-1">服务</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-user text-lg"></i>
                    <span class="text-xs mt-1">我的</span>
                </a>
            </div>
        </div>
    </nav>
</body>
</html>