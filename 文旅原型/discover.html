<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>某市文旅小程序 - 发现</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        html, body {
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 flex flex-col h-screen">
    <!-- 顶部搜索栏 -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <div class="relative">
                <input type="text" placeholder="搜索景点、活动..." 
                    class="w-full bg-gray-100 rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="flex-1 overflow-y-auto pb-20">
        <!-- 分类筛选 -->
        <div class="container mx-auto px-4 mt-4">
            <div class="flex overflow-x-auto space-x-3 pb-2">
                <button class="flex-shrink-0 bg-blue-500 text-white px-4 py-1 rounded-full text-sm">全部</button>
                <button class="flex-shrink-0 bg-white border border-gray-200 px-4 py-1 rounded-full text-sm">自然风光</button>
                <button class="flex-shrink-0 bg-white border border-gray-200 px-4 py-1 rounded-full text-sm">历史文化</button>
                <button class="flex-shrink-0 bg-white border border-gray-200 px-4 py-1 rounded-full text-sm">亲子游玩</button>
                <button class="flex-shrink-0 bg-white border border-gray-200 px-4 py-1 rounded-full text-sm">美食购物</button>
                <button class="flex-shrink-0 bg-white border border-gray-200 px-4 py-1 rounded-full text-sm">网红打卡</button>
            </div>
        </div>

        <!-- 附近景点 -->
        <div class="container mx-auto px-4 mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">附近景点</h3>
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="h-40 bg-cover bg-center relative" style="background-image: url('https://images.unsplash.com/photo-1483728642387-6c3bdd6c93e5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1350&q=80&fit=max')">
                    <div class="absolute inset-0 flex items-end p-4 bg-gradient-to-t from-black/60 to-transparent">
                        <div>
                            <h4 class="text-white font-medium">距离您 1.2km</h4>
                            <p class="text-white text-sm mt-1">巢湖湿地公园</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 景点列表 -->
        <div class="container mx-auto px-4 mt-6">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-gray-800">全部景点</h3>
                <a href="#" class="text-sm text-blue-500">查看地图</a>
            </div>
            <div class="space-y-4">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="flex">
                        <div class="w-24 h-24 bg-cover bg-center flex-shrink-0" style="background-image: url('https://images.unsplash.com/photo-1518258726560-ed5a8b3f63d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                        <div class="p-3 flex-1">
                            <h4 class="text-sm font-medium text-gray-800">三河古镇</h4>
                            <p class="text-xs text-gray-500 mt-1">国家5A级旅游景区，具有2500多年历史的水乡古镇</p>
                            <div class="flex items-center mt-2">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <span class="text-xs text-gray-500 ml-1">4.8 (1.2k)</span>
                                <span class="text-xs text-gray-500 ml-3"><i class="fas fa-location-arrow mr-1"></i>12km</span>
                            </div>
                        </div>
                    </div>
                </div>
                <a href="scenic-detail.html" class="bg-white rounded-xl shadow-sm overflow-hidden block">
                    <div class="flex">
                        <div class="w-24 h-24 bg-cover bg-center flex-shrink-0" style="background-image: url('https://images.unsplash.com/photo-1470004914212-05527e49370b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                        <div class="p-3 flex-1">
                            <h4 class="text-sm font-medium text-gray-800">包公园</h4>
                            <p class="text-xs text-gray-500 mt-1">为纪念北宋著名清官包拯而建的园林建筑群</p>
                            <div class="flex items-center mt-2">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <span class="text-xs text-gray-500 ml-1">4.7 (980)</span>
                                <span class="text-xs text-gray-500 ml-3"><i class="fas fa-location-arrow mr-1"></i>8.5km</span>
                            </div>
                        </div>
                    </div>
                </a>
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="flex">
                        <div class="w-24 h-24 bg-cover bg-center flex-shrink-0" style="background-image: url('https://images.unsplash.com/photo-1470115636492-6d2b56f9146d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                        <div class="p-3 flex-1">
                            <h4 class="text-sm font-medium text-gray-800">巢湖风景区</h4>
                            <p class="text-xs text-gray-500 mt-1">中国五大淡水湖之一，湖光山色交相辉映</p>
                            <div class="flex items-center mt-2">
                                <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <span class="text-xs text-gray-500 ml-1">4.9 (1.5k)</span>
                                <span class="text-xs text-gray-500 ml-3"><i class="fas fa-location-arrow mr-1"></i>15km</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bg-white border-t border-gray-200 fixed bottom-0 w-full">
        <div class="container mx-auto px-4">
            <div class="flex justify-around py-3">
                <a href="index.html" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-home text-lg"></i>
                    <span class="text-xs mt-1">首页</span>
                </a>
                <a href="#" class="flex flex-col items-center text-blue-500">
                    <i class="fas fa-compass text-lg"></i>
                    <span class="text-xs mt-1">发现</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-route text-lg"></i>
                    <span class="text-xs mt-1">行程</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-concierge-bell text-lg"></i>
                    <span class="text-xs mt-1">服务</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-user text-lg"></i>
                    <span class="text-xs mt-1">我的</span>
                </a>
            </div>
        </div>
    </nav>
</body>
</html>