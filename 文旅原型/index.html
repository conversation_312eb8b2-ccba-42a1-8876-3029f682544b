<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>某市文旅小程序 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        html, body {
            height: 100%;
            overflow: hidden;
        }
        .hero-image {
            background-image: url('https://images.unsplash.com/photo-1506197603052-3cc9c878a1bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
        }
    </style>
</head>
<body class="bg-gray-50 flex flex-col h-screen">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <i class="fas fa-map-marked-alt text-blue-500 text-xl"></i>
                <h1 class="text-lg font-bold text-gray-800">合肥市文旅</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button class="text-gray-600">
                    <i class="fas fa-search"></i>
                </button>
                <button class="text-gray-600">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="flex-1 overflow-y-auto pb-20">
        <!-- 轮播图 -->
        <div class="hero-image h-48 rounded-b-2xl relative overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <div class="text-center px-4">
                    <h2 class="text-white text-2xl font-bold mb-2">发现城市之美</h2>
                    <p class="text-white">探索本地文化与自然风光</p>
                </div>
            </div>
        </div>

        <!-- 快速入口 -->
        <div class="container mx-auto px-4 mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">快速入口</h3>
            <div class="grid grid-cols-4 gap-4">
                <a href="#" class="flex flex-col items-center">
                    <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-route text-blue-500 text-xl"></i>
                    </div>
                    <span class="text-xs text-gray-700">行程规划</span>
                </a>
                <a href="#" class="flex flex-col items-center">
                    <div class="w-14 h-14 bg-green-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-hotel text-green-500 text-xl"></i>
                    </div>
                    <span class="text-xs text-gray-700">酒店预订</span>
                </a>
                <a href="#" class="flex flex-col items-center">
                    <div class="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-utensils text-purple-500 text-xl"></i>
                    </div>
                    <span class="text-xs text-gray-700">美食推荐</span>
                </a>
                <a href="#" class="flex flex-col items-center">
                    <div class="w-14 h-14 bg-yellow-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-comments text-yellow-500 text-xl"></i>
                    </div>
                    <span class="text-xs text-gray-700">智能导游</span>
                </a>
            </div>
        </div>

        <!-- 热门景点 -->
        <div class="container mx-auto px-4 mt-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">热门景点</h3>
                <a href="#" class="text-sm text-blue-500">查看全部</a>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="h-24 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1518258726560-ed5a8b3f63d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                    <div class="p-2">
                        <h4 class="text-sm font-medium text-gray-800">三河古镇</h4>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                            <span class="text-xs text-gray-500 ml-1">4.8 (1.2k)</span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="h-24 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1470004914212-05527e49370b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                    <div class="p-2">
                        <h4 class="text-sm font-medium text-gray-800">包公园</h4>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                            <span class="text-xs text-gray-500 ml-1">4.7 (980)</span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="h-24 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1470115636492-6d2b56f9146d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                    <div class="p-2">
                        <h4 class="text-sm font-medium text-gray-800">巢湖风景区</h4>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                            <span class="text-xs text-gray-500 ml-1">4.9 (1.5k)</span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="h-24 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1464037866556-6816c9d1c72e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                    <div class="p-2">
                        <h4 class="text-sm font-medium text-gray-800">骆岗公园</h4>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                            <span class="text-xs text-gray-500 ml-1">4.6 (890)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推荐活动 -->
        <div class="container mx-auto px-4 mt-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">推荐活动</h3>
                <a href="#" class="text-sm text-blue-500">查看全部</a>
            </div>
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="h-32 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1499591934245-40b55745b905?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80&fit=max')"></div>
                <div class="p-3">
                    <h4 class="text-sm font-medium text-gray-800">古镇文化节</h4>
                    <p class="text-xs text-gray-500 mt-1">体验传统民俗，品尝地道美食</p>
                    <div class="flex items-center justify-between mt-2">
                        <span class="text-xs text-blue-500">2023.10.1-10.7</span>
                        <button class="text-xs bg-blue-500 text-white px-2 py-1 rounded-full">立即预约</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bg-white border-t border-gray-200 fixed bottom-0 w-full">
        <div class="container mx-auto px-4">
            <div class="flex justify-around py-3">
                <a href="#" class="flex flex-col items-center text-blue-500">
                    <i class="fas fa-home text-lg"></i>
                    <span class="text-xs mt-1">首页</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-compass text-lg"></i>
                    <span class="text-xs mt-1">发现</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-route text-lg"></i>
                    <span class="text-xs mt-1">行程</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-concierge-bell text-lg"></i>
                    <span class="text-xs mt-1">服务</span>
                </a>
                <a href="#" class="flex flex-col items-center text-gray-500">
                    <i class="fas fa-user text-lg"></i>
                    <span class="text-xs mt-1">我的</span>
                </a>
            </div>
        </div>
    </nav>
</body>
</html>