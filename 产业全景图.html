<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业全景图 | 产业大脑系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        background: '#f8fafc',
                        navbar: '#ffffff'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-navbar shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- 左侧：Logo和主导航 -->
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Logo" class="h-8 w-8 rounded">
                        <span class="ml-2 text-xl font-bold text-gray-900">产业大脑</span>
                    </div>
                    
                    <!-- 主导航菜单 -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="首页.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">首页</a>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                企业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="企业登记.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业登记</a>
                                    <a href="企业云图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业云图</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业链管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业链管理</a>
                                    <a href="产业资源管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业资源管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                项目管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商项目管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商项目管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">重点项目管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业招商
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商企业地图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业地图</a>
                                    <a href="招商企业档案.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业档案</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业运营
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业经济看板.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业经济看板</a>
                                    <a href="产业图谱.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业图谱</a>
                                    <a href="产业全景图.html" class="block px-4 py-2 text-sm text-primary bg-blue-50 font-medium">产业全景图</a>
                                    <a href="产业预警.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业预警</a>
                                    <a href="产业报告.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业报告</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                系统管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="账号管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">账号管理</a>
                                    <a href="角色管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">角色管理</a>
                                    <a href="日志管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">日志管理</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：搜索和用户信息 -->
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input type="text" placeholder="搜索..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    
                    <!-- 通知 -->
                    <button class="relative p-2 text-gray-600 hover:text-primary">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                    </button>
                    
                    <!-- 用户头像和信息 -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-primary">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm font-medium">管理员</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">个人设置</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 面包屑导航 -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="首页.html" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        首页
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="#" class="ml-1 text-sm font-medium text-gray-700 hover:text-primary md:ml-2">产业运营</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">产业全景图</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- 页面标题和控制面板 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">产业全景图看板</h1>
                <p class="mt-2 text-gray-600">全方位展示产业发展现状、趋势分析和空间分布</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    导出报告
                </button>
                <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    智能分析
                </button>
                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 筛选控制面板 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">产业类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>新能源汽车</option>
                        <option>智能制造</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>数字经济</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>近一年</option>
                        <option>近三年</option>
                        <option>近五年</option>
                        <option>自定义</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">地区范围</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全国</option>
                        <option>长三角</option>
                        <option>珠三角</option>
                        <option>京津冀</option>
                        <option>成渝地区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">企业规模</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全部规模</option>
                        <option>大型企业</option>
                        <option>中型企业</option>
                        <option>小型企业</option>
                        <option>微型企业</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询分析
                    </button>
                </div>
            </div>
        </div>

        <!-- 产业规模统计 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">产业规模指标</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">财务指标</button>
                    <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">创新指标</button>
                    <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">人力指标</button>
                </div>
            </div>

            <!-- 核心指标卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-blue-700">总营收</h4>
                            <p class="text-2xl font-bold text-blue-900">¥2,856.7亿</p>
                            <p class="text-sm text-blue-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +18.5%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-blue-200">
                            <svg class="h-6 w-6 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-green-700">总税收</h4>
                            <p class="text-2xl font-bold text-green-900">¥428.5亿</p>
                            <p class="text-sm text-green-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +22.3%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-green-200">
                            <svg class="h-6 w-6 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-purple-700">营收增加值</h4>
                            <p class="text-2xl font-bold text-purple-900">¥1,142.3亿</p>
                            <p class="text-sm text-purple-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +15.7%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-purple-200">
                            <svg class="h-6 w-6 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-orange-700">增加值率</h4>
                            <p class="text-2xl font-bold text-orange-900">39.98%</p>
                            <p class="text-sm text-orange-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +2.1%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-orange-200">
                            <svg class="h-6 w-6 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细指标表格 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 财务指标 -->
                <div class="space-y-3">
                    <h4 class="text-base font-semibold text-gray-900 mb-4">财务指标</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">利润总额</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥285.6亿</span>
                                <span class="text-xs text-green-600 ml-2">+24.8%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">税金总额</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥428.5亿</span>
                                <span class="text-xs text-green-600 ml-2">+22.3%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">研发费用</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥171.4亿</span>
                                <span class="text-xs text-green-600 ml-2">+31.2%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">折旧费用</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥142.8亿</span>
                                <span class="text-xs text-green-600 ml-2">+12.5%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 人力指标 -->
                <div class="space-y-3">
                    <h4 class="text-base font-semibold text-gray-900 mb-4">人力与创新指标</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">社保人员</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">156.8万人</span>
                                <span class="text-xs text-green-600 ml-2">+8.5%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">薪酬总额</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥892.3亿</span>
                                <span class="text-xs text-green-600 ml-2">+14.7%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">专利总数</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">28,456件</span>
                                <span class="text-xs text-green-600 ml-2">+42.1%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">研发强度</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">6.0%</span>
                                <span class="text-xs text-green-600 ml-2">+0.8%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- 产业发展趋势 -->
            <div class="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">产业发展趋势</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">产值</button>
                        <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">企业数</button>
                        <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">就业</button>
                    </div>
                </div>

                <!-- 趋势图表区域 -->
                <div class="h-80 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-4 relative">
                    <!-- 模拟图表 -->
                    <div class="absolute inset-4">
                        <!-- Y轴标签 -->
                        <div class="absolute left-0 top-0 bottom-0 flex flex-col justify-between text-xs text-gray-500">
                            <span>3.0万亿</span>
                            <span>2.5万亿</span>
                            <span>2.0万亿</span>
                            <span>1.5万亿</span>
                            <span>1.0万亿</span>
                            <span>0.5万亿</span>
                        </div>

                        <!-- 图表区域 -->
                        <div class="ml-12 mr-4 h-full relative">
                            <!-- 网格线 -->
                            <div class="absolute inset-0">
                                <div class="h-full flex flex-col justify-between">
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                </div>
                            </div>

                            <!-- 趋势线 -->
                            <svg class="absolute inset-0 w-full h-full" viewBox="0 0 400 200">
                                <path d="M 20 160 Q 80 140 120 120 T 200 80 T 280 60 T 360 40"
                                      stroke="#3b82f6" stroke-width="3" fill="none" opacity="0.8"/>
                                <path d="M 20 160 Q 80 140 120 120 T 200 80 T 280 60 T 360 40 L 360 200 L 20 200 Z"
                                      fill="url(#gradient)" opacity="0.3"/>
                                <defs>
                                    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.4" />
                                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.1" />
                                    </linearGradient>
                                </defs>

                                <!-- 数据点 -->
                                <circle cx="20" cy="160" r="4" fill="#3b82f6"/>
                                <circle cx="120" cy="120" r="4" fill="#3b82f6"/>
                                <circle cx="200" cy="80" r="4" fill="#3b82f6"/>
                                <circle cx="280" cy="60" r="4" fill="#3b82f6"/>
                                <circle cx="360" cy="40" r="4" fill="#3b82f6"/>
                            </svg>
                        </div>

                        <!-- X轴标签 -->
                        <div class="absolute bottom-0 left-12 right-4 flex justify-between text-xs text-gray-500">
                            <span>2019</span>
                            <span>2020</span>
                            <span>2021</span>
                            <span>2022</span>
                            <span>2023</span>
                        </div>
                    </div>
                </div>

                <!-- 趋势分析 -->
                <div class="mt-4 grid grid-cols-3 gap-4">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-600 font-medium">年均增长率</p>
                        <p class="text-xl font-bold text-blue-800">15.2%</p>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <p class="text-sm text-green-600 font-medium">预测2024年</p>
                        <p class="text-xl font-bold text-green-800">3.2万亿</p>
                    </div>
                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                        <p class="text-sm text-purple-600 font-medium">增长潜力</p>
                        <p class="text-xl font-bold text-purple-800">高</p>
                    </div>
                </div>
            </div>

            <!-- 产业链分布 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">产业链分布</h3>

                <!-- 产业链环节 -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">上游供应</span>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-green-600">3,456家</p>
                            <p class="text-xs text-gray-500">27.8%</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">核心制造</span>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-blue-600">2,890家</p>
                            <p class="text-xs text-gray-500">23.2%</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">下游应用</span>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-purple-600">4,123家</p>
                            <p class="text-xs text-gray-500">33.1%</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">配套服务</span>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-orange-600">1,987家</p>
                            <p class="text-xs text-gray-500">15.9%</p>
                        </div>
                    </div>
                </div>

                <!-- 产业链健康度 -->
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">产业链健康度</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-600">完整性</span>
                            <div class="flex items-center">
                                <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                                    <div class="w-14 h-2 bg-green-500 rounded-full"></div>
                                </div>
                                <span class="text-xs font-medium text-green-600">88%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-600">稳定性</span>
                            <div class="flex items-center">
                                <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                                    <div class="w-12 h-2 bg-blue-500 rounded-full"></div>
                                </div>
                                <span class="text-xs font-medium text-blue-600">75%</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-600">创新性</span>
                            <div class="flex items-center">
                                <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                                    <div class="w-15 h-2 bg-purple-500 rounded-full"></div>
                                </div>
                                <span class="text-xs font-medium text-purple-600">92%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产业核心企业和产业地图 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 产业核心企业 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">产业核心企业</h3>
                    <button class="text-sm text-primary hover:text-blue-700">查看全部</button>
                </div>

                <!-- 企业排行榜 -->
                <div class="space-y-4">
                    <div class="flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                        <div class="flex items-center justify-center w-8 h-8 bg-yellow-500 text-white rounded-full text-sm font-bold mr-3">1</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900">比亚迪股份有限公司</h4>
                            <p class="text-sm text-gray-600">新能源汽车整车制造</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥3,286亿</p>
                            <p class="text-xs text-green-600">+35.2%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-400 text-white rounded-full text-sm font-bold mr-3">2</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900">宁德时代新能源科技</h4>
                            <p class="text-sm text-gray-600">动力电池系统</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥3,285亿</p>
                            <p class="text-xs text-green-600">+43.7%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
                        <div class="flex items-center justify-center w-8 h-8 bg-orange-500 text-white rounded-full text-sm font-bold mr-3">3</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900">特斯拉（上海）有限公司</h4>
                            <p class="text-sm text-gray-600">智能电动汽车</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥1,810亿</p>
                            <p class="text-xs text-green-600">+28.9%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-300 text-white rounded-full text-sm font-bold mr-3">4</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900">蔚来汽车科技有限公司</h4>
                            <p class="text-sm text-gray-600">智能电动汽车</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥492亿</p>
                            <p class="text-xs text-green-600">+36.1%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-300 text-white rounded-full text-sm font-bold mr-3">5</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900">小鹏汽车科技有限公司</h4>
                            <p class="text-sm text-gray-600">智能电动汽车</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥307亿</p>
                            <p class="text-xs text-green-600">+23.4%</p>
                        </div>
                    </div>
                </div>

                <!-- 企业分析 -->
                <div class="mt-6 grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-600 font-medium">独角兽企业</p>
                        <p class="text-xl font-bold text-blue-800">23家</p>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <p class="text-sm text-green-600 font-medium">上市企业</p>
                        <p class="text-xl font-bold text-green-800">156家</p>
                    </div>
                </div>
            </div>

            <!-- 合肥市产业地图 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">合肥市产业地图</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">企业分布</button>
                        <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">产值分布</button>
                        <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">园区分布</button>
                    </div>
                </div>

                <!-- 合肥市地图区域 -->
                <div class="h-80 bg-gradient-to-br from-blue-50 to-green-50 rounded-lg p-4 relative border border-gray-200">
                    <!-- 合肥市区域分布 -->
                    <div class="absolute inset-4">
                        <!-- 地图标题 -->
                        <div class="absolute top-2 left-2 text-xs font-medium text-gray-600">
                            合肥市新能源汽车产业分布图
                        </div>

                        <!-- 瑶海区 -->
                        <div class="absolute top-8 right-8">
                            <div class="relative">
                                <div class="w-12 h-12 bg-blue-500 rounded-lg opacity-80 flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                    瑶海区
                                </div>
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">
                                    156
                                </div>
                                <div class="absolute top-14 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                                    物流配送
                                </div>
                            </div>
                        </div>

                        <!-- 庐阳区 -->
                        <div class="absolute top-12 left-8">
                            <div class="relative">
                                <div class="w-12 h-12 bg-green-500 rounded-lg opacity-80 flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                    庐阳区
                                </div>
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">
                                    89
                                </div>
                                <div class="absolute top-14 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                                    研发设计
                                </div>
                            </div>
                        </div>

                        <!-- 蜀山区 -->
                        <div class="absolute top-16 left-1/2 transform -translate-x-1/2">
                            <div class="relative">
                                <div class="w-14 h-14 bg-purple-500 rounded-lg opacity-80 flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                    蜀山区
                                </div>
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">
                                    234
                                </div>
                                <div class="absolute top-16 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                                    智能制造
                                </div>
                            </div>
                        </div>

                        <!-- 包河区 -->
                        <div class="absolute bottom-16 right-12">
                            <div class="relative">
                                <div class="w-12 h-12 bg-orange-500 rounded-lg opacity-80 flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                    包河区
                                </div>
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">
                                    178
                                </div>
                                <div class="absolute top-14 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                                    金融服务
                                </div>
                            </div>
                        </div>

                        <!-- 经开区 -->
                        <div class="absolute bottom-8 left-12">
                            <div class="relative">
                                <div class="w-16 h-16 bg-red-500 rounded-lg opacity-80 flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                    经开区
                                </div>
                                <div class="absolute -top-2 -right-2 w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                    1.2K
                                </div>
                                <div class="absolute top-18 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                                    整车制造
                                </div>
                            </div>
                        </div>

                        <!-- 高新区 -->
                        <div class="absolute bottom-12 right-1/4">
                            <div class="relative">
                                <div class="w-14 h-14 bg-indigo-500 rounded-lg opacity-80 flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                    高新区
                                </div>
                                <div class="absolute -top-2 -right-2 w-7 h-7 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                    892
                                </div>
                                <div class="absolute top-16 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                                    电池电控
                                </div>
                            </div>
                        </div>

                        <!-- 新站区 -->
                        <div class="absolute top-6 right-1/4">
                            <div class="relative">
                                <div class="w-12 h-12 bg-teal-500 rounded-lg opacity-80 flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                    新站区
                                </div>
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">
                                    145
                                </div>
                                <div class="absolute top-14 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                                    零部件
                                </div>
                            </div>
                        </div>

                        <!-- 连接线 -->
                        <svg class="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 300 200">
                            <path d="M 50 50 Q 150 100 250 50" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.5" stroke-dasharray="5,5"/>
                            <path d="M 50 150 Q 150 100 250 150" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.5" stroke-dasharray="5,5"/>
                        </svg>
                    </div>
                </div>

                <!-- 区域统计 -->
                <div class="mt-4 space-y-2">
                    <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg border border-red-200">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">经开区</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-red-600">1,234家</span>
                            <span class="text-xs text-gray-500 ml-2">整车制造集群</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-indigo-50 rounded-lg border border-indigo-200">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-indigo-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">高新区</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-indigo-600">892家</span>
                            <span class="text-xs text-gray-500 ml-2">电池电控集群</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-purple-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">蜀山区</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-purple-600">234家</span>
                            <span class="text-xs text-gray-500 ml-2">智能制造</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-orange-50 rounded-lg border border-orange-200">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">包河区</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-orange-600">178家</span>
                            <span class="text-xs text-gray-500 ml-2">金融服务</span>
                        </div>
                    </div>
                </div>

                <!-- 产业园区信息 -->
                <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <h5 class="text-sm font-semibold text-blue-900 mb-2">重点产业园区</h5>
                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <div class="flex justify-between">
                            <span class="text-blue-700">江淮汽车工业园</span>
                            <span class="text-blue-600 font-medium">356家</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-700">新能源汽车产业园</span>
                            <span class="text-blue-600 font-medium">289家</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-700">智能网联汽车示范区</span>
                            <span class="text-blue-600 font-medium">167家</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-700">动力电池产业基地</span>
                            <span class="text-blue-600 font-medium">234家</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产业发展总结分析 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">产业发展总结分析</h3>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 发展现状 -->
                <div class="p-4 bg-blue-50 rounded-lg">
                    <h4 class="text-base font-semibold text-blue-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        发展现状
                    </h4>
                    <ul class="text-sm text-blue-800 space-y-2">
                        <li>• 产业规模持续扩大，总产值达2.8万亿元</li>
                        <li>• 企业数量快速增长，已超过1.2万家</li>
                        <li>• 从业人员规模庞大，提供156.8万个就业岗位</li>
                        <li>• 产业链条日趋完善，覆盖上下游各环节</li>
                        <li>• 区域集聚效应明显，形成多个产业集群</li>
                    </ul>
                </div>

                <!-- 发展优势 -->
                <div class="p-4 bg-green-50 rounded-lg">
                    <h4 class="text-base font-semibold text-green-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        发展优势
                    </h4>
                    <ul class="text-sm text-green-800 space-y-2">
                        <li>• 技术创新能力强，创新指数达85.6分</li>
                        <li>• 龙头企业实力雄厚，引领产业发展</li>
                        <li>• 政策支持力度大，营商环境优良</li>
                        <li>• 产业生态完整，协同效应显著</li>
                        <li>• 市场需求旺盛，发展前景广阔</li>
                    </ul>
                </div>

                <!-- 发展建议 -->
                <div class="p-4 bg-purple-50 rounded-lg">
                    <h4 class="text-base font-semibold text-purple-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        发展建议
                    </h4>
                    <ul class="text-sm text-purple-800 space-y-2">
                        <li>• 加强关键技术攻关，提升核心竞争力</li>
                        <li>• 完善产业链配套，增强供应链韧性</li>
                        <li>• 培育更多独角兽企业，壮大产业规模</li>
                        <li>• 优化区域布局，促进协调发展</li>
                        <li>• 深化国际合作，拓展海外市场</li>
                    </ul>
                </div>
            </div>

            <!-- 关键指标总览 -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-base font-semibold text-gray-900 mb-4">关键指标总览</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-600">2.8万亿</p>
                        <p class="text-sm text-gray-600">产业总产值</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-600">12,456家</p>
                        <p class="text-sm text-gray-600">企业总数</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600">156.8万</p>
                        <p class="text-sm text-gray-600">从业人员</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-orange-600">85.6分</p>
                        <p class="text-sm text-gray-600">创新指数</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
