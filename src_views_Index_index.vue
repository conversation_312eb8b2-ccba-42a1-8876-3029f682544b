<template>
  <div
    :class="{ 'foreign-languages': !langIfZh }"
    :style="{ background: 'linear-gradient(to right, #FCF8F9, #F5F5FD)' }"
  >
    <div class="wmc-banner">
      <div class="banner-cont">
        <div class="banner-cont-container">
          <div class="banner-cont-title">
            制造世界 · 创造美好
          </div>
          <div class="second-title">
            2025世界制造业大会
          </div>
          <div class="banner-detail">
            <span class="banner-detail-time">9/20-9/23</span>
            <span class="banner-detail-address">丨合肥 滨湖国际会场中心</span>
          </div>
          <div class="banner-search-cont">
            <div class="banner-search-input">
              <div class="serch-icon">
                <img
                  class="serch-img"
                  src="../../assets/images/newWmc/input-icon.png"
                  alt=""
                />
              </div>
              <div id="input">
                <input
                  type="text"
                  placeholder="世界制造业大会丨"
                  v-model="serachs"
                  @change="search(serachs, 1)"
                />
              </div>
              <div class="banner-search-btn" id="search" @click="search(serachs, 1)">
                <span>观看视频</span>
                <img
                  class="serch-arrow"
                  src="../../assets/images/newWmc/search-arrow.svg"
                  alt=""
                />
              </div>
            </div>
          </div>
        </div>
        <div class="count-down">
          倒计时    
          <div class="count-down-num">3</div>
          日
        </div>
      </div>
      <el-carousel
        ref="bannerRef"
        height="800px"
        :autoplay="false"
        indicator-position="none"
      >
        <el-carousel-item v-for="(item, index) in bannerList" :key="index">
          <div v-if="!item.videoUrl">
            <img class="item-img" :src="item.imgUrl" />
          </div>
          <div v-else v-loading="videoLoading" style="width: 100%;">
          <video
            :id="videoId"
            ref="videoPlayer"
            preload="auto"
            class="video-js vjs-default-skin vjs-big-play-centered"
            autoplay
            muted
            loop
            :controls="false"
            playsinline
            data-setup="{}"
            :poster="item.imgUrl"
            style="height: 800px;width: 100%;"
          >
            <source
              id="source"
              :src="item.videoUrl"
              type="application/x-mpegURL"
            >
          </video>
          </div>
        </el-carousel-item>
        <div class="slick-dots">
          <div
            v-for="index in totalDots"
            :key="index"
            :class="{ active: index === activeIndex }"
          >
            <div class="banner-dot-item">
              <div
                class="dot-progress"
                :class="{ active: index === activeIndex }"
                @click="handleDotClick(index)"
              >
                <!-- <img
                  class="dot-img"
                  :class="{ active: index === activeIndex }"
                  src="../../../assets/images/outPortal/banner-dot.png"
                  alt=""
                /> -->
              </div>
            </div>
          </div>
        </div>
      </el-carousel>
    </div>
    <!-- 数据总览 -->
    <div class="wmc-data">
      <div class="wmc-data-cont">
        <div class="wmc-data-item" v-for="(item,index) in dataList" :key="index">
          <div class="wmc-data-item-num">
            <span class="wmc-data-item-num-text">
              {{item.num}}
            </span>
            +
          </div>
          <div class="wmc-data-item-name">
            {{item.name}}
          </div>
        </div>
      </div>
    </div>
    <!-- 资讯中心 -->
    <div class="wmc-news-center wmc-animate clearfix" v-if="langIfZh">
      <div class="wmc-news-cont">
        <div class="wmc-news-title">
          <div class="wmc-news-title-left">最新资讯</div>
          <div class="wmc-news-title-right">
            <div class="tabs-item" :class="newsTypeIndex === index ? 'tabs-item-active':''" v-for="(item,index) in tabsLsit" :key="index" @click="showNewsType(item,index)">
              {{item.name}}
            </div>
          </div>
        </div>
        <div class="wmc-news-list">
          <div class="wmc-news-item" v-for="(item,index) in newsList" :key="index">
            <div class="wmc-news-item-title">{{item.title}}</div>
            <div class="wmc-news-item-time">{{item.time}}</div>
            <div class="wmc-news-item-text">{{item.text}}</div>
            <div class="wmc-news-item-btn">
              查看详情
            </div>
          </div>
        </div>
        <div class="wmc-news-more">
          查看更多
        </div>
      </div>
    </div>
    <!-- 大会活动  活动日程 -->
    <div class="wmc-itinerary wmc-animate">
      <div class="wmc-itinerary-left" :class="`wmc-itinerary-left-bg${itineraryActiveIndex}`">
        <div class="wmc-itinerary-left-cont">
          <div class="wmc-itinerary-title">
            大会活动
          </div>
          <div class="wmc-itinerary-type">
            <div class="wmc-itinerary-type-item" @click="changeItineraryType(index)" :class="itineraryActiveIndex === index ? 'wmc-itinerary-type-active':''" v-for="(item,index) in itineraryList" :key="index">
                <img
                  class="itinerary-icon"
                  :src="item.icon"
                  alt=""
                />
                <div class="wmc-itinerary-type-name">
                  {{item.name}}
                </div>
                <img
                  class="itinerary-arrow"
                  src="../../assets/images/newWmc/more-icon-white.png"
                  alt=""
                />
            </div>
          </div>
        </div>
      </div>
      <div class="wmc-itinerary-right">
        <div class="itinerary-activity-cont">
          <div class="itinerary-activity-list">
            <div class="itinerary-activity-item" v-for="(item,index) in activityList" :key="index">
              <img
                  class="itinerary-activity-logo"
                  src="../../assets/images/newWmc/activity-logo.png"
                  alt=""
                />
                <div class="itinerary-activity-text">{{item.name}}</div>
            </div>
          </div>
          <div class="more-activity-btn">
            查看更多 >
          </div>
        </div>
      </div>
    </div>
    <!-- 展商信息 -->
    <div class="wmc-exhibitor-info">
      <WmcTitle title="展商信息"></WmcTitle>
      <div class="wmc-exhibitor-arrow-btn">
          <img class="wmc-exhibitor-arrow-left" @click="scrollLeft" v-if="!scrollAtStart" src="../../assets/images/newWmc/arrow-left-icon.svg">
          <img class="wmc-exhibitor-arrow-left-disable" @click="scrollLeft" v-else src="../../assets/images/newWmc/arrow-left-disable-icon.svg">
          <img class="wmc-exhibitor-arrow-right"  @click="scrollRight" v-if="!scrollAtEnd" src="../../assets/images/newWmc/arrow-right-icon.svg">
          <img class="wmc-exhibitor-arrow-right-disable"  @click="scrollRight" v-else src="../../assets/images/newWmc/arrow-right-disable-icon.svg">
      </div>
      <div class="wmc-exhibitor-info-cont" ref="listBox">
        <div class="wmc-exhibitor-list" ref="list">
          <div class="wmc-exhibitor-item" v-for="(item,index) in exhibitorList" :key="index" :class="{'last-item': index === exhibitorList.length - 1}">
            <div class="wmc-exhibitor-vr">
              <img class="wmc-exhibitor-img" :src="item.img">
              <img class="wmc-exhibitor-vr-btn" src="../../assets/images/newWmc/vr-btn.svg">
            </div>
            <div class="wmc-exhibitor-bottom">
              <div class="wmc-exhibitor-name">
                <img class="wmc-exhibitor-logo" :src="item.logo">
                <span>{{item.name}}</span>
              </div>
              <div class="wmc-exhibitor-postion">
                <div class="wmc-exhibitor-postion-row">
                  <span class="wmc-exhibitor-label">展区</span>
                  <span class="wmc-exhibitor-value">{{item.type}}</span>
                </div>
                <div class="wmc-exhibitor-postion-row">
                  <span class="wmc-exhibitor-label">展位</span>
                  <span class="wmc-exhibitor-value">{{item.position}}</span>
                </div>
              </div>
              <div class="wmc-exhibitor-detail">
                {{item.detail}}
              </div>
              <div class="wmc-exhibitor-item-btn">
                查看详情
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="wmc-exhibitor-more-btn">
          查看更多
      </div>
    </div>
    <!-- 往届回顾 -->
    <div class="wmc-before wmc-animate">
      <WmcTitle :title="pastReview"></WmcTitle>
      <div class="wmc-before-cont">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane
            v-for="(item, index) in historyItems"
            :key="index"
            :label="`${item.year}年`"
            :name="item.year"
          >
            <div class="wmc-before-pane">
              <div class="wmc-before-detail">
                <div class="wmc-before-detail-cont">
                  <div class="wmc-before-year">
                    <span style="color: #e40069; padding-right: 4px">{{
                      item.year
                    }}</span>
                    世界制造业大会
                  </div>
                  <div class="wmc-before-theme">
                    {{ item.theme }}
                  </div>
                  <div class="wmc-before-theme-after-bar"></div>
                  <div class="wmc-before-detail-title">参展嘉宾</div>
                  <div class="wmc-before-detail-text">
                    {{ item.guests }}
                  </div>
                  <div class="wmc-before-detail-title">大会成效</div>
                  <div class="wmc-before-detail-text">
                    {{ item.effect }}
                  </div>
                </div>
              </div>
              <div class="wmc-before-img">
                <img :src="item.img" alt="" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!-- 精彩瞬间 -->
    <div class="wmc-jcsj wmc-animate">
      <WmcTitle :title="brilliantMoment"></WmcTitle>
      <div class="wmc-jcsj-cont">
        <el-carousel indicator-position="none" arrow="never" :autoplay="false" height="640px" ref="carouselGuest" @change="handleCarouselChange">
          <el-carousel-item v-for="(group, index) in photoGroups" :key="index">
            <div class="carousel-layout">
              <!-- 左侧一张 -->
              <div class="left-box">
                <img :src="group[0]?.imgUrl" class="photo-img left-img" />
              </div>

              <!-- 右侧三张 -->
              <div class="right-box">
                <div class="top-img">
                  <img :src="group[1]?.imgUrl" class="photo-img" />
                </div>
                <div class="bottom-imgs">
                  <img v-show="group[2]" :src="group[2]?.imgUrl" class="bottom-img" />
                  <img v-show="group[3]" :src="group[3]?.imgUrl" class="bottom-img" />
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
        <div class="carousel-arrow prev-arrow" v-if="currentCarouselIndex !== 0" @click="prev">
				</div>
        <div class="carousel-arrow prev-arrow-disable" v-else>
				</div>
				<div class="carousel-arrow next-arrow" v-if="currentCarouselIndex !== carouselItemCount-1" @click="next">
				</div>
        <div class="carousel-arrow next-arrow-disable" v-else>
				</div>
      </div>
      <div class="wmc-jcsj-more-btn">
          查看更多
      </div>
    </div>
    <!-- 线上展会 -->
    <div class="wmc-online-show wmc-animate">
      <div class="wmc-online-show-cont">
        <div class="wmc-online-show-title">线上展厅</div>
        <div class="wmc-online-show-name">
          <span>2025</span>
          世界制造业大会实景展
        </div>
        <div class="wmc-online-show-btn">
          点击进入
        </div>
      </div>
    </div>
    <div v-if="selectedImage" class="overlayImg">
      <div class="modal">
        <img :src="selectedImage.url" alt="" class="imgCont" />
        <img
          src="../../assets/images/banner/close_icon.png"
          alt=""
          class="closeImg"
          @click="hideImage"
        />
      </div>
    </div>
    <detail-modal ref="detailModalRef" />
  </div>
</template>
<script>
import videojs from "video.js";
import autofit from 'autofit.js'
// import 'videojs-contrib-hls'
// import "@videojs/http-streaming"
// import "videojs-contrib-hls";
// import {
// 	Message
// } from "element-ui";
// import "element-ui/lib/theme-chalk/message.css";
// import WmcFourm from "../../components/WmcForum.vue";
// import WmcGuest from "../../components/WmcGuest.vue";
import global from "../../global/global.js";
import Axios from "axios";
import DetailModal from "../wmcActivities/DetailModal.vue";
// import TooltipOver from "@/components/TooltipOver.vue";
export default {
  name: "WmcIndex",
  components: {
    DetailModal,
  },
  data() {
    return {
      currentCarouselIndex: 0,
      photoList:[
        {
          imgUrl:require("../../assets/images/newWmc/photo1.png")
        },
        {
          imgUrl:require("../../assets/images/newWmc/photo2.png")
        },
        {
          imgUrl:require("../../assets/images/newWmc/photo3.png")
        },
        {
          imgUrl:require("../../assets/images/newWmc/photo4.png")
        },
        {
          imgUrl:require("../../assets/images/newWmc/photo2.png")
        },
        {
          imgUrl:require("../../assets/images/newWmc/photo3.png")
        },
        {
          imgUrl:require("../../assets/images/newWmc/photo4.png")
        }
      ],
      scrollAtStart: true,
      scrollAtEnd: false,
      exhibitorList:[
        {
          name:"安徽络宝致联有限公司",
          type:"人工智能展区",
          position:"A103",
          detail:"安徽络宝致联信息技术有限公司成立于2019年8月，是合肥重点产业链企业，聚焦新能源汽车全生命周期管理，基于物联网、大数据、云计算等新一代信息技术...",
          logo:require("../../assets/images/newWmc/activity-logo.png"),
          img:require("../../assets/images/newWmc/exhibitor-img1.png")
        },
        {
          name:"产业互联创新中心有限公司",
          type:"人工智能展区",
          position:"A104",
          detail:"安徽产业互联数据智能创新中心有限公司成立于2020-07-15，法定代表人为吴仲城，注册资本为2000万元，统一社会信用代码为91340100MA2W0W9N2F…",
          logo:require("../../assets/images/newWmc/activity-logo.png"),
          img:require("../../assets/images/newWmc/exhibitor-img2.png")
        },
        {
          name:"安徽络宝致联有限公司",
          type:"人工智能展区",
          position:"A103",
          detail:"安徽络宝致联信息技术有限公司成立于2019年8月，是合肥重点产业链企业，聚焦新能源汽车全生命周期管理，基于物联网、大数据、云计算等新一代信息技术...",
          logo:require("../../assets/images/newWmc/activity-logo.png"),
          img:require("../../assets/images/newWmc/exhibitor-img1.png")
        },
        {
          name:"产业互联创新中心有限公司",
          type:"人工智能展区",
          position:"A104",
          detail:"安徽产业互联数据智能创新中心有限公司成立于2020-07-15，法定代表人为吴仲城，注册资本为2000万元，统一社会信用代码为91340100MA2W0W9N2F…",
          logo:require("../../assets/images/newWmc/activity-logo.png"),
          img:require("../../assets/images/newWmc/exhibitor-img2.png")
        },
        {
          name:"安徽络宝致联有限公司",
          type:"人工智能展区",
          position:"A103",
          detail:"安徽络宝致联信息技术有限公司成立于2019年8月，是合肥重点产业链企业，聚焦新能源汽车全生命周期管理，基于物联网、大数据、云计算等新一代信息技术...",
          logo:require("../../assets/images/newWmc/activity-logo.png"),
          img:require("../../assets/images/newWmc/exhibitor-img1.png")
        },
        {
          name:"安徽络宝致联有限公司",
          type:"人工智能展区",
          position:"A103",
          detail:"安徽络宝致联信息技术有限公司成立于2019年8月，是合肥重点产业链企业，聚焦新能源汽车全生命周期管理，基于物联网、大数据、云计算等新一代信息技术...",
          logo:require("../../assets/images/newWmc/activity-logo.png"),
          img:require("../../assets/images/newWmc/exhibitor-img1.png")
        },
        {
          name:"安徽络宝致联有限公司",
          type:"人工智能展区",
          position:"A103",
          detail:"安徽络宝致联信息技术有限公司成立于2019年8月，是合肥重点产业链企业，聚焦新能源汽车全生命周期管理，基于物联网、大数据、云计算等新一代信息技术...",
          logo:require("../../assets/images/newWmc/activity-logo.png"),
          img:require("../../assets/images/newWmc/exhibitor-img1.png")
        }
      ],
      activityList:[
        {
          name:"安徽省新兴产业与跨国公司对接会"
        },
        {
          name:"皖港澳制造业合作交流对接会"
        },
        {
          name:"皖台优势产业合作推进会"
        },
        {
          name:'侨创项目对接暨巢湖侨创会'
        },
        {
          name:'中国制造业民营企业合作交流对接..'
        }
      ],
      itineraryActiveIndex:0,
      itineraryList:[
        {
          icon:require("../../assets/images/newWmc/major-event-icon.svg"),
          name:"重大活动"
        },
        {
          icon:require("../../assets/images/newWmc/project-docking-icon.svg"),
          name:"重大项目对接活动"
        },
        {
          icon:require("../../assets/images/newWmc/special-activities-icon.svg"),
          name:"重大专项活动"
        },
        {
          icon:require("../../assets/images/newWmc/conference-release-icon.svg"),
          name:"大会发布"
        }
      ],
      newsList:[
        {
          title:"安徽省“双招双引”打法提质升级",
          time:"2025-05-08",
          text:"《安徽省人民政府关于推动新兴产业“双招双引”和产业培育提质增效（2.0版）的意见》近日印发。《意见》提出七个方面33条政策措施，特别是对创新精准招引新模式作出..."
        },
        {
          title:"一图读懂2025世界制造业大会同期市场化展",
          time:"2025-04-28",
          text:"一图读懂2025世界制造业大会同期市场化展"
        },
        {
          title:"安徽省工信厅对接筹备世界制造业大会工作",
          time:"2025-02-14",
          text:"近日，安徽省工信厅党组成员、副厅长程英春一行赴安徽省外办对接2025世界制造业大会相关工作。省外办党组成员、副主任杨小琳，安徽省工信厅总工程师沈忠林，厅技…"
        },
        {
          title:"五个历史新高！2024世界制造业大会成果丰硕",
          time:"2024-09-26",
          text:"9月26日，记者从省政府办公厅召开的2024世界制造业大会成果发布会获悉，2024世界制造业大会参会外宾人数508名，参展产品数量1万余件，签约项目数量718个，发布成…"
        }
      ],
      newsTypeIndex:0,
      tabsLsit:[
        {
          name:"大会资讯"
        },
        {
          name:"通知公告"
        },
        {
          name:"媒体报道"
        }
      ],
      dataList:[
        {
          num:"60000",
          name:"展会面积（㎡）"
        },
        {
          num:"1000",
          name:"参展商家（位）"
        },
        {
          num:"30",
          name:"同期论坛（场）"
        },
        {
          num:"100000",
          name:"展会观众（名）"
        }
      ],
      intervalId: null,
      totalDots: 3,
      activeIndex: 1,
      activeName: "2024",
      isVisible: true, // 控制图片的显示状态
      videoLoading: true,
      videoId: "myVideo",
      moreText: "",
      activities: [
        {
          category: "",
          subItems: [],
        },
        {
          category: "",
          subItems: [],
        },
        {
          category: "",
          subItems: [],
        },
      ],
      scheduleItems: [
        {
          time: "9:00-11:20",
          title: "开幕式暨主旨论坛",
          address: "活动地点：合肥滨湖国际会展中心",
          unit: "责任单位：安徽省经济和信息化厅",
        },
        {
          time: "14:00-17:00",
          title: "“百家港澳企”项目签约",
          address: "活动地点：安徽高速徽风皖韵酒店",
          unit: "责任单位：安徽省外办（港澳办）",
        },
        {
          time: "14:00-17:00",
          title: "“百家侨企”项目对接活动",
          address: "活动地点：安徽高速徽风皖韵酒店",
          unit: "责任单位：安徽省侨联",
        },
        {
          time: "14:00-17:00",
          title: "“百家侨企”项目对接活动",
          address: "活动地点：安徽高速徽风皖韵酒店",
          unit: "责任单位：安徽省侨联",
        },
      ],
      historyItems: [
        {
          img: require("../../assets/images/newWmc/2024.png"),
          year: "2024",
          title: "世界制造业大会",
          theme: "智造世界·创造美好",
          guests:
            "邀请法国担任大会主宾国，邀请159名国际组织及商协会代表、96名驻华使领馆官员、国际友好省州（城市）和港澳代表，222名境外世界500强及跨国公司负责人参会，参会外宾人数达到508名、同比增长63%，涉及41个国家和地区，是历届世界制造业大会中参会国家和地区数、外宾参会人数最多的一届。全国人大常委会副委员长张庆伟出席大会开幕式、致辞并宣布大会开幕；工业和信息化部副部长辛国斌等19位国家部委及直属单位领导和大会主办、支持单位负责人参加大会；吉林、上海、内蒙古、福建、四川、重庆等29个外省（区、市）参会，比去年增加3个，其中吉林省、上海市作为主宾省（市）应邀参会；中国工程院主席团名誉主席、国家制造强国建设战略咨询委员会主任周济，中国工程院副院长钟志华，中国工程院院士干勇、李培根、卢秉恒等21位院士参加大会；435名省内外企业主要负责人参会，占全部参会企业数的66%。",
          effect:
            "大会认真贯彻落实习近平总书记关于新质生产力的重要论述和重要贺信精神，紧紧围绕“智造世界·创造美好”主题，以新中国成立75周年制造业发展成就为主线，实现参会外宾人数508名，同比增长63%、参展产品数量1万余件，同比增长233.3%、签约项目数量718个，同比增长22.3%、发布成果数量117个，同比增长67.1%、媒体报道数量约5.3万篇（条），同比增长10%。为加快形成新质生产力，助力制造强国建设，促进全球制造业交流合作、实现共享共赢作出了积极贡献。",
        },
        {
          img: require("../../assets/images/wjhg/2023.png"),
          year: "2023",
          title: "世界制造业大会",
          theme: "智造世界·创造美好",
          guests:
            "大会共邀请国内外1.6万名嘉宾和代表参加大会，其中，政商学界代表和知名人士等重要嘉宾2158名，共有来自7个国家部委的领导及代表32人参会，组团参会的外省（区、市）26个（10个由省级领导带队），院士20多名，重要嘉宾数和外省参加数均创历史新高。国际元素充分彰显。大会邀请由南非、希腊等9国驻华大使和马尔代夫、菲律宾等8国驻华使领馆高级外交官组成的外国驻华使节团参访安徽，并邀请美国、法国、以色列等国驻沪总领馆官员，希腊阿提卡大区省长等友好省州代表，有关国际组织负责人、世界500强及跨国公司高管，以及香港特区政府驻沪办、香港贸发局官员和港澳资企业高管等各类外宾共312人，来自31个国家和地区，是自我省举办世界制造业大会以来，外国驻华使节及港澳界代表参会人数最多的一次。",
          effect:
            "本届大会创造出历届大会参会人数最多、展览面积最大、活动内容最丰富、外向度最高、宣传覆盖最广、服务保障满意度最高的亮眼成绩，为宣传展示我国先进制造业发展成果、促进制造业国际合作交流作出积极贡献。大会设置展览展示8万平方米，共组织974户企业参展，展出展品3000余件。各展区共收获采购商机6941条、达成合作意向1646项，线下累计接待观众35.9万人次，线上访问量1382.8万人次，线上线下参观访问量均创历史新高。以会引资，项目对接成果丰硕，促成合作签约项目587个，投资总额3425亿元，其中，制造业项目534个，投资总额3060亿元，占比分别为91%、89%。",
        },
        {
          img: require("../../assets/images/wjhg/2022.png"),
          year: "2022",
          title: "世界制造业大会",
          theme: "制造世界·创造美好",
          guests:
            "本次大会共邀请到4621名嘉宾参会，包括国家部委领导、外国驻沪驻穗领事、外省（区、市）领导，院士及专家学者，国际组织及境内外商协会、境外500强及跨国公司、央企、知名企业负责人等。在出席大会开幕式暨主旨论坛的嘉宾中，福耀玻璃创始人、董事长曹德旺，比亚迪董事长兼总裁王传福等行业领军人物，香港科技大学教授李泽湘，大陆集团中国区总裁兼首席执行官汤恩、大众汽车乘用车品牌中国CEO孟侠（Stefan Mecha）作了主旨演讲。他们在演讲中，分享了发展制造业的经验，分析了制造业发展面临的新形势、新机遇，许多观点令人深受启迪。",
          effect:
            "大会共促成合作项目567个，总投资额3794亿元，吸引省外资金3389亿元。其中，促成央企合作项目65个，总投资1203亿元；民企合作项目348个，总投资1687亿元；外企合作项目31个，总投资324亿元；侨企合作项目32个，总投资94亿元；台企合作项目54个，总投资185亿元；港澳企合作项目37个，总投资301亿元。",
        },
        {
          img: require("../../assets/images/wmc-wjhg-2021.png"),
          year: "2021",
          title: "世界制造业大会",
          theme: "创新驱动 数字赋能 携手全球制造业高质量发展",
          guests:
            "国家部委、兄弟省（区、市）、驻华使节、国际组织、境外世界500强、央企、民企、商协会及院士和知名专家等嘉宾参会，其中外方代表主要来自德、日、韩、法、美、俄等24个国家和地区，通过线上线下方式参加。韩国为大会主宾国，沪苏浙为大会主宾省。围绕新兴产业和行业龙头企业精准务实邀商，共邀请了十大新兴产业领域企业近350家、世界500强企业60多家、上市公司70多家参会。",
          effect:
            "按照政府引导、市场化运作的方式，会同省十大新兴产业工作专班，组织省内外428家行业优势企业参展，集中展示制造业新产品新技术新业态和十大新兴产业“双招双引”成效, 其中省外企业参展比例超过20%按照政府引导、市场化运作的方式，会同省十大新兴产业工作专班，组织省内外428家行业优势企业参展，集中展示制造业新产品新技术新业态和十大新兴产业“双招双引”成效其中省外企业参展比例超过20%以上。华为公司首发“华为工业云智能体”的工业互联网平台方案。主动承接进博会溢出效应，40多家进博会参展企业齐聚进口商品展区，推出一系列高端前沿产品。上海虹桥品汇展出来自26个国家和地区的198个日用消费品类，深受广大市民喜爱。展馆专门设立洽谈区，为境内外、省内外企业提供洽谈服务、搭建交流平台。",
        },
        {
          img: require("../../assets/images/wjhg/2020-1.png"),
          year: "2020",
          title: "世界制造业大会江淮线上经济论坛",
          theme: "线上经济赋能高质量发展",
          guests:
            "大会通过线上线下相结合的方式，举办云开幕暨主旨论坛、云签约、云论坛、云展示等“四朵云”系列活动。邀请全球制造业和数字经济等领域最具影响力的企业家、科学家，云上或现场出席会议。本次大会论坛首次采用线上线下结合形式举办，通过手机、电脑登录云平台，即可实现云端参会、线上观展。大会创新展示方式，将传统的线下展览转变为24小时线上呈现，依托数字网络和融媒体等现代信息技术手段，通过图文、视频、3D立体模型导航等形式，突出展示安徽科技创新能力，重点展示“芯屏器合”等安徽制造业高质量发展成就，集中展示长三角一体化发展成果，全面展示全省220多个骨干企业及其特色产品，并将持续运行1个月，引导线下互动交流及对接合作。",
          effect:
            "大会论坛共签约项目678个、投资总额6178亿元，其中代表制造业未来发展方向的战略性新兴产业项目288个、投资额2848亿元，分别占42.5%、46.1%，较去年大会增长38.5%、59.6%，为安徽制造业转型升级注入了新动能。大会论坛期间，还先后发布了《长三角制造业协同发展报告》《长三角地区电子证照互认应用合作共识》《数据赋能政府治理评价及安徽省发展研究报告》《安徽省5G+工业互联网十大创新应用》《工业互联网和智能制造研究白皮书》《徽商发展报告2020》以及“2020创新力徽商”和“皖事通办·一源五端”等相关成果。",
        },
        {
          img: require("../../assets/images/wmc-wjhg-2019.jpg"),
          year: "2019",
          title: "世界制造业大会",
          detail: "2019世界制造业大会",
          theme: "创新创业创造，迈向制造业新时代",
          guests:
            "78个国家和地区4500多位嘉宾出席，其中境外来宾1700多位，111家境内外世界500强负责人、30多位知名专家学者参会，伊拉克总理率60人组成的政府代表团参加会议。展览展示注重前沿性、智能性、体验性，展区面积达6.1万平方米，分为序厅、国际制造、智能制造、高端制造、绿色制造、服务型制造、数字经济、无人驾驶汽车体验等十大展区。",
          effect:
            "大会集中签约项目638个，投资总额达7351亿元，项目数、投资总额分别较去年大会增长46%和64%。其中，制造业项目数、投资额分别占总量的87%、86%，先进制造业项目、投资额分别占总量的49%、68%。签约项目涵盖集成电路、新型显示、智能终端、机器人、通用航空、轨道交通、新材料、生物医药、新能源、新能源汽车、节能环保等行业。, 其中省外企业参展比例超过20%以上。华为公司首发“华为工业云智能体”的工业互联网平台方案。主动承接进博会溢出效应，40多家进博会参展企业齐聚进口商品展区，推出一系列高端前沿产品。上海虹桥品汇展出来自26个国家和地区的198个日用消费品类，深受广大市民喜爱。展馆专门设立洽谈区，为境内外、省内外企业提供洽谈服务、搭建交流平台。",
        },
        {
          img: require("../../assets/images/wmc-wjhg-2018.jpg"),
          year: "2018",
          title: "世界制造业大会",
          theme: "创新驱动、制造引领、拥抱世界新工业革命",
          guests:
            "参会嘉宾超过4000位，其中100多位境内外世界500强高管、近500家国内外制造业领军知名企业负责人、30多位“两院”院士专家学者参会。展览展示面积为43000平方米。主展馆设有综合展、国内智能制造业展、国际智能制造业展、金融服务业；9号馆设有安徽人力资源展区；10号馆设有安徽进出口商品精品展。",
          effect:
            "大会共签约合作项目436个、投资总额4471亿元，涵盖电子信息和家电、新材料和新能源、装备制造、节能环保、现代服务、汽车和汽车零部件、生物医药等9个行业, 其中省外企业参展比例超过20%以上。华为公司首发“华为工业云智能体”的工业互联网平台方案。主动承接进博会溢出效应，40多家进博会参展企业齐聚进口商品展区，推出一系列高端前沿产品。上海虹桥品汇展出来自26个国家和地区的198个日用消费品类，深受广大市民喜爱。展馆专门设立洽谈区，为境内外、省内外企业提供洽谈服务、搭建交流平台。",
        },
      ],
      itemsHistory: 3,
      images: [],
      player: null,
      bannerSwiper: null,
      num: "",
      floatList: [],
      underList: [],
      conferenceItem: {
        conferenceList: [
          {
            path: {
              path: "",
              query: {
                id: "",
                type: "1",
              },
            },
            text: "",
            time: "",
          },
          {
            path: {
              path: "",
              query: {
                id: "",
                type: "1",
              },
            },
            text: "",
            time: "",
          },
          {
            path: {
              path: "",
              query: {
                id: "",
                type: "1",
              },
            },
            text: "",
            time: "",
          },
        ],
        morePath: {
          path: "moreNews",
          query: {
            title: "dhdt",
          },
        },
      },
      notifyItem: {
        notifyList: [
          {
            path: {
              path: "",
              query: {
                id: "",
              },
            },
            text: "",
            time: "",
          },
          {
            path: {
              path: "",
              query: {
                id: "",
              },
            },
            text: "",
            time: "",
          },
          {
            path: {
              path: "",
              query: {
                id: "",
              },
            },
            text: "",
            time: "",
          },
        ],
        morePath: {
          path: "moreNews",
          query: {
            title: "tzgg",
          },
        },
      },
      reportItem: {
        reportList: [
          {
            path: {
              path: "",
              query: {
                id: "",
                type: "6",
              },
            },
            text: "“精品安徽”奏响世界制造业大会宣传进行曲",
            time: "2022-04-10",
          },
          {
            path: {
              path: "",
              query: {
                id: "",
                type: "6",
              },
            },
            text: "“精品安徽”奏响世界制造业大会宣传进行曲",
            time: "2022-04-10",
          },
          {
            path: {
              path: "",
              query: {
                id: "",
                type: "6",
              },
            },
            text: "“精品安徽”奏响世界制造业大会宣传进行曲",
            time: "2022-04-10",
          },
        ],
      },
      showTip: false,
      newsVideoMask: true,
      //  newsVideoUrl: require("../../assets/video/publish-video.mp4"),
      newsVideoUrl: "https://vjs.zencdn.net/v/oceans.mp4",
      // newsVideoUrl:
      //   "http://***********:18086/files/file/wmc/202206/02/20220602144447112138722.mp4",
      forumList: [],
      isReloadData: true,
      importGuestList: [],
      beforeGuestList: [
        [
          // {
          //   duty: "董事会主席",
          //   introduce:
          //     "迪斯博士于 1958 年 10 月 24 日出生于德国慕尼黑。2018 年 4 月 13 日，大众汽车集团监事会正式任命迪斯博士为大众汽车集团董事会主席。自 2019 年 1 月 11 日起，大众汽车集团监事会委任集团全球董事会主席迪斯博士担任中国董事会负责人。迪斯博士现有的集团及品牌职责保持不变。",
          //   imgSrc: require("../../assets/images/guest/ds.png"),
          //   name: "迪斯",
          //   company: "大众集团",
          // },
          {
            duty: "全球董事会主席兼首席执行官",
            introduce:
              "马克于2017年10月被任命为惠而浦集团全球首席执行官，并于2019年1月1日当选全球董事会主席。在加入惠而浦集团之前，马克曾任职于波士顿咨询公司，在德国慕尼黑和加拿大多伦多两地工作8年之久，并被任命为该公司副总裁。1999年，马克加入惠而浦欧洲，历任旗下品牌Bauknecht集团副总裁、惠而浦欧洲区市场营销及服务高级副总裁、惠而浦欧洲区总裁。马克自2009年起出任惠而浦北美区总裁并负责全美的业务运营，之后被任命为副董事长，全面领导北美、欧洲、中东和非洲地区（EMEA）的业务。马克拥有瑞士圣加仑大学商业、经济学和法学院工商管理学硕士及博士学位。",
            imgSrc: require("../../assets/images/guest/bzr.jpg"),
            name: "马克·比泽尔",
            company: "惠而浦集团",
          },
          {
            duty: "董事长兼CEO",
            introduce:
              "杨元庆，1964年11月12日出生于安徽合肥，祖籍浙江舟山。联想集团董事长兼CEO。国家高级工程师，享受政府专家特殊津贴，中华全国青年联合会副主任委员，中国企业家协会理事，中国科技大学客座教授。未来论坛创始理事。",
            imgSrc: require("../../assets/images/guest/yyq.jpg"),
            name: "杨元庆",
            company: "联想集团",
          },
          {
            duty: "董事长",
            introduce:
              "方洪波，男，1967年出生于安徽省枞阳县藕山镇万桥村杉木窊庄人，1983年至1987年就读于上海华东师范大学历史系，2002年获得新加坡国立大学管理学院MBA硕士学位。2009年获得南京大学企业管理博士学位，经济师。 1992年11月加入美的集团，曾任美的市场部部长、美的空调事业部总经理、美的制冷家电集团CEO、广东美的电器股份有限公司董事局主席、总裁等职务。现任美的集团股份有限公司董事长兼总裁，无锡小天鹅股份有限公司董事长。",
            imgSrc: require("../../assets/images/guest/fhb.jpg"),
            name: "方洪波",
            company: "美的集团",
          },
          {
            duty: "常务董事",
            introduce:
              "余承东，出生于1969年，安徽省六安市霍邱县人，毕业于清华大学，硕士。1993年加入华为，历任3G产品总监、无线产品行销副总裁、无线产品线总裁、欧洲片区总裁、战略与Marketing总裁、终端公司董事长及消费者BG CEO等。",
            imgSrc: require("../../assets/images/guest/ycd.jpg"),
            name: "余承东",
            company: "华为技术有限公司",
          },
          {
            duty: "董事长",
            introduce:
              "刘庆峰，男，1973年2月出生于安徽泾县，现居住在合肥，科大讯飞董事长，1990年考入中国科学技术大学，1998年获“通信与电子系统”专业硕士学位，2003年7月获“信号与信息处理”专业博士学位。中国科学技术大学兼职教授、博士生导师，中华全国青年联合会委员、安徽省青年企业家协会会长，中国科协七届委员和十届、十一届、十二届、十三届全国人大代表。1999年创办科大讯飞股份有限公司，并担任总裁，2009年4月起同时兼任董事长。安徽信息工程学院董事长。伏羲智库联合发起人。",
            imgSrc: require("../../assets/images/guest/lqf.jpg"),
            name: "刘庆峰",
            company: "科大讯飞股份有限公司",
          },
          {
            duty: "总裁",
            introduce: "陈睿，男，四川成都人，1978年生，哔哩哔哩董事长兼CEO。",
            imgSrc: require("../../assets/images/guest/cr.jpg"),
            name: "陈睿",
            company: "哔哩哔哩",
          },
          {
            duty: "CEO",
            introduce:
              "朱一明，男，1972年生，中国国籍，清华大学本科、硕士，美国纽约州立大学石溪分校硕士。合肥长鑫存储及睿力CEO，海归学子，中国存储器领域的开拓者和领导者。",
            imgSrc: require("../../assets/images/guest/zym.jpg"),
            name: "朱一明",
            company: "长鑫存储",
          },
          {
            duty: "董事长",
            introduce:
              "贺东风，男，汉族，1966年4月生，黑龙江人，工程硕士，研究员。1988年5月加入中国共产党，1989年8月参加工作。1989年毕业于吉林工业大学金属材料、经济管理专业，获双学士学位。",
            imgSrc: require("../../assets/images/guest/hdf.jpg"),
            name: "贺东风",
            company: "中国商飞",
          },
        ],
        // {
        //   duty: "",
        //   introduce: "",
        //   imgSrc: require("../../assets"),
        //   name: "",
        //   company: "",
        // },
      ],
      showWmcBefore: true,
      previousReviews: 0,
      languageVersion: "",
      key: 0,
      keyss: 0,
      itineraryIndex: 0,
      itineraryLeft: 0,
      currItineraryIndex: 0,
      timer: null,
      showInfo: true,
      selectedImage: null,
      bannerList: [
        {
          title:"banner1",
          title1: "制造世界 · 创造美好",
          title2:"2025世界制造业大会",
          time:"9/20-9/23",
          address:"丨合肥 滨湖国际会场中心",
          imgUrl: require("../../assets/images/newWmc/banner.png"),
          videoUrl:"https://www.wmconvention.com/files/wmc/2025/1937432601987342337/input.m3u8",
          videoUrl1:"https://www.wmconvention.com/files/wmc//m3u8/20240923111315917414808/input.m3u8",
        },
        {
          title:"banner1",
          title1: "制造世界 · 创造美好",
          title2:"2025世界制造业大会",
          time:"9/20-9/23",
          address:"丨合肥 滨湖国际会场中心",
          imgUrl: require("../../assets/images/newWmc/banner.png"),
        },
      ],
      videoList: null,
    };
  },
  computed: {
    dhhd() {
      return this.$t("language.conventionActivities");
    },

    lang() {
      return this.$store.state.lang;
    },
    langIfZh() {
      return this.$store.state.lang == "zh-CN";
    },
    historyList() {
      const hisList = [];
      for (let i = 0; i < this.historyItems.length; i += this.itemsHistory) {
        hisList.push(this.historyItems.slice(i, i + this.itemsHistory));
      }
      return hisList;
    },
    historyCount() {
      return Math.ceil(this.historyItems.length / this.itemsHistory);
    },
    itemsCount() {
      return Math.ceil(this.images.length / this.itemsPerGroup);
    },
    titlePxlt() {
      return this.$t("language.parallelForum");
    },
    titleXszh() {
      return this.$t("language.onlineFair");
    },
    pastReview() {
      return this.$t("language.review");
    },
    brilliantMoment() {
      return this.$t("language.brilliantMoments");
    },
    nameOfConference() {
      return this.$t("language.nameOfConference");
    },
    themeOfConference() {
      return this.$t("language.themeOfConference");
    },
    guestsAtExhibition() {
      return this.$t("language.guestsAtExhibition");
    },
    exhibitionEffectiveness() {
      return this.$t("language.exhibitionEffectiveness");
    },
    itineraryLeftBtnDisabled() {
      return this.currItineraryIndex == 0 ? true : false;
    },
    itineraryRightBtnDisabled() {
      return this.currItineraryIndex >= this.itineraryTimeList.length - 4
        ? true
        : false;
    },
    newImportGuestList() {
      let index = 0;
      let newArray = [];
      while (index < this.importGuestList.length) {
        newArray.push(this.importGuestList.slice(index, (index += 8)));
      }
      return newArray;
    },
    newBeforeGuestList() {
      let index = 0;
      let newArray = [];
      while (index < this.beforeGuestList.length) {
        newArray.push(this.beforeGuestList.slice(index, (index += 8)));
      }
      return newArray;
    },
    photoGroups() {
      const groupSize = 4
      const result = []
      for (let i = 0; i < this.photoList.length; i += groupSize) {
        result.push(this.photoList.slice(i, i + groupSize))
      }
      return result
    },
    carouselItemCount() {
      return Math.ceil(this.photoList.length / 4)
    }
  },
  watch: {
    lang(val) {
      if (val == "zh-CN") {
        this.languageVersion = 1;
      } else {
        this.languageVersion = 2;
      }

      this.initActivity("20241", this.languageVersion); //重大活动
      this.initActivity("20242", this.languageVersion); //六百对接
      this.initActivity("20243", this.languageVersion); //专题活动
      this.form(this.languageVersion);
      this.moreText = this.$t("language.more");
      this.key += 1;
    },
  },
  mounted() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#app',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    this.$nextTick(this.updateScrollStatus)
    this.startAutoChange()
    if (this.$store.state.lang == "zh-CN") {
      this.languageVersion = 1;
    } else {
      this.languageVersion = 2;
    }
    this.moreText = this.$t("language.more");
    this.init();
    this.initActivity("20241", this.languageVersion); //重大活动
    this.initActivity("20242", this.languageVersion); //六百对接
    this.initActivity("20243", this.languageVersion); //专题活动
    let now = new Date().getTime();
    let target = new Date("2024-09-20T00:00:00+08:00").getTime();
    // 计算时间差，并将毫秒转换成天数
    let diffInMs = target - now;
    let diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
    if (diffInDays <= 0) this.num = "0";
    else
      this.num =
        diffInDays < 100 ? diffInDays.toString() : diffInDays.toString();

    this.form(this.languageVersion);

    setTimeout(() => {
      this.isVisible = false;
    }, 5000); // 10000 毫秒即 10 秒

    var startTime = new Date("2024/09/20 14:00:00");
    //	var endTime = new Date("2024/09/20 15:00:00");

    var timeDifference = startTime.getTime() - now;
    //timeDifference = (startTime.getTime() - endTime.getTime());
    // 如果时间间隔小于等于1小时
    if (timeDifference <= 0) {
      this.showTip = false;
    } else {
      this.showTip = true;
    }

    //	this.num = "0"
  },
  // 离开页面销毁视频播放器
  beforeDestroy() {
    if (this.player != null) {
      this.player.dispose(); // dispose()会直接删除Dom元素
    }
  },
  methods: {
    handleCarouselChange(index){
      this.currentCarouselIndex = index
    },
    updateScrollStatus() {
      const listEl = this.$refs.list
      const boxEl = this.$refs.listBox

      const allWidth = listEl.scrollWidth
      const boxWidth = boxEl.clientWidth

      const leftValStr = listEl.style.left
      const leftValParsed = parseInt(leftValStr, 10)
      const leftVal = isNaN(leftValParsed) ? 0 : leftValParsed

      const leftAbs = Math.abs(leftVal)

      this.scrollAtStart = leftVal >= 0
      this.scrollAtEnd = leftAbs + boxWidth >= allWidth
    },
    scrollLeft(){
      const listEl = this.$refs.list
      const leftVal = parseInt(getComputedStyle(listEl).left) || 0
      const step = 440  // 项目宽400 + 40 margin
      let target = leftVal + step
      if (target > 0) target = 0
      listEl.style.left = `${target}px`
      this.$nextTick(this.updateScrollStatus)
      console.log("this.scrollAtEnd",this.scrollAtEnd);
      console.log("this.scrollAtStart",this.scrollAtStart);
    },
    scrollRight(){
      const listEl = this.$refs.list
      const boxEl = this.$refs.listBox

      const allWidth = listEl.scrollWidth
      const boxWidth = boxEl.clientWidth
      const step = 440  // 项目宽400 + 40 margin

      const leftVal = parseInt(getComputedStyle(listEl).left) || 0
      let target = leftVal - step
      const maxLeft = -(allWidth - boxWidth)

      if (target < maxLeft) target = maxLeft
      listEl.style.left = `${target}px`
      this.$nextTick(this.updateScrollStatus)
    },
    changeItineraryType(index){
      this.itineraryActiveIndex = index
    },
    showNewsType(item,index){
      console.log(item);
      this.newsTypeIndex = index
    },
    startAutoChange () {

      this.$nextTick(() => {
        // if (this.bannerList[0].videoUrl) {
        //   this.videoLoading = true
        // } else {
        //   this.videoLoading = false
        // }
        this.initVideoPlayer()
      })
      // this.intervalId = setInterval(() => {
      //   this.activeIndex = this.activeIndex < this.totalDots ? this.activeIndex + 1 : 1
      //   this.setActive(this.activeIndex)
      // }, 6000)
    },
    handleDotClick (index) {
      if (this.activeIndex !== index) {
        this.activeIndex = index
        this.setActive(index)
        clearInterval(this.intervalId)
        this.intervalId = setInterval(() => {
          this.activeIndex = this.activeIndex < this.totalDots ? this.activeIndex + 1 : 1
          this.setActive(this.activeIndex)
        }, 6000)
      }
    },
    setActive (val) {
      this.$nextTick(() => {
        if (this.$refs.bannerRef) {
          this.$refs.bannerRef.setActiveItem(val - 1)
        }
      })
    },
    goLiving() {
      window.open(
        "https://www.wmconvention.com/#/livingDetail?id=230",
        "_blank"
      );
    },
    showDetail(val) {
      this.$refs.detailModalRef.init(val);
    },
    toVirtualExhibition() {
      let clientWidth = document.documentElement.clientWidth;
      if (clientWidth > 1024) {
        window.open(
          "https://vr.wmconvention.com/2023/view/pavilion/",
          "_blank"
        );
      } else {
        window.open(
          "https://vr.wmconvention.com/2023/view/pavimobile/",
          "_blank"
        );
      }
      // Message({
      //   showClose: true,
      //   message: "敬请期待",
      //   customClass: "my-message",
      // });
    },
    toRegister() {
      window.location.href = "https://work.wmconvention.com/";
      // Message({
      //   showClose: true,
      //   message: "敬请期待",
      //   customClass: "my-message",
      // });
      // this.$router.push("/wmcEntranceNew");
    },
    toLogin() {
      window.location.href = "https://work.wmconvention.com/";
    },
    toDetail(index) {
      if (index === 0) {
        this.$router.push("/wmcActivities#zdhd");
      } else if (index === 1) {
        this.$router.push("/wmcActivities#lbdj");
      } else if (index === 2) {
        this.$router.push("/wmcActivities#zthd");
      } else if (index === 3) {
        this.$router.push("/wmcActivities#pxlt");
      } else if (index === 4) {
        this.$router.push("/wmcActivities#schlt");
      } else if (index === 5) {
        this.$router.push("/wmcActivities#xgslt");
      }
    },
    showImage(image) {
      this.selectedImage = image;
    },
    hideImage() {
      this.selectedImage = null;
    },
    initActivity(type, languageVersion) {
      Axios.get(global.apiUrl + "/wmc-server/activity-info-view/publish/", {
        params: {
          current: 1,
          size: 6,
          type: type,
          status: 4,
          year: "2024",
          languageVersion: languageVersion,
        },
      }).then((response) => {
        if (response.data.code === 200) {
          // list.length = 0;
          let subItems = response.data.data.list.map((item) => ({
            id: item.id,
            name: item.activityTheme,
            scheme: "",
            imgSrc: item.exhibitorCardImg,
            activityTheme: item.activityTheme,
            introduction: item.introduction,
            undertakeUnit: item.undertakeUnit,
            activityTime: item.activityTime,
            activityLocation: item.activityLocation,
            activityType: item.activityType,
          }));
          if (type === "20241") {
            this.activities[0].category = this.$t("language.majorEvents");
            this.activities[0].subItems = subItems;
          } else if (type === "20242") {
            this.activities[1].category = this.$t("language.projectDocking");
            this.activities[1].subItems = subItems;
          } else if (type === "20243") {
            this.activities[2].category = this.$t(
              "language.thematicActivities"
            );
            this.activities[2].subItems = subItems;
          } else {
            return;
          }
          console.log("activities", this.activities);
          // for (var i = 0; i < response.data.data.list.length; i++) {
          //   var test = {
          //     path: "/activeDetail",
          //     imgSrc: require("../../assets/images/forum.png"),
          //     query: {
          //       id: "",
          //     },
          //     scheme: "",
          //     activityTheme: "", // 活动名称
          //     introduction: "", // 简介
          //     undertakeUnit: "", // 承办单位
          //     activityTime: "", // 活动时间
          //     activityLocation: "", // 活动地点
          //     activityType: "", // 活动类型
          //   };
          //   test.imgSrc = response.data.data.list[i].exhibitorCardImg;
          //   test.activityTheme = response.data.data.list[i].activityTheme;
          //   test.introduction = response.data.data.list[i].introduction;
          //   test.undertakeUnit = response.data.data.list[i].undertakeUnit;
          //   test.activityTime = response.data.data.list[i].activityTime;
          //   test.activityLocation = response.data.data.list[i].activityLocation;
          //   test.activityType = response.data.data.list[i].activityType;
          //   test.query.id = response.data.data.list[i].id;
          //   if (test.activityType === "1") {
          //     test.path = "/cloudOpening";
          //   }
          //   list.push(test);
          //   list.forEach((data) => {
          //     // 获取行程信息
          //     Axios.get(
          //       global.apiUrl +
          //         `/wmc-server/activity-info-view/scheme/${data.query.id}`
          //     ).then((response2) => {
          //       const res = response2.data.data;
          //       if (res.length > 0) {
          //         data.scheme = res[0].schemeTheme;
          //       }
          //     });
          //   });
          // }
        }
      });
    },
    oneShowInfo() {
      this.showInfo = true;
    },
    oneShowMedium() {
      this.showInfo = false;
    },
    prev() {
      this.$refs.carouselGuest.prev();
      // 自定义滚动逻辑
    },
    next() {
      this.$refs.carouselGuest.next();
      // 自定义滚动逻辑
    },
    oneShowActive() {
      this.showActive = true;
    },
    oneShowDate() {
      this.showActive = false;
    },
    //初始化swiper
    initSwiper() {},
    initVideoPlayer() {
      this.videoLoading = true
      const videoOptions = {
        // 视频配置选项
      };
      // if (this.bannerList[0].videoUrl) {
        //   this.videoLoading = true
        // } else {
        //   this.videoLoading = false
        // }

      const videoElement = document.getElementById(this.videoId);
      console.log("videoElement",videoElement);
      if (!videoElement) {
        //		console.error("无效的视频元素 ID");
        setTimeout(() => {
          this.initVideoPlayer(); //延迟500ms继续执行这个初始化函数，继续判断是否能获取到
        }, 500);
        return;
      }
      const player = videojs(videoElement, videoOptions, () => {
        // Video.js 初始化完成回调
        // 监听 Video.js 播放事件

        player.on("play", () => {
          this.handleVideoPlayEvent();
        });
      });
      // 将 Video.js 播放器实例保存到组件数据中
      this.player = player;
    },
    handlePlayButtonClick() {
      this.videoLoading = false
      // 用户点击播放按钮后，解除静音
      this.unmuteVideo();
    },
    unmuteVideo() {
      if (this.player) {
        this.player.muted(false); // 解除静音
      }
    },
    handleVideoPlayEvent() {
      this.videoLoading = false;
      // 处理 Video.js 播放事件
      // if (this.player) {
      //   this.player.muted(false); // 解除静音
      // }
      // ... 执行其他操作
    },

    init() {
      // banner
      // Axios.get(global.apiUrl + "/wmc-server/api/index/siteCiv", {
      // 	params: {
      // 		year: "2024"
      // 	}
      // }).then((res) => {
      // 	this.bannerList = res.data.data.list;

      // 	this.initSwiper();
      // });
      // this.player = videojs("banner-video", function () {
      //   this.play();
      // });
      //循环展示大会动态
      Axios.get(global.apiUrl + "/wmc-server/notice/website/IndexSearch", {
        params: {
          current: 1,
          size: 5,
          type: "1",
          year: "2023",
          status: 0,
        },
      }).then((response) => {
        // let list = response.data.data.list.map((item) => ({
        let list = response.data.data.map((item) => ({
          path: {
            path: "/newsDetail",
            query: {
              id: item.id,
              type: "1",
            },
          },
          text: item.title,
          time: item.startDate,
        }));
        this.conferenceItem.conferenceList = list;
      });
      //循环展示通知公告
      Axios.get(global.apiUrl + "/wmc-server/notice/website/IndexSearch", {
        params: {
          current: 1,
          size: 5,
          type: "2",
          status: 0,
          year: "2023",
        },
      }).then((response) => {
        for (var i = 0; i < response.data.data.length && i < 3; i++) {
          var data = response.data.data[i];
          this.notifyItem.notifyList[i].path.path = "/newsDetail";
          this.notifyItem.notifyList[i].path.query.id = data.id;
          this.notifyItem.notifyList[i].text = data.title;
          this.notifyItem.notifyList[i].time = data.startDate.substring(0, 10);
        }
      });
      // //展示往届嘉宾
      // Axios.get(global.apiUrl + "/wmc-server/notice/website/search", {
      //   params: {
      //     current: 1,
      //     size: 100,
      //     type: "14",
      //   },
      // }).then((response) => {
      //   this.beforeGuestList.length = 0;
      //   var testList = [];
      //   // var count = 0;
      //   for (var i = 0; i < response.data.data.list.length; i++) {
      //     var data = response.data.data.list[i];
      //     var test = {
      //       duty: "",
      //       introduce: "",
      //       imgSrc: require("../../assets/images/guest/ds.png"),
      //       name: "",
      //       company: "",
      //     };
      //     test.duty = data.digest;
      //     test.introduce = data.content;
      //     console.log(data.attachment[0].name);
      //     test.imgSrc = data.attachment[0].name || "";
      //     test.name = data.title;
      //     test.company = data.enterpris;
      //     testList.push(test);
      //     // if (i % 7 === 0 && i != 0) {
      //     //   console.log("test" + testList);
      //     //   var tests = testList;
      //     //   this.beforeGuestList.push(tests);
      //     //   console.log("等于八" + this.beforeGuestList);
      //     //   testList.length = 0;
      //     //   count = count + 1;
      //     // }
      //   }
      //   // console.log(count);
      //   // if (count === 0) {
      //   //   this.beforeGuestList.push(testList);
      //   // } else {
      //   //   console.log("测试" + JSON.stringify(this.beforeGuestList));
      //   //   this.beforeGuestList = new Array(this.beforeGuestList, testList);
      //   // }
      //   this.beforeGuestList = testList;

      //   this.keyss++;
      // });
      //循环展示媒体报道
      Axios.get(global.apiUrl + "/wmc-server/notice/website/IndexSearch", {
        params: {
          current: 1,
          size: 5,
          type: "6",
          year: "2023",
          status: 0,
        },
      }).then((response) => {
        // let list = response.data.data.list.map((item) => ({
        let list = response.data.data.map((item) => ({
          path: {
            path: "/newsDetail",
            query: {
              id: item.id,
              type: "6",
            },
          },
          text: item.title,
          time: item.startDate,
        }));
        this.reportItem.reportList = list;
      });
      // 循环展示精彩瞬间
      Axios.get(global.apiUrl + "/wmc-server/notice/website/search", {
        params: {
          current: 1,
          size: 100,
          type: "3",
          status: 0,
          year: "2023",
        },
      }).then((res) => {
        res.data.data.list.forEach((data) => {
          data.attachment.forEach((attachment) => {
            const test = {
              id: "",
              pathSrc: "/",
              url: require("../../assets/images/2018-4.png"),
            };
            test.id = attachment.fileId;
            test.url = attachment.name;
            this.images.push(test);
          });
        });
        // for (var i = 0; i < response.data.list.length; i++) {
        //   for (var j = 0; j < response.data.list[i].attachment.length; j++) {
        //     var under = {};
        //     var float = {};
        //     if (j % 2 == 0) {
        //       float["imgSrc"] = response.data.list[i].attachment[j].name;
        //       this.floatList.push(float);
        //     } else if (j % 2 == 1) {
        //       under["imgSrc"] = response.data.list[i].attachment[j].name;
        //       this.underList.push(under);
        //     }
        //   }
        // }
      });
      // 展示大会视频

      // Axios.get(global.apiUrl + "/wmc-server/notice/website/IndexSearch", {
      //   params: {
      //     current: 1,
      //     size: 100,
      //     type: "15",
      //     year: "2023",
      //   },
      // }).then((response) => {
      //   for (var i = 0; i < response.data.data.length; i++) {
      //     if (response.data.data[i].stickTop === 1) {
      //       // this.isReloadData = false;
      //       this.newsVideoUrl = response.data.data[i].attachment[0].name;
      //       this.$nextTick(() => {
      //         // this.isReloadData = true;
      //       });
      //       //   document
      //       //     .getElementById("newsVideo")
      //       //     .html("<source :src=" + this.newsVideoUrl + " />");
      //     }
      //   }
      // });
    },
    entryKMS() {
      window.open(
        "https://www.wmconvention.com/#/livingDetail?id=372",
        "_blank"
      );
    },

    bannerClick(url) {
      if (url && url.indexOf("http") > -1) {
        window.open(url, "_blank");
      }
    },
    // 新闻中心视频播放
    // newsVideoPlay() {
    //   this.newsVideoMask = false;
    //   var video = document.getElementById("newsVideo");
    //   video.play();
    // },
    // 活动日程点击事件
    itineraryClick(val) {
      this.itineraryIndex = val;
    },
    itineraryArrowClick(flag) {
      if (flag == "right") {
        let count = this.itineraryTimeList.length;
        if (this.currItineraryIndex >= count - 4)
          return this.$message.warning("最后一条啦");
        this.currItineraryIndex++;
      } else {
        if (this.currItineraryIndex == 0)
          return this.$message.warning("已经是第一条啦");
        this.currItineraryIndex--;
      }
      this.itineraryLeft = -this.currItineraryIndex * 25;
    },
  },
};
</script>
<style lang="scss" scoped>
video {
  width: 100%;
  object-fit: cover;
}
.myVideo-dimensions{
  width: 100%;
}
.myVideo_html5_api{
  width: 100%;
  height: 800px;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s;
}

.fade-enter,
	.fade-leave-to

	/* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}

// banner
.wmc-banner {
  margin-bottom: -4px;
  width: 100%;
  height: 800px;
  position: relative;

  .banner-cont {
    position: absolute;
    top: 231px;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    width: 100%;
  }
  .banner-cont-container {
    width: 744px;
    margin: auto;
  }
  .banner-btn {
    margin-top: 30px;
  }
  .banner-cont-title {
    font-family: SourceHanSansSC, SourceHanSansSC;
    font-weight: 400;
    font-size: 32px;
    color: #FFFFFF;
    line-height: 24px;
    letter-spacing: 5px;
    text-align: center;
    font-style: normal;
    text-transform: uppercase;
    padding-bottom: 27px;
  }
  .second-title {
    font-family: DingTalk JinBuTi;
    font-weight: normal;
    font-size: 80px;
    color: #FFFFFF;
    line-height: 96px;
    text-align: center;
    font-style: normal;
    padding-bottom: 19px;
  }
  .banner-detail{
    padding-bottom: 51px;
    font-size: 24px;
    color: #FFFFFF;
    text-align: center;

    .banner-detail-time{
      font-family: DINAlternate, DINAlternate;
    }
  }
  .banner-search-cont{
    width: 100%;
    display: flex;
    justify-content: center;
    .banner-search-input{
      font-family: PingFangSC, PingFang SC;
      display: flex;
      align-items: center;
      width: 512px;
      height: 80px;
      background: #FFFFFF10;
      border-radius: 40px;
      backdrop-filter: blur(10px);
    }
    .serch-icon{
      width: 24px;
      height: 24px;
      margin-left: 24px;
      margin-right: 21px;
      >img{
        width: 100%;
        height: 100%;
      }
    }
    .banner-search-input input {
        font-family: PingFangSC, PingFang SC;
        height: 100%;
        border: none;
        color: #FFFFFF;
        background: transparent;
        width: calc(512px - 164px - 24px - 45px - 8px);
        line-height: 28px;
        box-sizing: border-box;
        font-size: 20px;
        outline: none;
        opacity: 1;

        &::placeholder{
          color: #FFFFFF;
          font-family: PingFangSC, PingFang SC;
        }
      }
      .banner-search-btn{
        width: 164px;
        height: 64px;
        margin-right: 8px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        background: linear-gradient( 135deg, #0064D2 0%, #ED006D 100%), #FFFFFF;
        border-radius: 40px;
        backdrop-filter: blur(10px);
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #FFFFFF;
        font-style: normal;
        cursor: pointer;

        .serch-arrow{
          width: 20px;
          height: 10px;
        }
      }
  }
  /deep/.el-button--primary {
    height: 50px;
    width: 200px;
    font-size: 18px;
    background-color: #005aff !important;
    border-radius: 8px;
  }
  /deep/.el-carousel__arrow {
    display: none;
  }
  .item-img {
    width: 100%;
    height: 800px;
  }
  .slick-dots {
    position: absolute;
    z-index: 10;
    left: 0;
    bottom: 40px;
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
  }
  .dot-progress {
    margin-right: 15px;
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    opacity: 0.5;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    // margin-right: 15px;
    // display: block;
    // width: 118px;
    // cursor: pointer;
    // opacity: 0.2;
    // height: 18px;
    // background-image: linear-gradient(
    //   to bottom,
    //   transparent 0,
    //   transparent 7px,
    //   rgba(255, 255, 255, 0.72) 7px,
    //   rgba(255, 255, 255, 0.72) 10px,
    //   transparent 10px,
    //   transparent 18px
    // );
    // white-space: nowrap;
    // position: relative;
    // overflow: hidden;
    &::after {
      content: "";
      display: inline-block;
      position: absolute;
      top: 0;
      left: 0;
      width: 0px;
      height: 5px;
      background: linear-gradient(135deg, #0064D2 0%, #ED006D 100%);
      opacity: 0.75;
      transition: all 0.3s ease;
      // content: "";
      // display: inline-block;
      // position: absolute;
      // top: 8px;
      // left: 0px;
      // width: 0px;
      // height: 2px;
      // text-align: center;
      // // background: #ffffff;
      // background: linear-gradient( 135deg, #0064D2 0%, #ED006D 100%);
      // opacity: 0.75;
    }
  }
  .dot-progress:hover {
    opacity: 0.8;
  }
  .banner-dot {
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    opacity: 0;
    transform: translate3d(-2px, -6.5px, 0);
  }
  .dot-img {
    width: 14px;
    height: 14px;
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    opacity: 0;
    transform: translate3d(-2px, -6.5px, 0);
  }
  .dot-progress.active {
    width: 118px;
    height: 4px;
    border-radius: 2px;
    opacity: 1;
    background-image: linear-gradient(
      to bottom,
      transparent 0,
      transparent 7px,
      rgba(255, 255, 255, 0.72) 7px,
      rgba(255, 255, 255, 0.72) 10px,
      transparent 10px,
      transparent 18px
    );

    &::after {
      width: 118px;
      opacity: 1;
      animation: borderAni 6s linear;
      transform-origin: left center;
    }
    // opacity: 1;
    // position: relative;
    // &::after {
    //   opacity: 1;
    //   width: 118px;
    //   animation: borderAni 6s linear;
    //   -ms-transform-origin: left center;
    //   transform-origin: left center;
    // }
  }

  .dot-img.active {
    z-index: 2;
    opacity: 0;
    display: block;
    animation: bugAni 6s linear;
    animation-fill-mode: forwards;
  }

  .count-down{
    position: absolute;
    bottom: 40px;
    right: 91px;
    display: flex;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 24px;
    color: #FFFFFF;
    line-height: 33px;
    text-align: center;
    font-style: normal;
    align-items: center;

    .count-down-num{
      width: 40px;
      height: 40px;
      margin: 0 8px;
      line-height: 40px;
      background: url("../../assets/images/newWmc/countdown-bg.svg") no-repeat;;
    }
  }
}
// 数据
.wmc-data{
  width: 100%;
  height: 285px;
  background: linear-gradient( 135deg, #0064D2 0%, #ED006D 100%), #D8D8D8;

  .wmc-data-cont{
    width: calc(100% - 710px);
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }
  .wmc-data-item{
    text-align: center;
  }
  .wmc-data-item-num{
    color: #fff;
    font-size: 40px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
  }
  .wmc-data-item-num-text{
    font-family:DIN Condensed Bold;
    font-weight: 900;
    font-size: 72px;
    color: #FFFFFF;
    line-height: 86px;
    text-align: center;
    font-style: normal;
  }
  .wmc-data-item-name{
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 28px;
    text-align: center;
    font-style: normal;
  }
}

// 新闻中心
.wmc-news-center {
  overflow: hidden;
  width: 100%;
  height: 944px;
  background: url("../../assets/images/newWmc/news-bg.png") no-repeat;
  background-size: 100% 100%;

  .wmc-news-cont{
    width: 1440px;
    margin: auto;
  }
  .wmc-news-title{
    display: flex;
    padding: 100px 0 60px 0;
    height: 76px;
    align-items: center;
    justify-content: space-between;
  }
  .wmc-news-title-left{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 48px;
    color: #272848;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    position: relative;

    &::before{
      position: absolute;
      content: '';
      width: 100%;
      height: 8px;
      border-radius: 4px;
      bottom: -28px;
      left: 0;
      background: linear-gradient( 135deg, #0064D2 0%, #ED006D 100%);
    }
  }
  .wmc-news-title-right{
    display: flex;

    .tabs-item{
      cursor: pointer;
      margin-left: 40px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 24px;
      color: #272848;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .tabs-item-active{
      color: #0064D2;
    }
  }
  .wmc-news-list{
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    padding-bottom: 40px;

    .wmc-news-item{
      width: 700px;
      height: 250px;
      background: #FFFFFF;
      padding: 40px;
      box-sizing: border-box;

      .wmc-news-item-title{
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 24px;
        color: #272848;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        padding-bottom: 20px;
      }
      .wmc-news-item-time{
        font-family: DIN Condensed Bold;
        font-weight: 400;
        font-size: 16px;
        color: #717991;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        padding-bottom: 20px;
      }
      .wmc-news-item-text{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #272848;
        line-height: 28px;
        text-align: justify;
        font-style: normal;
        height: 56px;
        overflow: hidden;
        display: -webkit-box;
        text-overflow: ellipsis;
        overflow: hidden;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
      .wmc-news-item-btn{
        padding-top: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #0064D2;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        cursor: pointer;
        position: relative;
        
        &::before{
          position: absolute;
          content: '';
          height: 4px;
          width: 19px;
          left: 72px;
          bottom: 0;
          background: url("../../assets/images/newWmc/more-icon-blue.png") no-repeat;
          background-size: 100% 100%;
        }
      }
      &:hover{
        background: url("../../assets/images/newWmc/news-item-bg.png") no-repeat;
        background-size: 100% 100%;

        .wmc-news-item-title{
          color: #FFFFFF;
        }
        .wmc-news-item-time{
          color: #FFFFFF;
        }
        .wmc-news-item-text{
          color: #FFFFFF;
        }
        .wmc-news-item-btn{
          color: #FFFFFF;
          
          &::before{
            background: url("../../assets/images/newWmc/more-icon-white.png") no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
  .wmc-news-more{
    cursor: pointer;
    width: 160px;
    height: 48px;
    border-radius: 24px;
    border: 1px solid #E4E6EF;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #000000;
    line-height: 48px;
    text-align: center;
    font-style: normal;
    margin: auto;

    &:hover{
      color: #fff;
      border: none;
      background: linear-gradient(135deg, #0064D2 0%, #ED006D 100%);
    }
  }
}

// 活动日程
.wmc-itinerary {
  height: 740px;
  overflow: hidden;
  width: 100%;
  display: flex;

  .wmc-itinerary-left{
    width: 1180px;
    height: 100%;
    background: url("../../assets/images/newWmc/major-event-bg.png") no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    justify-content: flex-end;

    .wmc-itinerary-left-cont{
      width: 518px;
      padding-top: 100px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .wmc-itinerary-title{
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 48px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      position: relative;
      width: 192px;

      &::before{
        content: '';
        width: 100%;
        position: absolute;
        height: 8px;
        background: #fff;
        border-radius: 4px;
        left: 0;
        bottom: -28px;
      }
    }
    .wmc-itinerary-type{
      height: 480px;
      background: #00000050;
      backdrop-filter: blur(10px);
    }
    .wmc-itinerary-type-item{
      cursor: pointer;
      display: flex;
      height: 120px;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #FFFFFF;
      line-height: 24px;
      font-style: normal;
      padding: 0 80px;
      box-sizing: border-box;
      width: 100%;

      .itinerary-icon{
        width: 30px;
        height: 27px;
      }
      .wmc-itinerary-type-name{
        width: 268px;
        padding-left: 40px;
        box-sizing: border-box;
        text-align: left;
      }
    }
    .wmc-itinerary-type-active{
      background: #0064D2;
      font-weight: 600;
    }
  }
  .wmc-itinerary-left-bg0{
    background: url("../../assets/images/newWmc/major-event-bg.png") no-repeat;
    background-size: 100% 100%;
  }
  .wmc-itinerary-left-bg1{
    background: url("../../assets/images/newWmc/project-docking-bg.png") no-repeat;
    background-size: 100% 100%;
  }
  .wmc-itinerary-left-bg2{
    background: url("../../assets/images/newWmc/special-activities-bg.png") no-repeat;
    background-size: 100% 100%;
  }
  .wmc-itinerary-left-bg3{
    background: url("../../assets/images/newWmc/conference-release-bg.png") no-repeat;
    background-size: 100% 100%;
  }
  .wmc-itinerary-right{
    height: 100%;
    width: calc(100% - 1180px);
    background: url("../../assets/images/newWmc/activity-bg.png") no-repeat;
    background-size: 100% 100%;

    .itinerary-activity-cont{
      padding: 100px 0 60px 80px;
    }
    .itinerary-activity-list{
      padding-bottom: 51px;
      height: 480px;
    }
    .itinerary-activity-item{
      display: flex;
      align-items: center;
      cursor: pointer;

      .itinerary-activity-logo{
        width: 24px;
        height: 24px;
        margin-right: 29px;
      }
      .itinerary-activity-text{
        width: 373px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #272848;
        line-height: 80px;
        text-align: left;
        font-style: normal;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      &:hover{
        .itinerary-activity-text{
          color: #0064D2;
          font-weight: 600;
        }
      }
    }
    .more-activity-btn{
      cursor: pointer;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #0064D2;
      line-height: 25px;
      text-align: justify;
      font-style: normal;
    }
  }
}
@media screen and (max-width: 1366px) {
  .active-title-text {
    color: #ffffff;
    font-size: 12px !important;
    line-height: 68px !important;
    padding-left: 30px;
  }

  .active-title-more {
    font-size: 12px !important;
    line-height: 68px !important;
  }
}

.overlayImg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  overflow: auto;
}

.modal {
  max-width: 80%;
  max-height: 80%;
  display: flex;
}

.imgCont {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.closeImg {
  width: 40px;
  height: 40px;
  margin-top: -30px;
  margin-left: 20px;
}

// 论坛活动
.wmc-forum {
  // overflow: hidden;
  width: 1200px;
  margin: 0 auto;
}

// 展商信息
.wmc-exhibitor-info{
  width: 100%;
  height: 924px;
  background: #FAFAFA;
  position: relative;
  overflow: hidden;

  .wmc-exhibitor-arrow-btn{
    position: absolute;
    right: 240px;
    top: 116px;
    width: 140px;
    display: flex;
    justify-content: space-between;

    .wmc-exhibitor-arrow-left{
      width: 60px;
      height: 60px;
      cursor: pointer;
    }
    .wmc-exhibitor-arrow-left-disable{
      width: 60px;
      height: 60px;
      cursor: not-allowed;
    }
    .wmc-exhibitor-arrow-right{
      width: 60px;
      height: 60px;
      cursor: pointer;
    }
    .wmc-exhibitor-arrow-right-disable{
      width: 60px;
      height: 60px;
      cursor: not-allowed;
    }
  }

  .wmc-exhibitor-list{
    display: flex;
    position: relative;
    left: 0;
    transition: left 0.3s ease;

    .wmc-exhibitor-item{
      flex: 0 0 400px;
      height: 520px;
      background: #FFFFFF;
      margin-right: 40px;
    }
    .last-item {
      margin-right: 0;
    }
    .wmc-exhibitor-vr{
      height: 172px;
      width: 100%;
      position: relative;
    }
    .wmc-exhibitor-img{
      width: 100%;
      height: 100%;
    }
    .wmc-exhibitor-vr-btn{
      position: absolute;
      z-index: 2;
      width: 80px;
      height: 80px;
      top: 46px;
      left: 160px;
    }
    .wmc-exhibitor-bottom{
      padding: 40px;
      box-sizing: border-box;
    }
    .wmc-exhibitor-name{
      display: flex;
      align-items: center;
      padding-bottom: 21px;

      >span{
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #272848;
        line-height: 24px;
        text-align: left;
        font-style: normal;
      }
    }
    .wmc-exhibitor-logo{
      height: 32px;
      width: 32px;
      border-radius: 100%;
      margin-right: 14px;
    }
    .wmc-exhibitor-postion{

      .wmc-exhibitor-postion-row{
        padding-bottom: 10px;
      }

      .wmc-exhibitor-label{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #717991;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        padding-right: 14px;
        display: inline-block;
      }
      .wmc-exhibitor-value{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #272848;
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }
    }
    .wmc-exhibitor-detail{
      padding-top: 3px;
      height: 112px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      line-height: 28px;
      text-align: justify;
      font-style: normal;
      display: -webkit-box;
      -webkit-line-clamp: 4;     /* 最多显示 3 行 */
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .wmc-exhibitor-item-btn{
        padding-top: 24px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #0064D2;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        cursor: pointer;
        position: relative;
        
        &::before{
          position: absolute;
          content: '';
          height: 4px;
          width: 19px;
          left: 72px;
          bottom: 0;
          background: url("../../assets/images/newWmc/more-icon-blue.png") no-repeat;
          background-size: 100% 100%;
        }
    }
  }
  .wmc-exhibitor-more-btn{
    cursor: pointer;
    width: 160px;
    height: 48px;
    border-radius: 24px;
    border: 1px solid #E4E6EF;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #000000;
    line-height: 48px;
    text-align: center;
    font-style: normal;
    margin: 40px auto 0;

    &:hover{
      color: #fff;
      border: none;
      background: linear-gradient(135deg, #0064D2 0%, #ED006D 100%);
    }
  }
}

// 线上展会
.wmc-online-show {
  width: 100%;
  height: 460px;
  background: url("../../assets/images/newWmc/online-show-bg.png") no-repeat;
  background-size: 100% 100%;
  overflow: hidden;

  .wmc-online-show-cont{
    padding-left: 240px;
    padding-top: 120px;
  }
  .wmc-online-show-title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 36px;
    color: #FFFFFF;
    line-height: 36px;
    text-align: left;
    font-style: normal;
  }
  .wmc-online-show-name{
    padding-top: 40px;
    padding-bottom: 60px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 36px;
    color: #FFFFFF;
    line-height: 36px;
    text-align: left;
    font-style: normal;

    >span{
      font-family: DIN-Heavy;
      font-weight: 900;
      font-size: 36px;
      color: #FFFFFF;
      line-height: 36px;
      text-align: left;
      font-style: normal;
    }
  }
  .wmc-online-show-btn{
    width: 140px;
    height: 48px;
    line-height: 48px;
    background: #0064D2;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #FFFFFF;
    text-align: center;
    font-style: normal;
    cursor: pointer;

    &:hover{
      background-color: #248CFF;
    }
  }
}

// 精彩瞬间，往届回顾
.wmc-before {
  width: 100%;
  background-color: #fff;
  margin: 0 auto;

  .wmc-before-cont {
    height: 725px;

    .wmc-before-pane {
      display: flex;
      padding-top: 15px;
    }
    .wmc-before-detail {
      width: 1180px;
      height: 640px;
      background: url("../../assets/images/newWmc/wmc-before-bg.png") no-repeat;
      background-size: 100% 100%;
    }
    .wmc-before-img {
      width: calc(100% - 1180px);
      height: 640px;

      > img {
        width: 100%;
        height: 640px;
        object-fit: fill;
      }
    }
    .wmc-before-detail-cont {
      padding: 80px 80px 0 240px;
    }
    .wmc-before-year {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 24px;
      color: #272848;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .wmc-before-theme {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 36px;
      color: #272848;
      line-height: 36px;
      text-align: left;
      font-style: normal;
      padding-top: 20px;
    }
    .wmc-before-theme-after-bar {
      width: 80px;
      height: 4px;
      margin-top: 20px;
      background: linear-gradient(135deg, #ed006d 0%, #0064d2 100%);
    }
    .wmc-before-detail-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #272848;
      line-height: 20px;
      text-align: justify;
      font-style: normal;
      padding-top: 20px;
      padding-bottom: 10px;
    }
    .wmc-before-detail-text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #272848;
      line-height: 28px;
      text-align: justify;
      font-style: normal;
    }

    /deep/.el-tabs__nav-scroll {
      width: 1400px;
      margin: auto;
    }

    /deep/.el-tabs__item {
      width: 82px;
      padding: 5px 59px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 18px;
      color: #717991;
      line-height: 25px;
      text-align: center;
      font-style: normal;
      box-sizing: content-box;
    }
    /deep/.el-tabs__active-bar {
      left: 6px;
      width: 69px !important;
    }
    /deep/.el-tabs__nav-wrap::after {
      background-color: transparent;
    }
    /deep/.el-tabs__item:hover {
      font-weight: 600;
      font-size: 24px;
      color: #0064d2;
      line-height: 33px;
      font-style: normal;
    }
    /deep/.el-tabs__item:hover {
      font-weight: 600;
      font-size: 24px;
      color: #0064d2;
      line-height: 33px;
      font-style: normal;
    }
    /deep/.el-tabs__item.is-active {
      font-weight: 600;
      font-size: 24px;
      color: #0064d2;
      line-height: 33px;
      font-style: normal;
    }
    /deep/.el-tabs__active-bar {
      height: 3px;
      background-color: #000000;
    }
  }
}

.wmc-jcsj {
  width: 100%;
  height: 1044px;
  box-sizing: border-box;
  background: #FFFFFF;

  .wmc-jcsj-cont{
    height: 640px;
    width: calc(100% - 480px);
    margin: auto;
    position: relative;

    .carousel-layout{
      display: flex;
      width: 100%;
      height: 100%;
      justify-content: center;
      gap: 8px;
    }
    .left-box {
      width: 716px;
      height: 640px;
    }
    .left-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
    .right-box {
      width: 716px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .top-img {
      width: 100%;
      height: 315px;
    }
    .bottom-imgs{
      display: flex;
      gap: 8px;
    }
    .bottom-img{
      width: 355px;
      height: 100%;
      object-fit: cover;
    }

    .photo-img{
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    /deep/.el-carousel__indicator.is-active .el-carousel__button {
				height: 10px !important;
				width: 30px !important;
				background-color: #0054a7 !important;
				border-radius: 15px;
			}

			/deep/.el-carousel__indicator .el-carousel__button {
				width: 10px !important;
				height: 10px !important;
				border-radius: 100%;
				background-color: #95b5d9 !important;
			}

			.el-carousel {
				position: relative;
			}

			.carousel-arrow {
				height: 60px;
				width: 60px;
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				color: #333;
				font-size: 24px;
				cursor: pointer;
			}

			.prev-arrow-disable {
        cursor: not-allowed;
				left: -60px;
				background: url("@/assets/images/newWmc/arrow-left-disable-icon.svg") no-repeat;
				background-size: 100% 100%;
			}
      .prev-arrow {
				left: -60px;
				background: url("@/assets/images/newWmc/arrow-left-icon.svg") no-repeat;
				background-size: 100% 100%;
			}

			.next-arrow-disable {
        cursor: not-allowed;
				right: -60px;
				background: url("@/assets/images/newWmc/arrow-right-disable-icon.svg") no-repeat;
				background-size: 100% 100%;
			}
      .next-arrow {
				right: -60px;
				background: url("@/assets/images/newWmc/arrow-right-icon.svg") no-repeat;
				background-size: 100% 100%;
			}
  }
  
  .wmc-jcsj-more-btn{
    cursor: pointer;
    width: 160px;
    height: 48px;
    border-radius: 24px;
    border: 1px solid #E4E6EF;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #000000;
    line-height: 48px;
    text-align: center;
    font-style: normal;
    margin: 40px auto 0;

    &:hover{
      color: #fff;
      border: none;
      background: linear-gradient(135deg, #0064D2 0%, #ED006D 100%);
    }
  }
}

// .wmc-before-item {
//   overflow: hidden;
//   position: absolute;
//   top: 0;
//   left: 0;
//   right: 0;
//   height: 470px;
//   ul {
//     // margin-top: 30px;
//   }
// }
#notice1,
#notice {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

#notice1 {
  margin-top: -24px;
}
.wmc-bi-scroll {
  float: left;
  white-space: nowrap;
}

.wmc-bi-jcsj {
  font-size: 0;
  white-space: nowrap;
}

.wmc-bi-jcsj span {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
}

.wmc-bi-jcsj span img {
  height: 220px;
  cursor: pointer;
}

.foreign-languages {
  .wmc-entrance .wmc-entrance-cont {
    text-align: center;

    .ec-item {
      width: 50%;
    }
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes bugAni {
  0% {
    opacity: 1;
    transform: translate3d(0, -7px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(107px, -7px, 0);
  }
}
@keyframes borderAni {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}
</style>
