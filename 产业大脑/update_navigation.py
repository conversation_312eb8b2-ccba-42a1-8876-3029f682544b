#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新产业大脑系统所有页面的导航菜单链接
"""

import os
import re

# 需要更新的页面文件列表
pages = [
    "产业资源管理.html",
    "招商企业地图.html",
    "招商企业档案.html",
    "产业经济看板.html",
    "产业图谱.html",
    "产业预警.html",
    "账号管理.html",
    "日志管理.html"
]

# 简单的链接替换映射
link_replacements = [
    ('href="#"', 'href="首页.html"', '首页'),
    ('href="#"', 'href="企业登记.html"', '企业登记'),
    ('href="#"', 'href="企业云图.html"', '企业云图'),
    ('href="#"', 'href="产业链管理.html"', '产业链管理'),
    ('href="#"', 'href="产业资源管理.html"', '产业资源管理'),
    ('href="#"', 'href="招商项目管理.html"', '招商项目管理'),
    ('href="#"', 'href="招商企业地图.html"', '招商企业地图'),
    ('href="#"', 'href="招商企业档案.html"', '招商企业档案'),
    ('href="#"', 'href="产业经济看板.html"', '产业经济看板'),
    ('href="#"', 'href="产业图谱.html"', '产业图谱'),
    ('href="#"', 'href="产业预警.html"', '产业预警'),
    ('href="#"', 'href="产业报告.html"', '产业报告'),
    ('href="#"', 'href="账号管理.html"', '账号管理'),
    ('href="#"', 'href="角色管理.html"', '角色管理'),
    ('href="#"', 'href="日志管理.html"', '日志管理'),
]

# 导航菜单模板
nav_template = '''                        <a href="首页.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">首页</a>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                企业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="企业登记.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业登记</a>
                                    <a href="企业云图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业云图</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业链管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业链管理</a>
                                    <a href="产业资源管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业资源管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                项目管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商项目管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商项目管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">重点项目管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业招商
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商企业地图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业地图</a>
                                    <a href="招商企业档案.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业档案</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业运营
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业经济看板.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业经济看板</a>
                                    <a href="产业图谱.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业图谱</a>
                                    <a href="产业预警.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业预警</a>
                                    <a href="产业报告.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业报告</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                系统管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="账号管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">账号管理</a>
                                    <a href="角色管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">角色管理</a>
                                    <a href="日志管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">日志管理</a>
                                </div>
                            </div>
                        </div>'''

# 页面高亮配置
page_highlights = {
    "企业登记.html": ("企业管理", "企业登记"),
    "企业云图.html": ("企业管理", "企业云图"),
    "产业链管理.html": ("产业管理", "产业链管理"),
    "产业资源管理.html": ("产业管理", "产业资源管理"),
    "招商项目管理.html": ("项目管理", "招商项目管理"),
    "招商企业地图.html": ("产业招商", "招商企业地图"),
    "招商企业档案.html": ("产业招商", "招商企业档案"),
    "产业经济看板.html": ("产业运营", "产业经济看板"),
    "产业图谱.html": ("产业运营", "产业图谱"),
    "产业预警.html": ("产业运营", "产业预警"),
    "产业报告.html": ("产业运营", "产业报告"),
    "账号管理.html": ("系统管理", "账号管理"),
    "角色管理.html": ("系统管理", "角色管理"),
    "日志管理.html": ("系统管理", "日志管理")
}

def update_navigation(filename):
    """更新单个页面的导航菜单"""
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return
    
    # 读取文件内容
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 获取高亮配置
    main_menu, sub_menu = page_highlights.get(filename, ("", ""))
    
    # 生成当前页面的导航菜单
    current_nav = nav_template
    
    # 高亮当前页面的主菜单
    if main_menu:
        if main_menu == "企业管理":
            current_nav = current_nav.replace(
                'button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                企业管理',
                'button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                企业管理'
            )
        elif main_menu == "产业管理":
            current_nav = current_nav.replace(
                'button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                产业管理',
                'button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                产业管理'
            )
        elif main_menu == "项目管理":
            current_nav = current_nav.replace(
                'button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                项目管理',
                'button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                项目管理'
            )
        elif main_menu == "产业招商":
            current_nav = current_nav.replace(
                'button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                产业招商',
                'button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                产业招商'
            )
        elif main_menu == "产业运营":
            current_nav = current_nav.replace(
                'button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                产业运营',
                'button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                产业运营'
            )
        elif main_menu == "系统管理":
            current_nav = current_nav.replace(
                'button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                系统管理',
                'button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">\n                                系统管理'
            )
    
    # 高亮当前页面的子菜单
    if sub_menu:
        current_nav = current_nav.replace(
            f'<a href="{filename}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{sub_menu}</a>',
            f'<a href="{filename}" class="block px-4 py-2 text-sm text-primary bg-blue-50 font-medium">{sub_menu}</a>'
        )
    
    # 使用正则表达式替换导航菜单部分
    pattern = r'<a href="[^"]*" class="[^"]*">首页</a>.*?</div>\s*</div>\s*</div>'
    replacement = current_nav + '\n                    </div>'
    
    new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    # 写回文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"已更新: {filename}")

def main():
    """主函数"""
    print("开始批量更新导航菜单...")
    
    for page in pages:
        update_navigation(page)
    
    print("导航菜单更新完成！")

if __name__ == "__main__":
    main()
