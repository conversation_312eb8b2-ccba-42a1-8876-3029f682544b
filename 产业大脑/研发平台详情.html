<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业大脑 - 人工智能重点实验室</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .sidebar-item:hover {
            background: linear-gradient(90deg, #3B82F6 0%, #1D4ED8 100%);
        }
        .tab-active {
            border-bottom: 2px solid #3B82F6;
            color: #3B82F6;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .status-running { background: #10B981; }
        .achievement-card {
            transition: all 0.3s ease;
        }
        .achievement-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">产业大脑</h1>
                    </div>
                    <nav class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">产业总览</a>
                        <a href="#" class="text-blue-600 border-b-2 border-blue-600 px-3 py-2 text-sm font-medium">产业资源</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">政策服务</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">数据分析</a>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                        </svg>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">管理员</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex h-screen bg-gray-50">
        <!-- 左侧边栏 -->
        <div class="w-64 bg-white shadow-sm">
            <div class="p-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">产业资源管理</h2>
                <nav class="space-y-2">
                    <a href="研发平台列表.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                        </svg>
                        研发平台
                    </a>
                    <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-white rounded-md transition-colors">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        企业管理
                    </a>
                    <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-white rounded-md transition-colors">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        产业园区
                    </a>
                    <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-white rounded-md transition-colors">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        招商项目
                    </a>
                </nav>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 overflow-auto">
            <!-- 面包屑导航 -->
            <div class="bg-white px-6 py-4 border-b">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <div class="flex items-center">
                                <a href="#" class="text-gray-400 hover:text-gray-500">产业资源管理</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="研发平台列表.html" class="ml-4 text-gray-400 hover:text-gray-500">研发平台</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-900">人工智能重点实验室</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>

            <!-- 平台头部信息 -->
            <div class="bg-white px-6 py-6 border-b">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center space-x-4 mb-4">
                            <h1 class="text-3xl font-bold text-gray-900">人工智能重点实验室</h1>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">国家级</span>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">重点实验室</span>
                                <div class="w-3 h-3 status-running rounded-full" title="正常运营"></div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">156</div>
                                <div class="text-sm text-gray-500">人才数量</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">89</div>
                                <div class="text-sm text-gray-500">专利数量</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">23</div>
                                <div class="text-sm text-gray-500">成果转化</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-orange-600">15</div>
                                <div class="text-sm text-gray-500">在研项目</div>
                            </div>
                        </div>

                        <div class="text-sm text-gray-600">
                            <p><strong>所属机构：</strong>中科院安徽光机所</p>
                            <p><strong>建设时间：</strong>2018年</p>
                            <p><strong>研究领域：</strong>人工智能、机器学习、深度学习</p>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button class="bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            收藏
                        </button>
                        <button class="bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                            分享
                        </button>
                        <button class="bg-blue-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-blue-700">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            编辑
                        </button>
                    </div>
                </div>
            </div>

            <!-- 标签页导航 -->
            <div class="bg-white px-6 border-b">
                <nav class="flex space-x-8">
                    <button onclick="switchTab('overview')" class="tab-active py-4 px-1 text-sm font-medium border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                        平台概况
                    </button>
                    <button onclick="switchTab('talent')" class="py-4 px-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                        人才团队
                    </button>
                    <button onclick="switchTab('research')" class="py-4 px-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                        研究方向
                    </button>
                    <button onclick="switchTab('achievements')" class="py-4 px-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                        科研成果
                    </button>
                    <button onclick="switchTab('cooperation')" class="py-4 px-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                        产业合作
                    </button>
                    <button onclick="switchTab('management')" class="py-4 px-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                        运营管理
                    </button>
                </nav>
            </div>

            <!-- 内容区域 -->
            <div class="p-6">
                <!-- 平台概况 -->
                <div id="overview" class="tab-content">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 平台简介 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">平台简介</h3>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                人工智能重点实验室是国家级科研平台，专注于人工智能前沿技术研究与产业化应用。
                                实验室拥有完善的科研基础设施和高水平的研究团队，在机器学习、深度学习、
                                计算机视觉、自然语言处理等领域取得了重要突破。
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                实验室致力于推动人工智能技术的产业化应用，与多家知名企业建立了深度合作关系，
                                在智能制造、智慧医疗、智能交通等领域形成了一批具有自主知识产权的核心技术。
                            </p>
                        </div>

                        <!-- 组织架构 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">组织架构</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">实验室主任</div>
                                            <div class="text-sm text-gray-500">张教授</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">研究团队</div>
                                            <div class="text-sm text-gray-500">5个研究方向</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">技术支撑部</div>
                                            <div class="text-sm text-gray-500">设备管理与技术服务</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 基础设施 -->
                    <div class="mt-6 bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">基础设施</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600 mb-2">5,000㎡</div>
                                <div class="text-sm text-gray-600">实验室面积</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600 mb-2">2.5亿元</div>
                                <div class="text-sm text-gray-600">设备总值</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600 mb-2">50+</div>
                                <div class="text-sm text-gray-600">大型设备</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 人才团队 -->
                <div id="talent" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 团队概况 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">团队概况</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">总人数</span>
                                    <span class="font-semibold text-gray-900">156人</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">博士</span>
                                    <span class="font-semibold text-gray-900">45人 (29%)</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">硕士</span>
                                    <span class="font-semibold text-gray-900">78人 (50%)</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">本科</span>
                                    <span class="font-semibold text-gray-900">33人 (21%)</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">平均年龄</span>
                                    <span class="font-semibold text-gray-900">35岁</span>
                                </div>
                            </div>
                        </div>

                        <!-- 高层次人才 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">高层次人才</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-red-600 font-bold text-sm">院</span>
                                        </div>
                                        <span class="font-medium text-gray-900">院士</span>
                                    </div>
                                    <span class="text-red-600 font-semibold">2人</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-blue-600 font-bold text-sm">长</span>
                                        </div>
                                        <span class="font-medium text-gray-900">长江学者</span>
                                    </div>
                                    <span class="text-blue-600 font-semibold">5人</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-green-600 font-bold text-sm">杰</span>
                                        </div>
                                        <span class="font-medium text-gray-900">杰青</span>
                                    </div>
                                    <span class="text-green-600 font-semibold">8人</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-purple-600 font-bold text-sm">海</span>
                                        </div>
                                        <span class="font-medium text-gray-900">海外人才</span>
                                    </div>
                                    <span class="text-purple-600 font-semibold">12人</span>
                                </div>
                            </div>
                        </div>

                        <!-- 核心人才 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">核心人才</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-4">
                                    <h4 class="font-semibold text-gray-900">张教授</h4>
                                    <p class="text-sm text-gray-600">实验室主任 | 院士</p>
                                    <p class="text-xs text-gray-500 mt-1">机器学习领域国际知名专家</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-4">
                                    <h4 class="font-semibold text-gray-900">李研究员</h4>
                                    <p class="text-sm text-gray-600">副主任 | 长江学者</p>
                                    <p class="text-xs text-gray-500 mt-1">深度学习算法优化专家</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-4">
                                    <h4 class="font-semibold text-gray-900">王博士</h4>
                                    <p class="text-sm text-gray-600">技术负责人 | 杰青</p>
                                    <p class="text-xs text-gray-500 mt-1">计算机视觉技术专家</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 人才结构图表 -->
                    <div class="mt-6 bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">人才结构分析</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">学历结构</h4>
                                <div id="educationChart" style="height: 300px;"></div>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">年龄结构</h4>
                                <div id="ageChart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 研究方向 -->
                <div id="research" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 主要研究方向 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">主要研究方向</h3>
                            <div class="space-y-4">
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <h4 class="font-semibold text-gray-900 mb-2">机器学习算法</h4>
                                    <p class="text-sm text-gray-600 mb-2">研究深度学习、强化学习等前沿算法</p>
                                    <div class="flex items-center text-xs text-gray-500">
                                        <span class="mr-4">团队：25人</span>
                                        <span class="mr-4">项目：8个</span>
                                        <span>投入：800万元</span>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <h4 class="font-semibold text-gray-900 mb-2">计算机视觉</h4>
                                    <p class="text-sm text-gray-600 mb-2">图像识别、目标检测、场景理解</p>
                                    <div class="flex items-center text-xs text-gray-500">
                                        <span class="mr-4">团队：32人</span>
                                        <span class="mr-4">项目：12个</span>
                                        <span>投入：1200万元</span>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <h4 class="font-semibold text-gray-900 mb-2">自然语言处理</h4>
                                    <p class="text-sm text-gray-600 mb-2">文本理解、机器翻译、对话系统</p>
                                    <div class="flex items-center text-xs text-gray-500">
                                        <span class="mr-4">团队：28人</span>
                                        <span class="mr-4">项目：10个</span>
                                        <span>投入：1000万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 重点攻关项目 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">重点攻关项目</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-4 py-2">
                                    <h4 class="font-semibold text-gray-900">智能制造AI平台</h4>
                                    <p class="text-sm text-gray-600">国家重点研发计划</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500">2022-2025</span>
                                        <span class="text-xs font-medium text-blue-600">2000万元</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                </div>
                                <div class="border-l-4 border-green-500 pl-4 py-2">
                                    <h4 class="font-semibold text-gray-900">医疗影像AI诊断</h4>
                                    <p class="text-sm text-gray-600">省科技重大专项</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500">2023-2026</span>
                                        <span class="text-xs font-medium text-green-600">1500万元</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 40%"></div>
                                    </div>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-4 py-2">
                                    <h4 class="font-semibold text-gray-900">智能交通系统</h4>
                                    <p class="text-sm text-gray-600">企业合作项目</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500">2023-2024</span>
                                        <span class="text-xs font-medium text-purple-600">800万元</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: 80%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 科研成果 -->
                <div id="achievements" class="tab-content hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="achievement-card bg-white rounded-lg shadow p-6 text-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-blue-600 mb-1">89</div>
                            <div class="text-sm text-gray-600">专利总数</div>
                            <div class="text-xs text-gray-500 mt-1">发明专利 67件</div>
                        </div>

                        <div class="achievement-card bg-white rounded-lg shadow p-6 text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-green-600 mb-1">156</div>
                            <div class="text-sm text-gray-600">发表论文</div>
                            <div class="text-xs text-gray-500 mt-1">SCI论文 89篇</div>
                        </div>

                        <div class="achievement-card bg-white rounded-lg shadow p-6 text-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-purple-600 mb-1">12</div>
                            <div class="text-sm text-gray-600">获奖项目</div>
                            <div class="text-xs text-gray-500 mt-1">国家级 3项</div>
                        </div>

                        <div class="achievement-card bg-white rounded-lg shadow p-6 text-center">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-orange-600 mb-1">23</div>
                            <div class="text-sm text-gray-600">成果转化</div>
                            <div class="text-xs text-gray-500 mt-1">转化收入 2.3亿</div>
                        </div>
                    </div>

                    <!-- 成果详情 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 专利成果 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">专利成果</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-900">深度学习优化算法</h4>
                                        <p class="text-sm text-gray-500">发明专利 | ZL202110123456.7</p>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">已授权</span>
                                </div>
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-900">智能图像识别系统</h4>
                                        <p class="text-sm text-gray-500">发明专利 | ZL202110234567.8</p>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">已授权</span>
                                </div>
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-900">自然语言处理方法</h4>
                                        <p class="text-sm text-gray-500">发明专利 | 202210345678.9</p>
                                    </div>
                                    <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">审查中</span>
                                </div>
                            </div>
                        </div>

                        <!-- 论文成果 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">论文成果</h3>
                            <div class="space-y-3">
                                <div class="p-3 border border-gray-200 rounded-lg">
                                    <h4 class="font-medium text-gray-900 mb-1">Deep Learning for Computer Vision</h4>
                                    <p class="text-sm text-gray-500 mb-2">Nature Machine Intelligence | IF: 25.898</p>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-400">2023年</span>
                                        <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">高被引</span>
                                    </div>
                                </div>
                                <div class="p-3 border border-gray-200 rounded-lg">
                                    <h4 class="font-medium text-gray-900 mb-1">AI-Driven Manufacturing Systems</h4>
                                    <p class="text-sm text-gray-500 mb-2">IEEE Transactions on AI | IF: 12.456</p>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-400">2023年</span>
                                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">ESI热点</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 产业合作 -->
                <div id="cooperation" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 合作企业 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">合作企业</h3>
                            <div class="space-y-4">
                                <div class="flex items-center p-4 border border-gray-200 rounded-lg">
                                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=48&h=48&fit=crop&crop=center" alt="华为" class="w-12 h-12 rounded-lg mr-4">
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900">华为技术有限公司</h4>
                                        <p class="text-sm text-gray-500">智能制造AI平台合作</p>
                                        <p class="text-xs text-gray-400">合作金额：2000万元</p>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">进行中</span>
                                </div>
                                <div class="flex items-center p-4 border border-gray-200 rounded-lg">
                                    <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=48&h=48&fit=crop&crop=center" alt="腾讯" class="w-12 h-12 rounded-lg mr-4">
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900">腾讯科技有限公司</h4>
                                        <p class="text-sm text-gray-500">自然语言处理技术研发</p>
                                        <p class="text-xs text-gray-400">合作金额：1500万元</p>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">进行中</span>
                                </div>
                                <div class="flex items-center p-4 border border-gray-200 rounded-lg">
                                    <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=48&h=48&fit=crop&crop=center" alt="阿里巴巴" class="w-12 h-12 rounded-lg mr-4">
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900">阿里巴巴集团</h4>
                                        <p class="text-sm text-gray-500">智能推荐算法优化</p>
                                        <p class="text-xs text-gray-400">合作金额：1200万元</p>
                                    </div>
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">已完成</span>
                                </div>
                            </div>
                        </div>

                        <!-- 技术转移 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">技术转移</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-4 py-3">
                                    <h4 class="font-semibold text-gray-900">智能视觉检测技术</h4>
                                    <p class="text-sm text-gray-600">转让给合肥智能制造有限公司</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500">2023年6月</span>
                                        <span class="text-sm font-medium text-blue-600">800万元</span>
                                    </div>
                                </div>
                                <div class="border-l-4 border-green-500 pl-4 py-3">
                                    <h4 class="font-semibold text-gray-900">语音识别算法</h4>
                                    <p class="text-sm text-gray-600">许可给科大讯飞股份有限公司</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500">2023年3月</span>
                                        <span class="text-sm font-medium text-green-600">600万元</span>
                                    </div>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-4 py-3">
                                    <h4 class="font-semibold text-gray-900">机器学习平台</h4>
                                    <p class="text-sm text-gray-600">转让给安徽人工智能产业园</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500">2022年12月</span>
                                        <span class="text-sm font-medium text-purple-600">1200万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 运营管理 -->
                <div id="management" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 财务状况 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">财务状况</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                                    <span class="text-gray-700">年度预算</span>
                                    <span class="font-semibold text-blue-600">5000万元</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                                    <span class="text-gray-700">实际支出</span>
                                    <span class="font-semibold text-green-600">4200万元</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                                    <span class="text-gray-700">科研经费</span>
                                    <span class="font-semibold text-purple-600">3500万元</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                                    <span class="text-gray-700">设备采购</span>
                                    <span class="font-semibold text-orange-600">700万元</span>
                                </div>
                            </div>
                        </div>

                        <!-- 项目管理 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">项目管理</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <span class="text-gray-700">在研项目</span>
                                        <p class="text-xs text-gray-500">国家级3个，省级5个</p>
                                    </div>
                                    <span class="text-2xl font-bold text-blue-600">15</span>
                                </div>
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <span class="text-gray-700">完成项目</span>
                                        <p class="text-xs text-gray-500">近三年完成项目</p>
                                    </div>
                                    <span class="text-2xl font-bold text-green-600">28</span>
                                </div>
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <span class="text-gray-700">项目成功率</span>
                                        <p class="text-xs text-gray-500">验收通过率</p>
                                    </div>
                                    <span class="text-2xl font-bold text-purple-600">95%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 对外交流 -->
                    <div class="mt-6 bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">对外交流</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center p-4 border border-gray-200 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600 mb-2">12</div>
                                <div class="text-sm text-gray-600">国际合作项目</div>
                            </div>
                            <div class="text-center p-4 border border-gray-200 rounded-lg">
                                <div class="text-2xl font-bold text-green-600 mb-2">45</div>
                                <div class="text-sm text-gray-600">学术交流活动</div>
                            </div>
                            <div class="text-center p-4 border border-gray-200 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600 mb-2">23</div>
                                <div class="text-sm text-gray-600">人员互访</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            // 移除所有标签的激活状态
            const tabs = document.querySelectorAll('nav button');
            tabs.forEach(tab => {
                tab.classList.remove('tab-active');
                tab.classList.add('text-gray-500');
            });

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.remove('hidden');

            // 激活选中的标签
            event.target.classList.add('tab-active');
            event.target.classList.remove('text-gray-500');

            // 如果是人才团队标签，初始化图表
            if (tabName === 'talent') {
                initCharts();
            }
        }

        // 初始化图表
        function initCharts() {
            // 学历结构图表
            const educationChart = echarts.init(document.getElementById('educationChart'));
            const educationOption = {
                tooltip: {
                    trigger: 'item'
                },
                series: [{
                    name: '学历结构',
                    type: 'pie',
                    radius: '70%',
                    data: [
                        { value: 45, name: '博士' },
                        { value: 78, name: '硕士' },
                        { value: 33, name: '本科' }
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            educationChart.setOption(educationOption);

            // 年龄结构图表
            const ageChart = echarts.init(document.getElementById('ageChart'));
            const ageOption = {
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: ['25-30', '31-35', '36-40', '41-45', '46-50', '50+']
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    name: '人数',
                    data: [25, 45, 38, 28, 15, 5],
                    type: 'bar',
                    itemStyle: {
                        color: '#3B82F6'
                    }
                }]
            };
            ageChart.setOption(ageOption);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示第一个标签页
            document.getElementById('overview').classList.remove('hidden');
        });
    </script>
</body>
</html>
