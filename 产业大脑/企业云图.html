<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业云图 | 产业大脑系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        background: '#f8fafc',
                        navbar: '#ffffff'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-navbar shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- 左侧：Logo和主导航 -->
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Logo" class="h-8 w-8 rounded">
                        <span class="ml-2 text-xl font-bold text-gray-900">产业大脑</span>
                    </div>
                    
                    <!-- 主导航菜单 -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="首页.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">首页</a>
                        <div class="relative group">
                            <button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                企业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="企业登记.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业登记</a>
                                    <a href="企业云图.html" class="block px-4 py-2 text-sm text-primary bg-blue-50 font-medium">企业云图</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业链管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业资源管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                项目管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商项目管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">重点项目管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业招商
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业地图</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业档案</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业运营
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业经济看板</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业图谱</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业预警</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业报告</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                系统管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">账号管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">角色管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">日志管理</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：搜索和用户信息 -->
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input type="text" placeholder="搜索..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    
                    <!-- 通知 -->
                    <button class="relative p-2 text-gray-600 hover:text-primary">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                    </button>
                    
                    <!-- 用户头像和信息 -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-primary">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm font-medium">管理员</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">个人设置</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 面包屑导航 -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="#" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        首页
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="#" class="ml-1 text-sm font-medium text-gray-700 hover:text-primary md:ml-2">企业管理</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">企业云图</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- 页面标题和视图切换 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">企业云图</h1>
                <p class="mt-2 text-gray-600">企业数据看板和一企一档管理</p>
            </div>
            <div class="flex items-center space-x-3">
                <!-- 视图切换 -->
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button class="px-4 py-2 text-sm bg-white text-primary rounded-md shadow-sm font-medium">看板视图</button>
                    <button class="px-4 py-2 text-sm text-gray-600 hover:text-gray-900">列表视图</button>
                </div>
                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    导出数据
                </button>
            </div>
        </div>

        <!-- 企业数据看板 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 企业总数 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">企业总数</h3>
                        <p class="text-2xl font-bold text-gray-900">12,456</p>
                        <p class="text-sm text-green-600 flex items-center">
                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            +2.5% 本月
                        </p>
                    </div>
                </div>
            </div>

            <!-- 规上企业 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">规上企业</h3>
                        <p class="text-2xl font-bold text-gray-900">3,245</p>
                        <p class="text-sm text-blue-600">占比 26.1%</p>
                    </div>
                </div>
            </div>

            <!-- 高新技术企业 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">高新技术企业</h3>
                        <p class="text-2xl font-bold text-gray-900">1,856</p>
                        <p class="text-sm text-purple-600">占比 14.9%</p>
                    </div>
                </div>
            </div>

            <!-- 上市企业 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">上市企业</h3>
                        <p class="text-2xl font-bold text-gray-900">89</p>
                        <p class="text-sm text-yellow-600">市值 ¥2,456亿</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产业分布和筛选 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- 产业分布图 -->
            <div class="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">产业分布</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">按数量</button>
                        <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">按产值</button>
                    </div>
                </div>
                <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="产业分布图" class="w-full h-full object-cover rounded-lg">
                </div>
            </div>

            <!-- 产业链企业数量 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">产业链分布</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">新能源汽车</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-900">2,456</div>
                            <div class="text-xs text-gray-500">19.7%</div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">集成电路</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-900">1,892</div>
                            <div class="text-xs text-gray-500">15.2%</div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">人工智能</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-900">1,634</div>
                            <div class="text-xs text-gray-500">13.1%</div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">生物医药</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-900">1,245</div>
                            <div class="text-xs text-gray-500">10.0%</div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">新材料</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-900">987</div>
                            <div class="text-xs text-gray-500">7.9%</div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">其他</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-900">4,242</div>
                            <div class="text-xs text-gray-500">34.1%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 企业筛选和搜索 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">企业名称</label>
                    <div class="relative">
                        <input type="text" placeholder="请输入企业名称" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">产业链</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全部产业链</option>
                        <option>新能源汽车</option>
                        <option>集成电路</option>
                        <option>人工智能</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">企业规模</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全部规模</option>
                        <option>大型企业</option>
                        <option>中型企业</option>
                        <option>小型企业</option>
                        <option>微型企业</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">企业类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全部类型</option>
                        <option>规上企业</option>
                        <option>高新技术企业</option>
                        <option>上市企业</option>
                        <option>专精特新</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 企业卡片展示 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- 企业卡片 1 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="企业Logo" class="w-12 h-12 rounded-lg mr-3">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">合肥智能科技有限公司</h3>
                            <p class="text-sm text-gray-500">91340100MA2N1234XY</p>
                        </div>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">人工智能</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-xs text-gray-500">注册资本</p>
                        <p class="text-sm font-semibold text-gray-900">1000万元</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">成立时间</p>
                        <p class="text-sm font-semibold text-gray-900">2020-03-15</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">员工数量</p>
                        <p class="text-sm font-semibold text-gray-900">156人</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">年营收</p>
                        <p class="text-sm font-semibold text-gray-900">5,600万</p>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex space-x-2">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">高新技术</span>
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">专精特新</span>
                    </div>
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">查看详情</button>
                </div>
            </div>

            <!-- 企业卡片 2 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="企业Logo" class="w-12 h-12 rounded-lg mr-3">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">安徽新能源动力有限公司</h3>
                            <p class="text-sm text-gray-500">91340100MA2N5678AB</p>
                        </div>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">新能源汽车</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-xs text-gray-500">注册资本</p>
                        <p class="text-sm font-semibold text-gray-900">5000万元</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">成立时间</p>
                        <p class="text-sm font-semibold text-gray-900">2019-08-20</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">员工数量</p>
                        <p class="text-sm font-semibold text-gray-900">423人</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">年营收</p>
                        <p class="text-sm font-semibold text-gray-900">2.8亿</p>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex space-x-2">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">规上企业</span>
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">高新技术</span>
                    </div>
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">查看详情</button>
                </div>
            </div>

            <!-- 企业卡片 3 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="企业Logo" class="w-12 h-12 rounded-lg mr-3">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">华东生物医药科技股份有限公司</h3>
                            <p class="text-sm text-gray-500">91340100MA2N9012CD</p>
                        </div>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">生物医药</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-xs text-gray-500">注册资本</p>
                        <p class="text-sm font-semibold text-gray-900">2亿元</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">成立时间</p>
                        <p class="text-sm font-semibold text-gray-900">2018-12-10</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">员工数量</p>
                        <p class="text-sm font-semibold text-gray-900">892人</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">年营收</p>
                        <p class="text-sm font-semibold text-gray-900">12.5亿</p>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex space-x-2">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">上市企业</span>
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">规上企业</span>
                    </div>
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">查看详情</button>
                </div>
            </div>

            <!-- 企业卡片 4 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="企业Logo" class="w-12 h-12 rounded-lg mr-3">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">中科集成电路设计有限公司</h3>
                            <p class="text-sm text-gray-500">91340100MA2N3456EF</p>
                        </div>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">集成电路</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-xs text-gray-500">注册资本</p>
                        <p class="text-sm font-semibold text-gray-900">8000万元</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">成立时间</p>
                        <p class="text-sm font-semibold text-gray-900">2017-05-18</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">员工数量</p>
                        <p class="text-sm font-semibold text-gray-900">267人</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">年营收</p>
                        <p class="text-sm font-semibold text-gray-900">3.2亿</p>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex space-x-2">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">高新技术</span>
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">专精特新</span>
                    </div>
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">查看详情</button>
                </div>
            </div>

            <!-- 企业卡片 5 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="企业Logo" class="w-12 h-12 rounded-lg mr-3">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">安徽新材料科技集团</h3>
                            <p class="text-sm text-gray-500">91340100MA2N7890GH</p>
                        </div>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">新材料</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-xs text-gray-500">注册资本</p>
                        <p class="text-sm font-semibold text-gray-900">1.5亿元</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">成立时间</p>
                        <p class="text-sm font-semibold text-gray-900">2016-11-25</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">员工数量</p>
                        <p class="text-sm font-semibold text-gray-900">634人</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">年营收</p>
                        <p class="text-sm font-semibold text-gray-900">8.9亿</p>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex space-x-2">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">规上企业</span>
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">高新技术</span>
                    </div>
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">查看详情</button>
                </div>
            </div>

            <!-- 企业卡片 6 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="企业Logo" class="w-12 h-12 rounded-lg mr-3">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">智慧制造装备有限公司</h3>
                            <p class="text-sm text-gray-500">91340100MA2N2468IJ</p>
                        </div>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800">智能制造</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-xs text-gray-500">注册资本</p>
                        <p class="text-sm font-semibold text-gray-900">3000万元</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">成立时间</p>
                        <p class="text-sm font-semibold text-gray-900">2021-07-08</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">员工数量</p>
                        <p class="text-sm font-semibold text-gray-900">198人</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">年营收</p>
                        <p class="text-sm font-semibold text-gray-900">1.8亿</p>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex space-x-2">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">高新技术</span>
                        <span class="inline-flex px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">专精特新</span>
                    </div>
                    <button class="text-primary hover:text-blue-700 text-sm font-medium">查看详情</button>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                显示第 <span class="font-medium">1</span> 到 <span class="font-medium">6</span> 条，共 <span class="font-medium">12,456</span> 家企业
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50 disabled:opacity-50" disabled>
                    上一页
                </button>
                <button class="px-3 py-1 text-sm bg-primary text-white rounded-md">1</button>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">2</button>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">3</button>
                <span class="px-3 py-1 text-sm text-gray-500">...</span>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">2076</button>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    下一页
                </button>
            </div>
        </div>
    </main>
</body>
</html>
