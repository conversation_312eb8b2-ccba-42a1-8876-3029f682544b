<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业全景图 | 产业大脑系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        background: '#f8fafc',
                        navbar: '#ffffff'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-navbar shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- 左侧：Logo和主导航 -->
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Logo" class="h-8 w-8 rounded">
                        <span class="ml-2 text-xl font-bold text-gray-900">产业大脑</span>
                    </div>
                    
                    <!-- 主导航菜单 -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="首页.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">首页</a>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                企业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="企业登记.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业登记</a>
                                    <a href="企业云图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业云图</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业链管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业链管理</a>
                                    <a href="产业资源管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业资源管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                项目管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商项目管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商项目管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">重点项目管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业招商
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商企业地图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业地图</a>
                                    <a href="招商企业档案.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业档案</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业运营
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业经济看板.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业经济看板</a>
                                    <a href="产业图谱.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业图谱</a>
                                    <a href="产业全景图.html" class="block px-4 py-2 text-sm text-primary bg-blue-50 font-medium">产业全景图</a>
                                    <a href="产业预警.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业预警</a>
                                    <a href="产业报告.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业报告</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                系统管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="账号管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">账号管理</a>
                                    <a href="角色管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">角色管理</a>
                                    <a href="日志管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">日志管理</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：搜索和用户信息 -->
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input type="text" placeholder="搜索..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    
                    <!-- 通知 -->
                    <button class="relative p-2 text-gray-600 hover:text-primary">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                    </button>
                    
                    <!-- 用户头像和信息 -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-primary">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm font-medium">管理员</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">个人设置</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 面包屑导航 -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="首页.html" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        首页
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="#" class="ml-1 text-sm font-medium text-gray-700 hover:text-primary md:ml-2">产业运营</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">产业全景图</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- 页面标题和控制面板 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">产业全景图看板</h1>
                <p class="mt-2 text-gray-600">全方位展示产业发展现状、趋势分析和空间分布</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    导出报告
                </button>
            </div>
        </div>

        <!-- 筛选控制面板 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">产业类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>新能源汽车</option>
                        <option>智能制造</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>数字经济</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">年份</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>2024年</option>
                        <option>2023年</option>
                        <option>2022年</option>
                        <option>2021年</option>
                        <option>2020年</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">季度</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>第四季度</option>
                        <option>第三季度</option>
                        <option>第二季度</option>
                        <option>第一季度</option>
                        <option>全年</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询分析
                    </button>
                </div>
            </div>
        </div>

        <!-- 产业规模统计 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">产业规模指标</h3>
            </div>

            <!-- 核心指标卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 企业总数 -->
                <div class="p-4 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg border border-indigo-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-indigo-700">企业总数</h4>
                            <p class="text-2xl font-bold text-indigo-900">2,928家</p>
                            <p class="text-sm text-indigo-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +12.3%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-indigo-200">
                            <svg class="h-6 w-6 text-indigo-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 总营收 -->
                <div class="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-blue-700">总营收</h4>
                            <p class="text-2xl font-bold text-blue-900">¥2,856.7亿</p>
                            <p class="text-sm text-blue-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +18.5%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-blue-200">
                            <svg class="h-6 w-6 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 总税收 -->
                <div class="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-green-700">总税收</h4>
                            <p class="text-2xl font-bold text-green-900">¥428.5亿</p>
                            <p class="text-sm text-green-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +22.3%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-green-200">
                            <svg class="h-6 w-6 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 专利总数 -->
                <div class="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-purple-700">专利总数</h4>
                            <p class="text-2xl font-bold text-purple-900">28,456件</p>
                            <p class="text-sm text-purple-600 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +42.1%
                            </p>
                        </div>
                        <div class="p-3 rounded-full bg-purple-200">
                            <svg class="h-6 w-6 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细指标表格 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 核心财务指标 -->
                <div class="space-y-3">
                    <h4 class="text-base font-semibold text-gray-900 mb-4">核心财务指标</h4>
                    <div class="space-y-3">
                        <!-- 营收增加值 - 带提醒图标 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700">营收增加值</span>
                                <div class="relative ml-2">
                                    <svg class="w-4 h-4 text-blue-500 cursor-help" fill="currentColor" viewBox="0 0 20 20"
                                         onmouseenter="showTooltip(event, 'valueAddedTooltip')"
                                         onmouseleave="hideTooltip('valueAddedTooltip')">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                    <!-- 提示气泡 -->
                                    <div id="valueAddedTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 w-80 z-50">
                                        <div class="text-center">
                                            <div class="font-semibold mb-1">营收增加值计算公式</div>
                                            <div class="text-left">营收增加值 = 营收 - 销售费用 - 管理费用 - 财务费用 - 营业成本 - 主营业务税金及附加</div>
                                        </div>
                                        <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥1,142.3亿</span>
                                <span class="text-xs text-green-600 ml-2">+15.7%</span>
                            </div>
                        </div>

                        <!-- 营收增加值率 - 带提醒图标 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700">营收增加值率</span>
                                <div class="relative ml-2">
                                    <svg class="w-4 h-4 text-blue-500 cursor-help" fill="currentColor" viewBox="0 0 20 20"
                                         onmouseenter="showTooltip(event, 'valueAddedRateTooltip')"
                                         onmouseleave="hideTooltip('valueAddedRateTooltip')">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                    <!-- 提示气泡 -->
                                    <div id="valueAddedRateTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 w-80 z-50">
                                        <div class="text-center">
                                            <div class="font-semibold mb-1">营收增加值率计算公式</div>
                                            <div class="text-left">营收增加值率 = (税收 + 利润 + 劳动薪酬 + 折旧) / 营收</div>
                                        </div>
                                        <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">39.98%</span>
                                <span class="text-xs text-green-600 ml-2">+2.1%</span>
                            </div>
                        </div>

                        <!-- 利润总额 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">利润总额</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥285.6亿</span>
                                <span class="text-xs text-green-600 ml-2">+24.8%</span>
                            </div>
                        </div>

                        <!-- 税金总额 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">税金总额</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥428.5亿</span>
                                <span class="text-xs text-green-600 ml-2">+22.3%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 人力与经营指标 -->
                <div class="space-y-3">
                    <h4 class="text-base font-semibold text-gray-900 mb-4">人力与经营指标</h4>
                    <div class="space-y-3">
                        <!-- 社保人员 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">社保人员</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">156.8万人</span>
                                <span class="text-xs text-green-600 ml-2">+8.5%</span>
                            </div>
                        </div>

                        <!-- 薪酬 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">薪酬</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥892.3亿</span>
                                <span class="text-xs text-green-600 ml-2">+14.7%</span>
                            </div>
                        </div>

                        <!-- 研发费用 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">研发费用</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥171.4亿</span>
                                <span class="text-xs text-green-600 ml-2">+31.2%</span>
                            </div>
                        </div>

                        <!-- 折旧 -->
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">折旧</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-900">¥142.8亿</span>
                                <span class="text-xs text-green-600 ml-2">+12.5%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- 合肥市产业地图 - 重点展示 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-xl font-bold text-gray-900">产业地图</h3>
                    <p class="text-sm text-gray-600 mt-1">实时展示各区域产业集群分布与发展态势</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 地图主体区域 -->
                <div class="lg:col-span-2">
                    <div class="h-[600px] bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-6 relative border-2 border-blue-200">
                        <!-- 合肥市区域分布 -->
                        <div class="absolute inset-6">
                            <!-- 地图标题 -->
                            <div class="absolute top-2 left-2 bg-white px-3 py-1 rounded-lg shadow-sm">
                                <span class="text-sm font-semibold text-gray-700">合肥市产业分布图</span>
                            </div>

                            <!-- 瑶海区 -->
                            <div class="absolute top-12 right-12">
                                <div class="relative group cursor-pointer">
                                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl opacity-90 flex items-center justify-center text-white text-sm font-bold shadow-xl transform hover:scale-105 transition-all duration-300">
                                        瑶海区
                                    </div>
                                    <div class="absolute -top-3 -right-3 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                        156
                                    </div>
                                    <div class="absolute top-18 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-gray-700 whitespace-nowrap">
                                        物流配送集群
                                    </div>
                                    <!-- 悬停信息 -->
                                    <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                                        156家企业 | 产值¥89.5亿
                                    </div>
                                </div>
                            </div>

                            <!-- 庐阳区 -->
                            <div class="absolute top-16 left-12">
                                <div class="relative group cursor-pointer">
                                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl opacity-90 flex items-center justify-center text-white text-sm font-bold shadow-xl transform hover:scale-105 transition-all duration-300">
                                        庐阳区
                                    </div>
                                    <div class="absolute -top-3 -right-3 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                        89
                                    </div>
                                    <div class="absolute top-18 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-gray-700 whitespace-nowrap">
                                        研发设计中心
                                    </div>
                                    <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                                        89家企业 | 产值¥156.2亿
                                    </div>
                                </div>
                            </div>

                            <!-- 蜀山区 -->
                            <div class="absolute top-20 left-1/2 transform -translate-x-1/2">
                                <div class="relative group cursor-pointer">
                                    <div class="w-18 h-18 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl opacity-90 flex items-center justify-center text-white text-sm font-bold shadow-xl transform hover:scale-105 transition-all duration-300">
                                        蜀山区
                                    </div>
                                    <div class="absolute -top-3 -right-3 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                        234
                                    </div>
                                    <div class="absolute top-20 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-gray-700 whitespace-nowrap">
                                        智能制造基地
                                    </div>
                                    <div class="absolute bottom-22 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                                        234家企业 | 产值¥298.7亿
                                    </div>
                                </div>
                            </div>

                            <!-- 包河区 -->
                            <div class="absolute bottom-20 right-16">
                                <div class="relative group cursor-pointer">
                                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl opacity-90 flex items-center justify-center text-white text-sm font-bold shadow-xl transform hover:scale-105 transition-all duration-300">
                                        包河区
                                    </div>
                                    <div class="absolute -top-3 -right-3 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                        178
                                    </div>
                                    <div class="absolute top-18 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-gray-700 whitespace-nowrap">
                                        金融服务区
                                    </div>
                                    <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                                        178家企业 | 产值¥234.1亿
                                    </div>
                                </div>
                            </div>

                            <!-- 经开区 - 最重要的区域 -->
                            <div class="absolute bottom-12 left-16">
                                <div class="relative group cursor-pointer">
                                    <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-xl opacity-95 flex items-center justify-center text-white text-sm font-bold shadow-2xl transform hover:scale-105 transition-all duration-300 border-2 border-yellow-400">
                                        经开区
                                    </div>
                                    <div class="absolute -top-4 -right-4 w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-xl animate-pulse">
                                        1.2K
                                    </div>
                                    <div class="absolute top-22 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-3 py-1 rounded-lg shadow text-sm font-semibold whitespace-nowrap">
                                        整车制造核心区
                                    </div>
                                    <div class="absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-3 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                                        1,234家企业 | 产值¥1,456.8亿 | 核心产业集群
                                    </div>
                                </div>
                            </div>

                            <!-- 高新区 -->
                            <div class="absolute bottom-16 right-1/3">
                                <div class="relative group cursor-pointer">
                                    <div class="w-18 h-18 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl opacity-90 flex items-center justify-center text-white text-sm font-bold shadow-xl transform hover:scale-105 transition-all duration-300">
                                        高新区
                                    </div>
                                    <div class="absolute -top-3 -right-3 w-9 h-9 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                        892
                                    </div>
                                    <div class="absolute top-20 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-gray-700 whitespace-nowrap">
                                        电池电控集群
                                    </div>
                                    <div class="absolute bottom-22 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                                        892家企业 | 产值¥1,089.4亿
                                    </div>
                                </div>
                            </div>

                            <!-- 新站区 -->
                            <div class="absolute top-8 right-1/3">
                                <div class="relative group cursor-pointer">
                                    <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl opacity-90 flex items-center justify-center text-white text-sm font-bold shadow-xl transform hover:scale-105 transition-all duration-300">
                                        新站区
                                    </div>
                                    <div class="absolute -top-3 -right-3 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                                        145
                                    </div>
                                    <div class="absolute top-18 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-gray-700 whitespace-nowrap">
                                        零部件制造
                                    </div>
                                    <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                                        145家企业 | 产值¥178.9亿
                                    </div>
                                </div>
                            </div>

                            <!-- 产业连接线 -->
                            <svg class="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 400 300">
                                <defs>
                                    <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0.6" />
                                    </linearGradient>
                                </defs>
                                <path d="M 80 200 Q 200 150 320 200" stroke="url(#connectionGradient)" stroke-width="3" fill="none" stroke-dasharray="8,4" opacity="0.7">
                                    <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" repeatCount="indefinite"/>
                                </path>
                                <path d="M 80 100 Q 200 150 320 100" stroke="url(#connectionGradient)" stroke-width="3" fill="none" stroke-dasharray="8,4" opacity="0.7">
                                    <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" repeatCount="indefinite"/>
                                </path>
                            </svg>

                            <!-- 图例 -->
                            <div class="absolute bottom-2 right-2 bg-white rounded-lg shadow-lg p-3">
                                <div class="text-xs font-semibold text-gray-700 mb-2">图例说明</div>
                                <div class="space-y-1 text-xs">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-red-500 rounded mr-2"></div>
                                        <span>核心制造区</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-indigo-500 rounded mr-2"></div>
                                        <span>技术研发区</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                                        <span>配套服务区</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地图统计信息 -->
                <div class="h-[600px] flex flex-col">
                    <!-- 总体指标 - 顶部 -->
                    <div class="bg-gradient-to-r from-indigo-50 to-indigo-100 p-4 rounded-xl border border-indigo-200 mb-4">
                        <h4 class="text-base font-bold text-indigo-800 mb-4 text-center">总体指标</h4>
                        <div class="grid grid-cols-3 gap-2">
                            <div class="text-center p-2 bg-white rounded-lg shadow-sm">
                                <div class="text-lg font-bold text-indigo-900">2,928家</div>
                                <div class="text-xs text-indigo-600">企业总数</div>
                            </div>
                            <div class="text-center p-2 bg-white rounded-lg shadow-sm">
                                <div class="text-lg font-bold text-indigo-900">¥3,502.7亿</div>
                                <div class="text-xs text-indigo-600">总营收</div>
                            </div>
                            <div class="text-center p-2 bg-white rounded-lg shadow-sm">
                                <div class="text-lg font-bold text-indigo-900">¥525.4亿</div>
                                <div class="text-xs text-indigo-600">总税收</div>
                            </div>
                        </div>
                    </div>

                    <!-- 各区域统计 - 可滚动区域 -->
<div class="flex-1 overflow-y-auto space-y-3 pr-2" style="scrollbar-width: thin; scrollbar-color: #cbd5e1 #f1f5f9;">
                        <style>
                            /* 自定义滚动条样式 */
                            .flex-1::-webkit-scrollbar {
                                width: 6px;
                            }
                            .flex-1::-webkit-scrollbar-track {
                                background: #f1f5f9;
                                border-radius: 3px;
                            }
                            .flex-1::-webkit-scrollbar-thumb {
                                background: #cbd5e1;
                                border-radius: 3px;
                            }
                            .flex-1::-webkit-scrollbar-thumb:hover {
                                background: #94a3b8;
                            }
                        </style>
                        <!-- 经开区 -->
                        <div class="bg-gradient-to-r from-red-50 to-red-100 p-3 rounded-xl border border-red-200">
                            <h5 class="text-sm font-bold text-red-800 mb-2 flex items-center">
                                <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                经开区
                            </h5>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-bold text-red-800">1,234家</div>
                                    <div class="text-xs text-red-600">企业数</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-red-800">¥1,456.8亿</div>
                                    <div class="text-xs text-red-600">营收</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-red-800">¥218.5亿</div>
                                    <div class="text-xs text-red-600">税收</div>
                                </div>
                            </div>
                        </div>

                        <!-- 高新区 -->
                        <div class="bg-gradient-to-r from-indigo-50 to-indigo-100 p-3 rounded-xl border border-indigo-200">
                            <h5 class="text-sm font-bold text-indigo-800 mb-2 flex items-center">
                                <div class="w-2 h-2 bg-indigo-500 rounded-full mr-2"></div>
                                高新区
                            </h5>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-bold text-indigo-800">892家</div>
                                    <div class="text-xs text-indigo-600">企业数</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-indigo-800">¥1,089.4亿</div>
                                    <div class="text-xs text-indigo-600">营收</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-indigo-800">¥163.4亿</div>
                                    <div class="text-xs text-indigo-600">税收</div>
                                </div>
                            </div>
                        </div>

                        <!-- 蜀山区 -->
                        <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-3 rounded-xl border border-purple-200">
                            <h5 class="text-sm font-bold text-purple-800 mb-2 flex items-center">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                                蜀山区
                            </h5>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-bold text-purple-800">234家</div>
                                    <div class="text-xs text-purple-600">企业数</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-purple-800">¥298.7亿</div>
                                    <div class="text-xs text-purple-600">营收</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-purple-800">¥44.8亿</div>
                                    <div class="text-xs text-purple-600">税收</div>
                                </div>
                            </div>
                        </div>

                        <!-- 包河区 -->
                        <div class="bg-gradient-to-r from-orange-50 to-orange-100 p-3 rounded-xl border border-orange-200">
                            <h5 class="text-sm font-bold text-orange-800 mb-2 flex items-center">
                                <div class="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
                                包河区
                            </h5>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-bold text-orange-800">178家</div>
                                    <div class="text-xs text-orange-600">企业数</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-orange-800">¥234.1亿</div>
                                    <div class="text-xs text-orange-600">营收</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-orange-800">¥35.1亿</div>
                                    <div class="text-xs text-orange-600">税收</div>
                                </div>
                            </div>
                        </div>

                        <!-- 瑶海区 -->
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-xl border border-blue-200">
                            <h5 class="text-sm font-bold text-blue-800 mb-2 flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                瑶海区
                            </h5>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-bold text-blue-800">156家</div>
                                    <div class="text-xs text-blue-600">企业数</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-blue-800">¥89.5亿</div>
                                    <div class="text-xs text-blue-600">营收</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-blue-800">¥13.4亿</div>
                                    <div class="text-xs text-blue-600">税收</div>
                                </div>
                            </div>
                        </div>

                        <!-- 庐阳区 -->
                        <div class="bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-xl border border-green-200">
                            <h5 class="text-sm font-bold text-green-800 mb-2 flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                庐阳区
                            </h5>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-bold text-green-800">89家</div>
                                    <div class="text-xs text-green-600">企业数</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-green-800">¥156.2亿</div>
                                    <div class="text-xs text-green-600">营收</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-green-800">¥23.4亿</div>
                                    <div class="text-xs text-green-600">税收</div>
                                </div>
                            </div>
                        </div>

                        <!-- 新站区 -->
                        <div class="bg-gradient-to-r from-teal-50 to-teal-100 p-3 rounded-xl border border-teal-200">
                            <h5 class="text-sm font-bold text-teal-800 mb-2 flex items-center">
                                <div class="w-2 h-2 bg-teal-500 rounded-full mr-2"></div>
                                新站区
                            </h5>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-bold text-teal-800">145家</div>
                                    <div class="text-xs text-teal-600">企业数</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-teal-800">¥178.9亿</div>
                                    <div class="text-xs text-teal-600">营收</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-teal-800">¥26.8亿</div>
                                    <div class="text-xs text-teal-600">税收</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产业发展趋势与产业核心企业 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- 产业发展趋势 -->
            <div class="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">产业发展趋势</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">产值</button>
                        <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">企业数</button>
                        <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">就业</button>
                    </div>
                </div>

                <!-- 趋势图表区域 -->
                <div class="h-80 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-4 relative">
                    <!-- 模拟图表 -->
                    <div class="absolute inset-4">
                        <!-- Y轴标签 -->
                        <div class="absolute left-0 top-0 bottom-0 flex flex-col justify-between text-xs text-gray-500">
                            <span>3.0万亿</span>
                            <span>2.5万亿</span>
                            <span>2.0万亿</span>
                            <span>1.5万亿</span>
                            <span>1.0万亿</span>
                            <span>0.5万亿</span>
                        </div>

                        <!-- 图表区域 -->
                        <div class="ml-12 mr-4 h-full relative">
                            <!-- 网格线 -->
                            <div class="absolute inset-0">
                                <div class="h-full flex flex-col justify-between">
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                    <div class="border-t border-gray-200"></div>
                                </div>
                            </div>

                            <!-- 趋势线 -->
                            <svg class="absolute inset-0 w-full h-full" viewBox="0 0 400 200">
                                <path d="M 20 160 Q 80 140 120 120 T 200 80 T 280 60 T 360 40"
                                      stroke="#3b82f6" stroke-width="3" fill="none" opacity="0.8"/>
                                <path d="M 20 160 Q 80 140 120 120 T 200 80 T 280 60 T 360 40 L 360 200 L 20 200 Z"
                                      fill="url(#gradient)" opacity="0.3"/>
                                <defs>
                                    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.4" />
                                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.1" />
                                    </linearGradient>
                                </defs>

                                <!-- 数据点 -->
                                <circle cx="20" cy="160" r="4" fill="#3b82f6"/>
                                <circle cx="120" cy="120" r="4" fill="#3b82f6"/>
                                <circle cx="200" cy="80" r="4" fill="#3b82f6"/>
                                <circle cx="280" cy="60" r="4" fill="#3b82f6"/>
                                <circle cx="360" cy="40" r="4" fill="#3b82f6"/>
                            </svg>
                        </div>

                        <!-- X轴标签 -->
                        <div class="absolute bottom-0 left-12 right-4 flex justify-between text-xs text-gray-500">
                            <span>2019</span>
                            <span>2020</span>
                            <span>2021</span>
                            <span>2022</span>
                            <span>2023</span>
                        </div>
                    </div>
                </div>

                <!-- 趋势分析 -->
                <div class="mt-4 grid grid-cols-3 gap-4">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-600 font-medium">年均增长率</p>
                        <p class="text-xl font-bold text-blue-800">15.2%</p>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <p class="text-sm text-green-600 font-medium">预测2024年</p>
                        <p class="text-xl font-bold text-green-800">3.2万亿</p>
                    </div>
                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                        <p class="text-sm text-purple-600 font-medium">增长潜力</p>
                        <p class="text-xl font-bold text-purple-800">高</p>
                    </div>
                </div>
            </div>

            <!-- 产业核心企业 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">产业核心企业</h3>
                    <button class="text-sm text-blue-600 hover:text-blue-700 flex items-center" onclick="showCompanyList()">
                        查看全部
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>

                <!-- 企业排行榜 -->
                <div class="space-y-3">
                    <div class="flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200 hover:shadow-md transition-shadow cursor-pointer" onclick="showCompanyDetail('byd')">
                        <div class="flex items-center justify-center w-8 h-8 bg-yellow-500 text-white rounded-full text-sm font-bold mr-3">1</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-sm hover:text-blue-600 transition-colors">比亚迪股份</h4>
                            <p class="text-xs text-gray-600">整车制造</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥3,286亿</p>
                            <p class="text-xs text-green-600">+35.2%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onclick="showCompanyDetail('catl')">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-400 text-white rounded-full text-sm font-bold mr-3">2</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-sm hover:text-blue-600 transition-colors">宁德时代</h4>
                            <p class="text-xs text-gray-600">动力电池</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥3,285亿</p>
                            <p class="text-xs text-green-600">+43.7%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200 hover:shadow-md transition-shadow cursor-pointer" onclick="showCompanyDetail('tesla')">
                        <div class="flex items-center justify-center w-8 h-8 bg-orange-500 text-white rounded-full text-sm font-bold mr-3">3</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-sm hover:text-blue-600 transition-colors">特斯拉</h4>
                            <p class="text-xs text-gray-600">智能电动</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥1,810亿</p>
                            <p class="text-xs text-green-600">+28.9%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer" onclick="showCompanyDetail('nio')">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-300 text-white rounded-full text-sm font-bold mr-3">4</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-sm hover:text-blue-600 transition-colors">蔚来汽车</h4>
                            <p class="text-xs text-gray-600">智能电动</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥492亿</p>
                            <p class="text-xs text-green-600">+36.1%</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer" onclick="showCompanyDetail('xpeng')">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-300 text-white rounded-full text-sm font-bold mr-3">5</div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-sm hover:text-blue-600 transition-colors">小鹏汽车</h4>
                            <p class="text-xs text-gray-600">智能电动</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900">¥307亿</p>
                            <p class="text-xs text-green-600">+23.4%</p>
                        </div>
                    </div>
                </div>



                <!-- 企业类型分布 -->
                <div class="mt-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
                    <h4 class="text-sm font-semibold text-purple-800 mb-3">企业类型分布</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-purple-700">规上企业</span>
                            <div class="flex items-center">
                                <div class="w-16 h-1.5 bg-purple-200 rounded-full mr-2">
                                    <div class="w-12 h-1.5 bg-purple-500 rounded-full"></div>
                                </div>
                                <span class="text-xs font-medium text-purple-800">456家</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-purple-700">高新技术企业</span>
                            <div class="flex items-center">
                                <div class="w-16 h-1.5 bg-purple-200 rounded-full mr-2">
                                    <div class="w-10 h-1.5 bg-purple-500 rounded-full"></div>
                                </div>
                                <span class="text-xs font-medium text-purple-800">892家</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-purple-700">专精特新企业</span>
                            <div class="flex items-center">
                                <div class="w-16 h-1.5 bg-purple-200 rounded-full mr-2">
                                    <div class="w-8 h-1.5 bg-purple-500 rounded-full"></div>
                                </div>
                                <span class="text-xs font-medium text-purple-800">234家</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-purple-700">科技型中小企业</span>
                            <div class="flex items-center">
                                <div class="w-16 h-1.5 bg-purple-200 rounded-full mr-2">
                                    <div class="w-14 h-1.5 bg-purple-500 rounded-full"></div>
                                </div>
                                <span class="text-xs font-medium text-purple-800">1,346家</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产业链分布 - 全宽展示 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-xl font-bold text-gray-900">产业链分布结构</h3>
                    <p class="text-sm text-gray-600 mt-1">全面展示新能源汽车产业链各环节企业分布与健康度评估</p>
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 text-sm bg-green-100 text-green-600 rounded-lg font-medium">上游</button>
                    <button class="px-4 py-2 text-sm bg-blue-100 text-blue-600 rounded-lg font-medium">中游</button>
                    <button class="px-4 py-2 text-sm bg-purple-100 text-purple-600 rounded-lg font-medium">下游</button>
                </div>
            </div>

            <!-- 产业链环节 - 网格布局 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- 上游 -->
                <div class="p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-5 h-5 bg-green-500 rounded-full mr-3"></div>
                        <h4 class="text-lg font-bold text-gray-900">上游</h4>
                    </div>
                    <div class="text-center mb-4">
                        <p class="text-3xl font-bold text-green-600">3,456家</p>
                        <p class="text-sm text-gray-600">占比 27.8%</p>
                    </div>
                    <div class="text-sm text-green-700 mb-4">
                        动力电池、电机电控、关键材料、车规芯片等核心零部件供应
                    </div>
                    <div class="w-full h-3 bg-green-200 rounded-full">
                        <div class="w-7/12 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div class="mt-3 text-xs text-green-600">
                        代表企业：宁德时代、比亚迪、汇川技术
                    </div>
                </div>

                <!-- 中游 -->
                <div class="p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-5 h-5 bg-blue-500 rounded-full mr-3"></div>
                        <h4 class="text-lg font-bold text-gray-900">中游</h4>
                    </div>
                    <div class="text-center mb-4">
                        <p class="text-3xl font-bold text-blue-600">2,890家</p>
                        <p class="text-sm text-gray-600">占比 23.2%</p>
                    </div>
                    <div class="text-sm text-blue-700 mb-4">
                        整车制造、系统集成、智能网联、自动驾驶等核心技术研发制造
                    </div>
                    <div class="w-full h-3 bg-blue-200 rounded-full">
                        <div class="w-6/12 h-3 bg-blue-500 rounded-full"></div>
                    </div>
                    <div class="mt-3 text-xs text-blue-600">
                        代表企业：比亚迪、特斯拉、蔚来汽车
                    </div>
                </div>

                <!-- 下游 -->
                <div class="p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border border-purple-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-5 h-5 bg-purple-500 rounded-full mr-3"></div>
                        <h4 class="text-lg font-bold text-gray-900">下游</h4>
                    </div>
                    <div class="text-center mb-4">
                        <p class="text-3xl font-bold text-purple-600">6,110家</p>
                        <p class="text-sm text-gray-600">占比 49.0%</p>
                    </div>
                    <div class="text-sm text-purple-700 mb-4">
                        充电设施、运营服务、销售渠道、后市场服务、金融服务等应用端服务
                    </div>
                    <div class="w-full h-3 bg-purple-200 rounded-full">
                        <div class="w-10/12 h-3 bg-purple-500 rounded-full"></div>
                    </div>
                    <div class="mt-3 text-xs text-purple-600">
                        代表企业：特来电、星星充电、滴滴出行
                    </div>
                </div>
            </div>

            <!-- 产业链细分结构 -->
            <div class="p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
                <h4 class="text-lg font-bold text-gray-900 mb-6 text-center">产业链细分结构</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 上游细分 -->
                    <div class="bg-white rounded-lg p-4 border border-green-200">
                        <div class="flex items-center mb-4">
                            <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                            <h5 class="text-lg font-semibold text-green-800">上游细分</h5>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                                <span class="text-sm text-green-700">动力电池</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-green-800">1,234家</span>
                                    <div class="text-xs text-green-600">35.7%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                                <span class="text-sm text-green-700">电机电控</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-green-800">892家</span>
                                    <div class="text-xs text-green-600">25.8%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                                <span class="text-sm text-green-700">关键材料</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-green-800">756家</span>
                                    <div class="text-xs text-green-600">21.9%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                                <span class="text-sm text-green-700">车规芯片</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-green-800">574家</span>
                                    <div class="text-xs text-green-600">16.6%</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 p-2 bg-green-100 rounded">
                            <p class="text-xs text-green-700 font-medium">代表企业</p>
                            <p class="text-xs text-green-600">宁德时代、比亚迪、汇川技术</p>
                        </div>
                    </div>

                    <!-- 中游细分 -->
                    <div class="bg-white rounded-lg p-4 border border-blue-200">
                        <div class="flex items-center mb-4">
                            <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                            <h5 class="text-lg font-semibold text-blue-800">中游细分</h5>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                                <span class="text-sm text-blue-700">整车制造</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-blue-800">1,456家</span>
                                    <div class="text-xs text-blue-600">50.4%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                                <span class="text-sm text-blue-700">系统集成</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-blue-800">678家</span>
                                    <div class="text-xs text-blue-600">23.5%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                                <span class="text-sm text-blue-700">智能网联</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-blue-800">456家</span>
                                    <div class="text-xs text-blue-600">15.8%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                                <span class="text-sm text-blue-700">自动驾驶</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-blue-800">300家</span>
                                    <div class="text-xs text-blue-600">10.4%</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 p-2 bg-blue-100 rounded">
                            <p class="text-xs text-blue-700 font-medium">代表企业</p>
                            <p class="text-xs text-blue-600">比亚迪、特斯拉、蔚来汽车</p>
                        </div>
                    </div>

                    <!-- 下游细分 -->
                    <div class="bg-white rounded-lg p-4 border border-purple-200">
                        <div class="flex items-center mb-4">
                            <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                            <h5 class="text-lg font-semibold text-purple-800">下游细分</h5>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 bg-purple-50 rounded">
                                <span class="text-sm text-purple-700">充电设施</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-purple-800">2,344家</span>
                                    <div class="text-xs text-purple-600">38.4%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-purple-50 rounded">
                                <span class="text-sm text-purple-700">运营服务</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-purple-800">1,567家</span>
                                    <div class="text-xs text-purple-600">25.6%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-purple-50 rounded">
                                <span class="text-sm text-purple-700">销售渠道</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-purple-800">1,234家</span>
                                    <div class="text-xs text-purple-600">20.2%</div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-purple-50 rounded">
                                <span class="text-sm text-purple-700">金融服务</span>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-purple-800">965家</span>
                                    <div class="text-xs text-purple-600">15.8%</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 p-2 bg-purple-100 rounded">
                            <p class="text-xs text-purple-700 font-medium">代表企业</p>
                            <p class="text-xs text-purple-600">特来电、星星充电、滴滴出行</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产业发展总结分析 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">产业发展总结分析</h3>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 发展现状 -->
                <div class="p-4 bg-blue-50 rounded-lg">
                    <h4 class="text-base font-semibold text-blue-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        发展现状
                    </h4>
                    <ul class="text-sm text-blue-800 space-y-2">
                        <li>• 产业规模持续扩大，总产值达2.8万亿元</li>
                        <li>• 企业数量快速增长，已超过1.2万家</li>
                        <li>• 从业人员规模庞大，提供156.8万个就业岗位</li>
                        <li>• 产业链条日趋完善，覆盖上下游各环节</li>
                        <li>• 区域集聚效应明显，形成多个产业集群</li>
                    </ul>
                </div>

                <!-- 发展优势 -->
                <div class="p-4 bg-green-50 rounded-lg">
                    <h4 class="text-base font-semibold text-green-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        发展优势
                    </h4>
                    <ul class="text-sm text-green-800 space-y-2">
                        <li>• 技术创新能力强，创新指数达85.6分</li>
                        <li>• 龙头企业实力雄厚，引领产业发展</li>
                        <li>• 政策支持力度大，营商环境优良</li>
                        <li>• 产业生态完整，协同效应显著</li>
                        <li>• 市场需求旺盛，发展前景广阔</li>
                    </ul>
                </div>

                <!-- 发展建议 -->
                <div class="p-4 bg-purple-50 rounded-lg">
                    <h4 class="text-base font-semibold text-purple-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        发展建议
                    </h4>
                    <ul class="text-sm text-purple-800 space-y-2">
                        <li>• 加强关键技术攻关，提升核心竞争力</li>
                        <li>• 完善产业链配套，增强供应链韧性</li>
                        <li>• 培育更多独角兽企业，壮大产业规模</li>
                        <li>• 优化区域布局，促进协调发展</li>
                        <li>• 深化国际合作，拓展海外市场</li>
                    </ul>
                </div>
            </div>

            <!-- 关键指标总览 -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-base font-semibold text-gray-900 mb-4">关键指标总览</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-600">2.8万亿</p>
                        <p class="text-sm text-gray-600">产业总产值</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-600">12,456家</p>
                        <p class="text-sm text-gray-600">企业总数</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600">156.8万</p>
                        <p class="text-sm text-gray-600">从业人员</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-orange-600">85.6分</p>
                        <p class="text-sm text-gray-600">创新指数</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 企业列表右侧抽屉 -->
    <div id="companyListDrawer" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute inset-y-0 right-0 w-full max-w-4xl bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out" id="listDrawerContent">
            <!-- 抽屉头部 -->
            <div class="flex justify-between items-center p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">产业核心企业列表</h2>
                    <p class="text-sm text-gray-600 mt-1">新能源汽车产业重点企业详细信息</p>
                </div>
                <button onclick="closeCompanyList()" class="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 抽屉内容 -->
            <div class="h-full overflow-y-auto">
                <div class="p-6">
                    <!-- 统计概览 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <p class="text-2xl font-bold text-blue-600">2,928</p>
                            <p class="text-sm text-blue-700">企业总数</p>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                            <p class="text-2xl font-bold text-green-600">¥3,502.7亿</p>
                            <p class="text-sm text-green-700">总营收</p>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                            <p class="text-2xl font-bold text-purple-600">¥525.4亿</p>
                            <p class="text-sm text-purple-700">总税收</p>
                        </div>
                        <div class="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                            <p class="text-2xl font-bold text-orange-600">156.8万</p>
                            <p class="text-sm text-orange-700">从业人员</p>
                        </div>
                    </div>

                    <!-- 企业列表 -->
                    <div class="space-y-4" id="companyListContainer">
                        <!-- 企业卡片将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 企业详情右侧抽屉 -->
    <div id="companyDrawer" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute inset-y-0 right-0 w-full max-w-2xl bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out" id="drawerContent">
            <!-- 抽屉头部 -->
            <div class="flex justify-between items-center p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div class="flex items-center">
                    <div id="companyLogo" class="w-14 h-14 bg-blue-500 rounded-xl flex items-center justify-center text-white font-bold text-xl mr-4 shadow-lg">
                        比
                    </div>
                    <div>
                        <h2 id="companyName" class="text-2xl font-bold text-gray-900">比亚迪股份有限公司</h2>
                        <p id="companyType" class="text-sm text-gray-600 mt-1">新能源汽车整车制造</p>
                        <div class="flex items-center mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                上市公司
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                                独角兽企业
                            </span>
                        </div>
                    </div>
                </div>
                <button onclick="closeCompanyDetail()" class="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 抽屉内容 -->
            <div class="h-full overflow-y-auto">
                <div class="p-6 space-y-8">
                    <!-- 基本工商信息 -->
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            基本工商信息
                        </h3>
                        <div class="grid grid-cols-1 gap-4">
                            <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600 mb-1">统一社会信用代码</p>
                                        <p id="companyCreditCode" class="text-base font-semibold text-gray-900">91440300279463209A</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 mb-1">成立时间</p>
                                        <p id="companyEstablishDate" class="text-base font-semibold text-gray-900">1995年02月10日</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 mb-1">注册资本</p>
                                        <p id="companyCapital" class="text-base font-semibold text-gray-900">290.42亿元</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 mb-1">法定代表人</p>
                                        <p id="companyLegalPerson" class="text-base font-semibold text-gray-900">王传福</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 mb-1">企业状态</p>
                                        <p id="companyStatus" class="text-base font-semibold text-green-600">存续（在营、开业、在册）</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600 mb-1">企业类型</p>
                                        <p id="companyBusinessType" class="text-base font-semibold text-gray-900">股份有限公司(上市)</p>
                                    </div>
                                    <div class="col-span-2">
                                        <p class="text-sm text-gray-600 mb-1">注册地址</p>
                                        <p id="companyAddress" class="text-base font-semibold text-gray-900">深圳市坪山区比亚迪路3009号</p>
                                    </div>
                                    <div class="col-span-2">
                                        <p class="text-sm text-gray-600 mb-1">经营范围</p>
                                        <p id="companyScope" class="text-sm text-gray-700 leading-relaxed">新能源汽车及传统燃油汽车在内的汽车业务、手机部件及组装业务、二次充电电池及光伏业务，并延伸至城市轨道交通业务领域。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 近3年财务数据 -->
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                            </svg>
                            近3年财务数据
                        </h3>

                        <!-- 年度数据切换 -->
                        <div class="flex space-x-1 mb-4">
                            <button class="px-4 py-2 text-sm bg-blue-500 text-white rounded-lg font-medium" onclick="switchYear(2023)">2023年</button>
                            <button class="px-4 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" onclick="switchYear(2022)">2022年</button>
                            <button class="px-4 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300" onclick="switchYear(2021)">2021年</button>
                        </div>

                        <!-- 财务指标卡片 -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="text-sm text-blue-600 font-medium">营业收入</p>
                                    <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                                    </svg>
                                </div>
                                <p id="yearRevenue" class="text-2xl font-bold text-blue-800">¥3,286亿</p>
                                <p id="revenueGrowth" class="text-sm text-green-600">+35.2% 同比</p>
                            </div>

                            <div class="p-4 bg-green-50 rounded-lg border border-green-200">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="text-sm text-green-600 font-medium">纳税总额</p>
                                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
                                    </svg>
                                </div>
                                <p id="yearTax" class="text-2xl font-bold text-green-800">¥156.8亿</p>
                                <p id="taxGrowth" class="text-sm text-green-600">+28.5% 同比</p>
                            </div>

                            <div class="p-4 bg-purple-50 rounded-lg border border-purple-200">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="text-sm text-purple-600 font-medium">社保人员</p>
                                    <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                    </svg>
                                </div>
                                <p id="yearEmployees" class="text-2xl font-bold text-purple-800">70.3万人</p>
                                <p id="employeesGrowth" class="text-sm text-purple-600">+15.2% 同比</p>
                            </div>

                            <div class="p-4 bg-orange-50 rounded-lg border border-orange-200">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="text-sm text-orange-600 font-medium">研发投入</p>
                                    <svg class="w-4 h-4 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <p id="yearRD" class="text-2xl font-bold text-orange-800">¥218.5亿</p>
                                <p id="rdGrowth" class="text-sm text-orange-600">+42.1% 同比</p>
                            </div>
                        </div>

                        <!-- 3年财务数据图表 -->
                        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <h4 class="text-base font-semibold text-gray-900 mb-4">近3年财务数据趋势</h4>

                            <!-- 图表容器 -->
                            <div class="space-y-6">
                                <!-- 营收趋势图 -->
                                <div>
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="text-sm font-medium text-gray-700">营业收入（亿元）</h5>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-3 h-3 bg-blue-500 rounded"></div>
                                            <span class="text-xs text-gray-600">营收</span>
                                        </div>
                                    </div>
                                    <div class="h-20 flex items-end justify-between space-x-4">
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="revenue2021Bar" class="w-full bg-blue-300 rounded-t transition-all duration-500" style="height: 40%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2021</p>
                                            <p id="revenue2021Text" class="text-xs font-medium text-gray-900">2,161</p>
                                        </div>
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="revenue2022Bar" class="w-full bg-blue-400 rounded-t transition-all duration-500" style="height: 70%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2022</p>
                                            <p id="revenue2022Text" class="text-xs font-medium text-gray-900">4,240</p>
                                        </div>
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="revenue2023Bar" class="w-full bg-blue-600 rounded-t transition-all duration-500" style="height: 100%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2023</p>
                                            <p id="revenue2023Text" class="text-xs font-medium text-gray-900">6,023</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 税收趋势图 -->
                                <div>
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="text-sm font-medium text-gray-700">纳税总额（亿元）</h5>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-3 h-3 bg-green-500 rounded"></div>
                                            <span class="text-xs text-gray-600">税收</span>
                                        </div>
                                    </div>
                                    <div class="h-20 flex items-end justify-between space-x-4">
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="tax2021Bar" class="w-full bg-green-300 rounded-t transition-all duration-500" style="height: 50%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2021</p>
                                            <p id="tax2021Text" class="text-xs font-medium text-gray-900">108.5</p>
                                        </div>
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="tax2022Bar" class="w-full bg-green-400 rounded-t transition-all duration-500" style="height: 72%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2022</p>
                                            <p id="tax2022Text" class="text-xs font-medium text-gray-900">158.2</p>
                                        </div>
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="tax2023Bar" class="w-full bg-green-600 rounded-t transition-all duration-500" style="height: 100%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2023</p>
                                            <p id="tax2023Text" class="text-xs font-medium text-gray-900">218.5</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 社保人员趋势图 -->
                                <div>
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="text-sm font-medium text-gray-700">社保人员（万人）</h5>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-3 h-3 bg-purple-500 rounded"></div>
                                            <span class="text-xs text-gray-600">人员</span>
                                        </div>
                                    </div>
                                    <div class="h-20 flex items-end justify-between space-x-4">
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="employee2021Bar" class="w-full bg-purple-300 rounded-t transition-all duration-500" style="height: 68%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2021</p>
                                            <p id="employee2021Text" class="text-xs font-medium text-gray-900">47.5</p>
                                        </div>
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="employee2022Bar" class="w-full bg-purple-400 rounded-t transition-all duration-500" style="height: 87%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2022</p>
                                            <p id="employee2022Text" class="text-xs font-medium text-gray-900">61.0</p>
                                        </div>
                                        <div class="flex-1 flex flex-col items-center">
                                            <div id="employee2023Bar" class="w-full bg-purple-600 rounded-t transition-all duration-500" style="height: 100%"></div>
                                            <p class="text-xs text-gray-600 mt-1">2023</p>
                                            <p id="employee2023Text" class="text-xs font-medium text-gray-900">70.3</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 企业创新能力 -->
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            企业创新能力（软著与专利）
                        </h3>

                        <!-- 创新能力图表 -->
                        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200 mb-6">
                            <h4 class="text-base font-semibold text-gray-900 mb-4">近3年软著与专利数量趋势</h4>

                            <!-- 软著数量图表 -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-2">
                                    <h5 class="text-sm font-medium text-gray-700">软件著作权（件）</h5>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-indigo-500 rounded"></div>
                                        <span class="text-xs text-gray-600">软著</span>
                                    </div>
                                </div>
                                <div class="h-24 flex items-end justify-between space-x-4">
                                    <div class="flex-1 flex flex-col items-center">
                                        <div id="copyright2021Bar" class="w-full bg-indigo-300 rounded-t transition-all duration-500" style="height: 60%"></div>
                                        <p class="text-xs text-gray-600 mt-1">2021</p>
                                        <p id="copyright2021Text" class="text-xs font-medium text-gray-900">1,245</p>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div id="copyright2022Bar" class="w-full bg-indigo-400 rounded-t transition-all duration-500" style="height: 80%"></div>
                                        <p class="text-xs text-gray-600 mt-1">2022</p>
                                        <p id="copyright2022Text" class="text-xs font-medium text-gray-900">1,658</p>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div id="copyright2023Bar" class="w-full bg-indigo-600 rounded-t transition-all duration-500" style="height: 100%"></div>
                                        <p class="text-xs text-gray-600 mt-1">2023</p>
                                        <p id="copyright2023Text" class="text-xs font-medium text-gray-900">2,089</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 专利数量图表 -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-2">
                                    <h5 class="text-sm font-medium text-gray-700">专利数量（件）</h5>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-orange-500 rounded"></div>
                                        <span class="text-xs text-gray-600">专利</span>
                                    </div>
                                </div>
                                <div class="h-24 flex items-end justify-between space-x-4">
                                    <div class="flex-1 flex flex-col items-center">
                                        <div id="patent2021Bar" class="w-full bg-orange-300 rounded-t transition-all duration-500" style="height: 55%"></div>
                                        <p class="text-xs text-gray-600 mt-1">2021</p>
                                        <p id="patent2021Text" class="text-xs font-medium text-gray-900">2,834</p>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div id="patent2022Bar" class="w-full bg-orange-400 rounded-t transition-all duration-500" style="height: 75%"></div>
                                        <p class="text-xs text-gray-600 mt-1">2022</p>
                                        <p id="patent2022Text" class="text-xs font-medium text-gray-900">3,645</p>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div id="patent2023Bar" class="w-full bg-orange-600 rounded-t transition-all duration-500" style="height: 100%"></div>
                                        <p class="text-xs text-gray-600 mt-1">2023</p>
                                        <p id="patent2023Text" class="text-xs font-medium text-gray-900">4,856</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 专利类型分布饼图 -->
                            <div>
                                <h5 class="text-sm font-medium text-gray-700 mb-3">2023年专利类型分布</h5>
                                <div class="flex items-center justify-center">
                                    <!-- 饼图容器 -->
                                    <div class="relative w-32 h-32">
                                        <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
                                            <!-- 发明专利 58% -->
                                            <circle cx="50" cy="50" r="40" stroke="#ef4444" stroke-width="20" fill="none"
                                                    stroke-dasharray="145.6 251.2" stroke-dashoffset="0"/>
                                            <!-- 实用新型 32% -->
                                            <circle cx="50" cy="50" r="40" stroke="#f97316" stroke-width="20" fill="none"
                                                    stroke-dasharray="80.4 251.2" stroke-dashoffset="-145.6"/>
                                            <!-- 外观设计 10% -->
                                            <circle cx="50" cy="50" r="40" stroke="#eab308" stroke-width="20" fill="none"
                                                    stroke-dasharray="25.1 251.2" stroke-dashoffset="-226"/>
                                        </svg>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <div class="text-center">
                                                <p class="text-lg font-bold text-gray-900" id="totalPatents">4,856</p>
                                                <p class="text-xs text-gray-600">总专利</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 图例 -->
                                    <div class="ml-6 space-y-2">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-red-500 rounded mr-2"></div>
                                            <span class="text-sm text-gray-700">发明专利</span>
                                            <span class="text-sm font-medium text-gray-900 ml-2" id="inventionPatents">2,816件 (58%)</span>
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-orange-500 rounded mr-2"></div>
                                            <span class="text-sm text-gray-700">实用新型</span>
                                            <span class="text-sm font-medium text-gray-900 ml-2" id="utilityPatents">1,554件 (32%)</span>
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                                            <span class="text-sm text-gray-700">外观设计</span>
                                            <span class="text-sm font-medium text-gray-900 ml-2" id="designPatents">486件 (10%)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 创新能力总结 -->
                        <div class="p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg border border-purple-200">
                            <h4 class="text-base font-semibold text-gray-900 mb-3">创新能力总结</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="text-center p-3 bg-white rounded-lg">
                                    <p class="text-2xl font-bold text-indigo-600" id="totalCopyrights">2,089</p>
                                    <p class="text-sm text-gray-600">软件著作权总数</p>
                                    <p class="text-xs text-green-600">+26.0% 年增长</p>
                                </div>
                                <div class="text-center p-3 bg-white rounded-lg">
                                    <p class="text-2xl font-bold text-orange-600" id="totalPatentsSum">4,856</p>
                                    <p class="text-sm text-gray-600">专利总数</p>
                                    <p class="text-xs text-green-600">+33.2% 年增长</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>

    <script>
        // 企业详细信息数据
        const companyData = {
            'byd': {
                name: '比亚迪股份有限公司',
                logo: '比',
                type: '新能源汽车整车制造',
                creditCode: '91440300279463209A',
                establishDate: '1995年02月10日',
                capital: '290.42亿元',
                legalPerson: '王传福',
                address: '深圳市坪山区比亚迪路3009号',
                status: '存续（在营、开业、在册）',
                businessType: '股份有限公司(上市)',
                scope: '新能源汽车及传统燃油汽车在内的汽车业务、手机部件及组装业务、二次充电电池及光伏业务，并延伸至城市轨道交通业务领域。主要产品为新能源汽车、传统燃油汽车、动力电池、太阳能电池、储能电池、电池材料、电子产品等。',
                financialData: {
                    2023: { revenue: '¥6,023亿', tax: '¥218.5亿', employees: '70.3万人', rd: '¥218.5亿', revenueGrowth: '+42.1%', taxGrowth: '+38.2%', employeesGrowth: '+15.2%', rdGrowth: '+42.1%' },
                    2022: { revenue: '¥4,240亿', tax: '¥158.2亿', employees: '61.0万人', rd: '¥154.0亿', revenueGrowth: '+96.2%', taxGrowth: '+45.8%', employeesGrowth: '+28.5%', rdGrowth: '+89.3%' },
                    2021: { revenue: '¥2,161亿', tax: '¥108.5亿', employees: '47.5万人', rd: '¥81.3亿', revenueGrowth: '+38.0%', taxGrowth: '+25.6%', employeesGrowth: '+18.2%', rdGrowth: '+24.1%' }
                },
                innovation: {
                    copyrights: {
                        2021: 1245,
                        2022: 1658,
                        2023: 2089
                    },
                    patents: {
                        2021: 2834,
                        2022: 3645,
                        2023: 4856
                    },
                    patentTypes: {
                        invention: 2816,
                        utility: 1554,
                        design: 486
                    }
                }
            },
            'catl': {
                name: '宁德时代新能源科技股份有限公司',
                logo: '宁',
                type: '动力电池系统制造',
                creditCode: '91350900MA2XN2KQ15',
                establishDate: '2011年12月16日',
                capital: '50.31亿元',
                legalPerson: '曾毓群',
                address: '福建省宁德市蕉城区漳湾镇新港路2号',
                status: '存续（在营、开业、在册）',
                businessType: '股份有限公司(上市)',
                scope: '锂离子电池、动力电池、储能电池、电池系统、电池材料的研发、生产和销售，以及电池回收等业务。',
                financialData: {
                    2023: { revenue: '¥3,285亿', tax: '¥142.6亿', employees: '11.8万人', rd: '¥155.1亿', revenueGrowth: '+22.8%', taxGrowth: '+18.5%', employeesGrowth: '+12.3%', rdGrowth: '+35.2%' },
                    2022: { revenue: '¥2,675亿', tax: '¥120.3亿', employees: '10.5万人', rd: '¥114.7亿', revenueGrowth: '+152.1%', taxGrowth: '+89.2%', employeesGrowth: '+45.8%', rdGrowth: '+102.5%' },
                    2021: { revenue: '¥1,061亿', tax: '¥63.6亿', employees: '7.2万人', rd: '¥56.6亿', revenueGrowth: '+159.1%', taxGrowth: '+125.8%', employeesGrowth: '+78.5%', rdGrowth: '+115.3%' }
                },
                innovation: {
                    copyrights: {
                        2021: 856,
                        2022: 1234,
                        2023: 1678
                    },
                    patents: {
                        2021: 2156,
                        2022: 2834,
                        2023: 3892
                    },
                    patentTypes: {
                        invention: 2245,
                        utility: 1245,
                        design: 402
                    }
                }
            },
            'tesla': {
                name: '特斯拉（上海）有限公司',
                logo: 'T',
                type: '智能电动汽车制造',
                creditCode: '91310115MA1FL1QU7E',
                establishDate: '2018年05月10日',
                capital: '46.7亿元',
                legalPerson: '朱晓彤',
                address: '上海市浦东新区南汇新城镇同汇路168号',
                status: '存续（在营、开业、在册）',
                businessType: '有限责任公司(外国法人独资)',
                scope: '电动汽车及零部件、电池、储能设备、光伏产品的生产、销售，以及相关技术的研发。',
                financialData: {
                    2023: { revenue: '¥1,810亿', tax: '¥89.5亿', employees: '2.1万人', rd: '¥98.2亿', revenueGrowth: '+15.8%', taxGrowth: '+22.1%', employeesGrowth: '+8.5%', rdGrowth: '+28.3%' },
                    2022: { revenue: '¥1,563亿', tax: '¥73.3亿', employees: '1.9万人', rd: '¥76.5亿', revenueGrowth: '+87.5%', taxGrowth: '+95.2%', employeesGrowth: '+52.0%', rdGrowth: '+89.1%' },
                    2021: { revenue: '¥833亿', tax: '¥37.6亿', employees: '1.3万人', rd: '¥40.5亿', revenueGrowth: '+125.8%', taxGrowth: '+118.5%', employeesGrowth: '+85.7%', rdGrowth: '+95.2%' }
                },
                innovation: {
                    copyrights: {
                        2021: 456,
                        2022: 678,
                        2023: 892
                    },
                    patents: {
                        2021: 1234,
                        2022: 1678,
                        2023: 2156
                    },
                    patentTypes: {
                        invention: 1245,
                        utility: 678,
                        design: 233
                    }
                }
            },
            'nio': {
                name: '蔚来汽车科技有限公司',
                logo: '蔚',
                type: '智能电动汽车制造',
                creditCode: '91310115MA1FL2QX8K',
                establishDate: '2014年11月25日',
                capital: '15.6亿元',
                legalPerson: '李斌',
                address: '上海市嘉定区安亭镇新源路1356号',
                status: '存续（在营、开业、在册）',
                businessType: '有限责任公司(中外合资)',
                scope: '智能电动汽车的设计、研发、制造和销售，以及相关的能源服务和数字化服务。',
                financialData: {
                    2023: { revenue: '¥556亿', tax: '¥23.8亿', employees: '2.6万人', rd: '¥45.6亿', revenueGrowth: '+12.9%', taxGrowth: '+15.2%', employeesGrowth: '+8.3%', rdGrowth: '+18.5%' },
                    2022: { revenue: '¥492亿', tax: '¥20.7亿', employees: '2.4万人', rd: '¥38.5亿', revenueGrowth: '+36.3%', taxGrowth: '+42.8%', employeesGrowth: '+20.0%', rdGrowth: '+35.1%' },
                    2021: { revenue: '¥361亿', tax: '¥14.5亿', employees: '2.0万人', rd: '¥28.5亿', revenueGrowth: '+122.1%', taxGrowth: '+89.5%', employeesGrowth: '+66.7%', rdGrowth: '+95.9%' }
                },
                innovation: {
                    copyrights: {
                        2021: 345,
                        2022: 567,
                        2023: 789
                    },
                    patents: {
                        2021: 1023,
                        2022: 1345,
                        2023: 1856
                    },
                    patentTypes: {
                        invention: 1089,
                        utility: 567,
                        design: 200
                    }
                }
            },
            'xpeng': {
                name: '小鹏汽车科技有限公司',
                logo: '小',
                type: '智能电动汽车制造',
                creditCode: '91440101MA59M8QJ7X',
                establishDate: '2014年06月12日',
                capital: '12.8亿元',
                legalPerson: '何小鹏',
                address: '广州市黄埔区科学城科珠路192号',
                status: '存续（在营、开业、在册）',
                businessType: '有限责任公司(中外合资)',
                scope: '智能电动汽车及相关技术的研发、生产、销售，以及智能驾驶技术和服务的开发。',
                financialData: {
                    2023: { revenue: '¥307亿', tax: '¥15.2亿', employees: '1.8万人', rd: '¥35.4亿', revenueGrowth: '+17.3%', taxGrowth: '+22.6%', employeesGrowth: '+12.5%', rdGrowth: '+25.8%' },
                    2022: { revenue: '¥268亿', tax: '¥12.4亿', employees: '1.6万人', rd: '¥28.1亿', revenueGrowth: '+28.5%', taxGrowth: '+35.2%', employeesGrowth: '+23.1%', rdGrowth: '+31.8%' },
                    2021: { revenue: '¥209亿', tax: '¥9.2亿', employees: '1.3万人', rd: '¥21.3亿', revenueGrowth: '+259.1%', taxGrowth: '+189.5%', employeesGrowth: '+85.7%', rdGrowth: '+156.6%' }
                },
                innovation: {
                    copyrights: {
                        2021: 234,
                        2022: 456,
                        2023: 678
                    },
                    patents: {
                        2021: 789,
                        2022: 1023,
                        2023: 1234
                    },
                    patentTypes: {
                        invention: 723,
                        utility: 356,
                        design: 155
                    }
                }
            }
        };

        let currentCompany = null;
        let currentYear = 2023;

        // 显示企业详情抽屉
        function showCompanyDetail(companyId) {
            const company = companyData[companyId];
            if (!company) return;

            currentCompany = company;

            // 更新基本信息
            document.getElementById('companyLogo').textContent = company.logo;
            document.getElementById('companyName').textContent = company.name;
            document.getElementById('companyType').textContent = company.type;
            document.getElementById('companyCreditCode').textContent = company.creditCode;
            document.getElementById('companyEstablishDate').textContent = company.establishDate;
            document.getElementById('companyCapital').textContent = company.capital;
            document.getElementById('companyLegalPerson').textContent = company.legalPerson;
            document.getElementById('companyAddress').textContent = company.address;
            document.getElementById('companyStatus').textContent = company.status;
            document.getElementById('companyBusinessType').textContent = company.businessType;
            document.getElementById('companyScope').textContent = company.scope;

            // 更新创新能力图表数据
            updateInnovationCharts(company);

            // 更新当前年度财务数据
            updateFinancialData(currentYear);

            // 显示抽屉
            const drawer = document.getElementById('companyDrawer');
            const drawerContent = document.getElementById('drawerContent');

            drawer.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // 延迟添加动画类以确保过渡效果
            setTimeout(() => {
                drawerContent.classList.remove('translate-x-full');
            }, 10);
        }

        // 关闭企业详情抽屉
        function closeCompanyDetail() {
            const drawer = document.getElementById('companyDrawer');
            const drawerContent = document.getElementById('drawerContent');

            drawerContent.classList.add('translate-x-full');

            setTimeout(() => {
                drawer.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // 切换年度数据
        function switchYear(year) {
            if (!currentCompany) return;

            currentYear = year;
            updateFinancialData(year);

            // 更新按钮状态
            document.querySelectorAll('[onclick^="switchYear"]').forEach(btn => {
                btn.className = 'px-4 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300';
            });
            event.target.className = 'px-4 py-2 text-sm bg-blue-500 text-white rounded-lg font-medium';
        }

        // 更新财务数据
        function updateFinancialData(year) {
            if (!currentCompany || !currentCompany.financialData[year]) return;

            const data = currentCompany.financialData[year];

            document.getElementById('yearRevenue').textContent = data.revenue;
            document.getElementById('yearTax').textContent = data.tax;
            document.getElementById('yearEmployees').textContent = data.employees;
            document.getElementById('yearRD').textContent = data.rd;
            document.getElementById('revenueGrowth').textContent = data.revenueGrowth + ' 同比';
            document.getElementById('taxGrowth').textContent = data.taxGrowth + ' 同比';
            document.getElementById('employeesGrowth').textContent = data.employeesGrowth + ' 同比';
            document.getElementById('rdGrowth').textContent = data.rdGrowth + ' 同比';

            // 更新财务图表
            updateFinancialCharts(currentCompany);
        }

        // 更新财务图表
        function updateFinancialCharts(company) {
            if (!company || !company.financialData) return;

            // 提取数值（去掉单位）
            const revenueData = {
                2021: parseFloat(company.financialData[2021].revenue.replace(/[¥亿]/g, '').replace(',', '')),
                2022: parseFloat(company.financialData[2022].revenue.replace(/[¥亿]/g, '').replace(',', '')),
                2023: parseFloat(company.financialData[2023].revenue.replace(/[¥亿]/g, '').replace(',', ''))
            };

            const taxData = {
                2021: parseFloat(company.financialData[2021].tax.replace(/[¥亿]/g, '').replace(',', '')),
                2022: parseFloat(company.financialData[2022].tax.replace(/[¥亿]/g, '').replace(',', '')),
                2023: parseFloat(company.financialData[2023].tax.replace(/[¥亿]/g, '').replace(',', ''))
            };

            const employeeData = {
                2021: parseFloat(company.financialData[2021].employees.replace(/[万人]/g, '').replace(',', '')),
                2022: parseFloat(company.financialData[2022].employees.replace(/[万人]/g, '').replace(',', '')),
                2023: parseFloat(company.financialData[2023].employees.replace(/[万人]/g, '').replace(',', ''))
            };

            // 计算最大值用于比例
            const maxRevenue = Math.max(...Object.values(revenueData));
            const maxTax = Math.max(...Object.values(taxData));
            const maxEmployee = Math.max(...Object.values(employeeData));

            // 更新营收图表
            document.getElementById('revenue2021Bar').style.height = (revenueData[2021] / maxRevenue * 100) + '%';
            document.getElementById('revenue2022Bar').style.height = (revenueData[2022] / maxRevenue * 100) + '%';
            document.getElementById('revenue2023Bar').style.height = (revenueData[2023] / maxRevenue * 100) + '%';
            document.getElementById('revenue2021Text').textContent = revenueData[2021].toLocaleString();
            document.getElementById('revenue2022Text').textContent = revenueData[2022].toLocaleString();
            document.getElementById('revenue2023Text').textContent = revenueData[2023].toLocaleString();

            // 更新税收图表
            document.getElementById('tax2021Bar').style.height = (taxData[2021] / maxTax * 100) + '%';
            document.getElementById('tax2022Bar').style.height = (taxData[2022] / maxTax * 100) + '%';
            document.getElementById('tax2023Bar').style.height = (taxData[2023] / maxTax * 100) + '%';
            document.getElementById('tax2021Text').textContent = taxData[2021].toLocaleString();
            document.getElementById('tax2022Text').textContent = taxData[2022].toLocaleString();
            document.getElementById('tax2023Text').textContent = taxData[2023].toLocaleString();

            // 更新员工图表
            document.getElementById('employee2021Bar').style.height = (employeeData[2021] / maxEmployee * 100) + '%';
            document.getElementById('employee2022Bar').style.height = (employeeData[2022] / maxEmployee * 100) + '%';
            document.getElementById('employee2023Bar').style.height = (employeeData[2023] / maxEmployee * 100) + '%';
            document.getElementById('employee2021Text').textContent = employeeData[2021].toLocaleString();
            document.getElementById('employee2022Text').textContent = employeeData[2022].toLocaleString();
            document.getElementById('employee2023Text').textContent = employeeData[2023].toLocaleString();
        }

        // 更新创新能力图表
        function updateInnovationCharts(company) {
            if (!company || !company.innovation) return;

            const copyrights = company.innovation.copyrights;
            const patents = company.innovation.patents;
            const patentTypes = company.innovation.patentTypes;

            // 计算最大值用于比例
            const maxCopyright = Math.max(...Object.values(copyrights));
            const maxPatent = Math.max(...Object.values(patents));

            // 更新软著图表
            document.getElementById('copyright2021Bar').style.height = (copyrights[2021] / maxCopyright * 100) + '%';
            document.getElementById('copyright2022Bar').style.height = (copyrights[2022] / maxCopyright * 100) + '%';
            document.getElementById('copyright2023Bar').style.height = (copyrights[2023] / maxCopyright * 100) + '%';
            document.getElementById('copyright2021Text').textContent = copyrights[2021].toLocaleString();
            document.getElementById('copyright2022Text').textContent = copyrights[2022].toLocaleString();
            document.getElementById('copyright2023Text').textContent = copyrights[2023].toLocaleString();

            // 更新专利图表
            document.getElementById('patent2021Bar').style.height = (patents[2021] / maxPatent * 100) + '%';
            document.getElementById('patent2022Bar').style.height = (patents[2022] / maxPatent * 100) + '%';
            document.getElementById('patent2023Bar').style.height = (patents[2023] / maxPatent * 100) + '%';
            document.getElementById('patent2021Text').textContent = patents[2021].toLocaleString();
            document.getElementById('patent2022Text').textContent = patents[2022].toLocaleString();
            document.getElementById('patent2023Text').textContent = patents[2023].toLocaleString();

            // 更新专利类型分布
            const totalPatents = patents[2023];
            const inventionPercent = Math.round((patentTypes.invention / totalPatents) * 100);
            const utilityPercent = Math.round((patentTypes.utility / totalPatents) * 100);
            const designPercent = Math.round((patentTypes.design / totalPatents) * 100);

            document.getElementById('totalPatents').textContent = totalPatents.toLocaleString();
            document.getElementById('inventionPatents').textContent = `${patentTypes.invention.toLocaleString()}件 (${inventionPercent}%)`;
            document.getElementById('utilityPatents').textContent = `${patentTypes.utility.toLocaleString()}件 (${utilityPercent}%)`;
            document.getElementById('designPatents').textContent = `${patentTypes.design.toLocaleString()}件 (${designPercent}%)`;

            // 更新创新能力总结
            document.getElementById('totalCopyrights').textContent = copyrights[2023].toLocaleString();
            document.getElementById('totalPatentsSum').textContent = totalPatents.toLocaleString();

            // 计算增长率
            const copyrightGrowth = ((copyrights[2023] - copyrights[2022]) / copyrights[2022] * 100).toFixed(1);
            const patentGrowth = ((patents[2023] - patents[2022]) / patents[2022] * 100).toFixed(1);
        }

        // 点击抽屉外部关闭
        document.getElementById('companyDrawer').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCompanyDetail();
            }
        });

        document.getElementById('companyListDrawer').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCompanyList();
            }
        });

        // ESC键关闭抽屉
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const companyDrawer = document.getElementById('companyDrawer');
                const companyListDrawer = document.getElementById('companyListDrawer');

                if (!companyDrawer.classList.contains('hidden')) {
                    closeCompanyDetail();
                } else if (!companyListDrawer.classList.contains('hidden')) {
                    closeCompanyList();
                }
            }
        });

        // 企业列表数据
        const companyListData = [
            { id: 'byd', name: '比亚迪股份有限公司', type: '整车制造', revenue: '¥6,023亿', growth: '+42.1%', rank: 1, logo: '比', color: 'yellow' },
            { id: 'catl', name: '宁德时代新能源科技股份有限公司', type: '动力电池', revenue: '¥3,285亿', growth: '+22.8%', rank: 2, logo: '宁', color: 'gray' },
            { id: 'tesla', name: '特斯拉（上海）有限公司', type: '智能电动', revenue: '¥1,810亿', growth: '+15.8%', rank: 3, logo: 'T', color: 'orange' },
            { id: 'nio', name: '蔚来汽车科技有限公司', type: '智能电动', revenue: '¥556亿', growth: '+12.9%', rank: 4, logo: '蔚', color: 'blue' },
            { id: 'xpeng', name: '小鹏汽车科技有限公司', type: '智能电动', revenue: '¥307亿', growth: '+17.3%', rank: 5, logo: '小', color: 'green' },
            { id: 'li', name: '理想汽车科技有限公司', type: '智能电动', revenue: '¥417亿', growth: '+173.0%', rank: 6, logo: '理', color: 'purple' },
            { id: 'geely', name: '浙江吉利控股集团有限公司', type: '整车制造', revenue: '¥1,234亿', growth: '+8.5%', rank: 7, logo: '吉', color: 'indigo' },
            { id: 'saic', name: '上汽集团股份有限公司', type: '整车制造', revenue: '¥2,156亿', growth: '****%', rank: 8, logo: '上', color: 'pink' },
            { id: 'baic', name: '北京汽车集团有限公司', type: '整车制造', revenue: '¥1,567亿', growth: '+12.8%', rank: 9, logo: '北', color: 'teal' },
            { id: 'gac', name: '广州汽车集团股份有限公司', type: '整车制造', revenue: '¥892亿', growth: '+18.9%', rank: 10, logo: '广', color: 'red' }
        ];

        // 显示企业列表抽屉
        function showCompanyList() {
            const drawer = document.getElementById('companyListDrawer');
            const drawerContent = document.getElementById('listDrawerContent');

            // 生成企业列表
            generateCompanyList();

            drawer.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // 延迟添加动画类以确保过渡效果
            setTimeout(() => {
                drawerContent.classList.remove('translate-x-full');
            }, 10);
        }

        // 关闭企业列表抽屉
        function closeCompanyList() {
            const drawer = document.getElementById('companyListDrawer');
            const drawerContent = document.getElementById('listDrawerContent');

            drawerContent.classList.add('translate-x-full');

            setTimeout(() => {
                drawer.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // 生成企业列表
        function generateCompanyList() {
            const container = document.getElementById('companyListContainer');

            container.innerHTML = companyListData.map(company => {
                const colorClasses = {
                    yellow: 'from-yellow-50 to-orange-50 border-yellow-200',
                    gray: 'from-gray-50 to-blue-50 border-gray-200',
                    orange: 'from-orange-50 to-red-50 border-orange-200',
                    blue: 'from-blue-50 to-indigo-50 border-blue-200',
                    green: 'from-green-50 to-emerald-50 border-green-200',
                    purple: 'from-purple-50 to-pink-50 border-purple-200',
                    indigo: 'from-indigo-50 to-blue-50 border-indigo-200',
                    pink: 'from-pink-50 to-rose-50 border-pink-200',
                    teal: 'from-teal-50 to-cyan-50 border-teal-200',
                    red: 'from-red-50 to-pink-50 border-red-200'
                };

                const rankColors = {
                    1: 'bg-yellow-500',
                    2: 'bg-gray-400',
                    3: 'bg-orange-500'
                };

                const rankColor = rankColors[company.rank] || 'bg-gray-300';

                return `
                    <div class="flex items-center p-4 bg-gradient-to-r ${colorClasses[company.color]} rounded-lg border hover:shadow-md transition-shadow cursor-pointer" onclick="showCompanyDetailFromList('${company.id}')">
                        <div class="flex items-center justify-center w-12 h-12 ${rankColor} text-white rounded-full text-lg font-bold mr-4">
                            ${company.rank}
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 bg-white rounded-lg shadow-sm mr-4">
                            <span class="text-lg font-bold text-gray-700">${company.logo}</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-base hover:text-blue-600 transition-colors">${company.name}</h4>
                            <p class="text-sm text-gray-600">${company.type}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-gray-900">${company.revenue}</p>
                            <p class="text-sm text-green-600">${company.growth}</p>
                        </div>
                        <div class="ml-4">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 从企业列表显示企业详情
        function showCompanyDetailFromList(companyId) {
            // 先关闭企业列表抽屉
            closeCompanyList();

            // 延迟显示企业详情抽屉
            setTimeout(() => {
                showCompanyDetail(companyId);
            }, 350);
        }

        // 提示气泡显示和隐藏函数
        function showTooltip(event, tooltipId) {
            const tooltip = document.getElementById(tooltipId);
            if (tooltip) {
                tooltip.classList.remove('opacity-0');
                tooltip.classList.add('opacity-100');
            }
        }

        function hideTooltip(tooltipId) {
            const tooltip = document.getElementById(tooltipId);
            if (tooltip) {
                tooltip.classList.remove('opacity-100');
                tooltip.classList.add('opacity-0');
            }
        }
    </script>
</body>
</html>
