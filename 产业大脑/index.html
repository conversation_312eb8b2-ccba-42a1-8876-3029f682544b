<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业大脑系统 - 页面总览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        background: '#f8fafc',
                        navbar: '#ffffff'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-navbar shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Logo" class="h-8 w-8 rounded">
                    <span class="ml-2 text-xl font-bold text-gray-900">产业大脑系统</span>
                </div>
                <div class="flex items-center">
                    <a href="首页.html" class="text-sm text-primary hover:text-blue-700 font-medium flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        进入系统首页
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">产业大脑系统</h1>
            <p class="text-xl text-gray-600 mb-6">智能化产业管理与招商服务平台</p>
            <div class="flex justify-center space-x-4">
                <span class="inline-flex px-4 py-2 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">16个核心页面</span>
                <span class="inline-flex px-4 py-2 text-sm font-semibold rounded-full bg-green-100 text-green-800">完整业务流程</span>
                <span class="inline-flex px-4 py-2 text-sm font-semibold rounded-full bg-purple-100 text-purple-800">AI智能助手</span>
            </div>
        </div>

        <!-- 页面分类展示 -->
        <div class="space-y-12">
            <!-- 首页 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-blue-100 mr-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">系统首页</h2>
                </div>
                <div class="grid grid-cols-1 gap-4">
                    <a href="首页.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">首页</h3>
                        <p class="text-sm text-gray-600">系统概览、数据看板、核心指标展示</p>
                    </a>
                </div>
            </div>

            <!-- 企业管理 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-green-100 mr-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">企业管理</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="企业登记.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">企业登记</h3>
                        <p class="text-sm text-gray-600">企业基本信息登记、资质管理、档案建立</p>
                    </a>
                    <a href="企业云图.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">企业云图</h3>
                        <p class="text-sm text-gray-600">企业关系网络可视化、产业链协作分析</p>
                    </a>
                </div>
            </div>

            <!-- 产业管理 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-purple-100 mr-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">产业管理</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="产业链管理.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">产业链管理</h3>
                        <p class="text-sm text-gray-600">产业链环节管理、上下游关系维护</p>
                    </a>
                    <a href="产业资源管理.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">产业资源管理 <span class="text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-1 rounded-full">AI助手</span></h3>
                        <p class="text-sm text-gray-600">政策资源管理、智能政策解读与推荐</p>
                    </a>
                </div>
            </div>

            <!-- 项目管理 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-orange-100 mr-4">
                        <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">项目管理</h2>
                </div>
                <div class="grid grid-cols-1 gap-4">
                    <a href="招商项目管理.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">招商项目管理</h3>
                        <p class="text-sm text-gray-600">招商项目全生命周期管理、进度跟踪</p>
                    </a>
                </div>
            </div>

            <!-- 产业招商 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-red-100 mr-4">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">产业招商</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="招商企业地图.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">招商企业地图</h3>
                        <p class="text-sm text-gray-600">企业地理分布可视化、产业集群分析</p>
                    </a>
                    <a href="招商企业档案.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">招商企业档案</h3>
                        <p class="text-sm text-gray-600">企业详细档案管理、跟踪记录维护</p>
                    </a>
                </div>
            </div>

            <!-- 产业运营 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-indigo-100 mr-4">
                        <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">产业运营</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="产业经济看板.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">产业经济看板</h3>
                        <p class="text-sm text-gray-600">经济指标监控、数据分析看板</p>
                    </a>
                    <a href="产业图谱.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">产业图谱</h3>
                        <p class="text-sm text-gray-600">产业链网络关系图谱、协作分析</p>
                    </a>
                    <a href="产业预警.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">产业预警</h3>
                        <p class="text-sm text-gray-600">风险监控预警、异常情况处理</p>
                    </a>
                    <a href="产业报告.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">产业报告 <span class="text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-1 rounded-full">AI助手</span></h3>
                        <p class="text-sm text-gray-600">AI智能生成产业分析报告</p>
                    </a>
                </div>
            </div>

            <!-- 系统管理 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="flex items-center mb-6">
                    <div class="p-3 rounded-full bg-gray-100 mr-4">
                        <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900">系统管理</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="账号管理.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">账号管理</h3>
                        <p class="text-sm text-gray-600">用户账号管理、权限控制、角色分配</p>
                    </a>
                    <a href="角色管理.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">角色管理</h3>
                        <p class="text-sm text-gray-600">系统角色配置、权限分配管理</p>
                    </a>
                    <a href="日志管理.html" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <h3 class="font-semibold text-gray-900 mb-2">日志管理</h3>
                        <p class="text-sm text-gray-600">系统日志记录、审计追踪、安全监控</p>
                    </a>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="mt-16 text-center">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-8 text-white">
                <h3 class="text-2xl font-bold mb-4">产业大脑系统特色</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <div class="text-3xl mb-2">🤖</div>
                        <h4 class="font-semibold mb-2">AI智能助手</h4>
                        <p class="text-sm opacity-90">智能政策解读、个性化推荐</p>
                    </div>
                    <div>
                        <div class="text-3xl mb-2">📊</div>
                        <h4 class="font-semibold mb-2">数据可视化</h4>
                        <p class="text-sm opacity-90">丰富的图表、地图、网络图谱</p>
                    </div>
                    <div>
                        <div class="text-3xl mb-2">🔄</div>
                        <h4 class="font-semibold mb-2">全流程管理</h4>
                        <p class="text-sm opacity-90">从招商到运营的完整业务链</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
