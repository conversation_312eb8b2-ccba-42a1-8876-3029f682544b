<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业图谱 | 产业大脑系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        background: '#f8fafc',
                        navbar: '#ffffff'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-navbar shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- 左侧：Logo和主导航 -->
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Logo" class="h-8 w-8 rounded">
                        <span class="ml-2 text-xl font-bold text-gray-900">产业大脑</span>
                    </div>
                    
                    <!-- 主导航菜单 -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="首页.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">首页</a>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                企业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="企业登记.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业登记</a>
                                    <a href="企业云图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业云图</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业链管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业链管理</a>
                                    <a href="产业资源管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业资源管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                项目管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商项目管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商项目管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">重点项目管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业招商
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商企业地图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业地图</a>
                                    <a href="招商企业档案.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业档案</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业运营
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业经济看板.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业经济看板</a>
                                    <a href="产业图谱.html" class="block px-4 py-2 text-sm text-primary bg-blue-50 font-medium">产业图谱</a>
                                    <a href="产业预警.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业预警</a>
                                    <a href="产业报告.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业报告</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                系统管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="账号管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">账号管理</a>
                                    <a href="角色管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">角色管理</a>
                                    <a href="日志管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">日志管理</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：搜索和用户信息 -->
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input type="text" placeholder="搜索..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    
                    <!-- 通知 -->
                    <button class="relative p-2 text-gray-600 hover:text-primary">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                    </button>
                    
                    <!-- 用户头像和信息 -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-primary">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm font-medium">管理员</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">个人设置</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 面包屑导航 -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="首页.html" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        首页
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="#" class="ml-1 text-sm font-medium text-gray-700 hover:text-primary md:ml-2">产业运营</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">产业图谱</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- 页面标题和控制面板 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">产业链思维导图</h1>
                <p class="mt-2 text-gray-600">以思维导图形式展示产业链结构，直观呈现产业关系和企业分布</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    导出思维导图
                </button>
                <button class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    智能分析
                </button>
                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    分析报告
                </button>
            </div>
        </div>

        <!-- 思维导图控制面板 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">导图类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>产业链思维导图</option>
                        <option>企业关系导图</option>
                        <option>技术路线导图</option>
                        <option>价值链导图</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">产业选择</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>新能源汽车</option>
                        <option>智能制造</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>数字经济</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">展示层级</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>完整层级</option>
                        <option>核心层级</option>
                        <option>上游重点</option>
                        <option>下游重点</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">节点详细度</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>详细信息</option>
                        <option>简化信息</option>
                        <option>仅显示名称</option>
                        <option>仅显示分类</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">导图风格</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>经典思维导图</option>
                        <option>现代简约</option>
                        <option>彩色主题</option>
                        <option>商务风格</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        生成导图
                    </button>
                </div>
            </div>
        </div>

        <!-- 思维导图可视化和分析面板 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- 思维导图可视化区域 -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">新能源汽车产业链思维导图</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">思维导图</button>
                            <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">聚焦模式</button>
                            <button class="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full">全屏模式</button>
                        </div>
                    </div>

                    <!-- 思维导图容器 -->
                    <div class="relative h-[600px] bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-lg overflow-hidden border border-gray-200">
                        <!-- 思维导图主体 -->
                        <div class="absolute inset-0 p-8">
                            <!-- 中心节点：新能源汽车产业 -->
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                                <div class="w-40 h-24 bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl flex items-center justify-center text-white font-bold text-base shadow-2xl border-4 border-white">
                                    <div class="text-center">
                                        <div>新能源汽车</div>
                                        <div class="text-sm">产业链</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 上方分支：上游供应链 -->
                            <div class="absolute top-8 left-1/2 transform -translate-x-1/2">
                                <!-- 连接线到中心 -->
                                <svg class="absolute top-16 left-1/2 transform -translate-x-1/2 w-4 h-32" viewBox="0 0 16 128">
                                    <path d="M 8 0 L 8 128" stroke="#10b981" stroke-width="3" opacity="0.7"/>
                                </svg>

                                <div class="w-28 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white font-semibold text-sm shadow-lg">
                                    上游供应链
                                </div>

                                <!-- 子分支 -->
                                <div class="absolute top-20 left-1/2 transform -translate-x-1/2 flex space-x-8">
                                    <!-- 动力电池 -->
                                    <div class="text-center">
                                        <div class="w-20 h-10 bg-green-100 rounded-lg flex items-center justify-center text-green-800 text-xs font-medium mb-2">
                                            动力电池
                                        </div>
                                        <div class="flex justify-center space-x-1">
                                            <div class="w-6 h-6 bg-green-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-green-400" title="宁德时代">宁</div>
                                            <div class="w-6 h-6 bg-green-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-green-400" title="比亚迪">比</div>
                                        </div>
                                    </div>

                                    <!-- 电机电控 -->
                                    <div class="text-center">
                                        <div class="w-20 h-10 bg-green-100 rounded-lg flex items-center justify-center text-green-800 text-xs font-medium mb-2">
                                            电机电控
                                        </div>
                                        <div class="flex justify-center space-x-1">
                                            <div class="w-6 h-6 bg-green-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-green-400" title="汇川技术">汇</div>
                                            <div class="w-6 h-6 bg-green-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-green-400" title="英威腾">英</div>
                                        </div>
                                    </div>

                                    <!-- 关键材料 -->
                                    <div class="text-center">
                                        <div class="w-20 h-10 bg-green-100 rounded-lg flex items-center justify-center text-green-800 text-xs font-medium mb-2">
                                            关键材料
                                        </div>
                                        <div class="flex justify-center space-x-1">
                                            <div class="w-6 h-6 bg-green-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-green-400" title="天齐锂业">天</div>
                                            <div class="w-6 h-6 bg-green-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-green-400" title="赣锋锂业">赣</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧分支：核心制造 -->
                            <div class="absolute top-1/2 right-8 transform -translate-y-1/2">
                                <!-- 连接线到中心 -->
                                <svg class="absolute top-1/2 right-28 transform -translate-y-1/2 w-32 h-4" viewBox="0 0 128 16">
                                    <path d="M 0 8 L 128 8" stroke="#3b82f6" stroke-width="3" opacity="0.7"/>
                                </svg>

                                <div class="w-28 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-semibold text-sm shadow-lg">
                                    核心制造
                                </div>

                                <!-- 子分支 -->
                                <div class="absolute top-1/2 right-32 transform -translate-y-1/2 space-y-4">
                                    <!-- 整车制造 -->
                                    <div class="text-right">
                                        <div class="w-20 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-blue-800 text-xs font-medium mb-2 ml-auto">
                                            整车制造
                                        </div>
                                        <div class="flex justify-end space-x-1">
                                            <div class="w-6 h-6 bg-blue-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-blue-400" title="比亚迪">比</div>
                                            <div class="w-6 h-6 bg-blue-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-blue-400" title="特斯拉">特</div>
                                            <div class="w-6 h-6 bg-blue-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-blue-400" title="蔚来">蔚</div>
                                        </div>
                                    </div>

                                    <!-- 系统集成 -->
                                    <div class="text-right">
                                        <div class="w-20 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-blue-800 text-xs font-medium mb-2 ml-auto">
                                            系统集成
                                        </div>
                                        <div class="flex justify-end space-x-1">
                                            <div class="w-6 h-6 bg-blue-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-blue-400" title="华为">华</div>
                                            <div class="w-6 h-6 bg-blue-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-blue-400" title="博世">博</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </div>

                            <!-- 下方分支：下游应用 -->
                            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
                                <!-- 连接线到中心 -->
                                <svg class="absolute bottom-16 left-1/2 transform -translate-x-1/2 w-4 h-32" viewBox="0 0 16 128">
                                    <path d="M 8 0 L 8 128" stroke="#8b5cf6" stroke-width="3" opacity="0.7"/>
                                </svg>

                                <div class="w-28 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-semibold text-sm shadow-lg">
                                    下游应用
                                </div>

                                <!-- 子分支 -->
                                <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex space-x-8">
                                    <!-- 充电服务 -->
                                    <div class="text-center">
                                        <div class="flex justify-center space-x-1 mb-2">
                                            <div class="w-6 h-6 bg-purple-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-purple-400" title="特来电">特</div>
                                            <div class="w-6 h-6 bg-purple-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-purple-400" title="星星充电">星</div>
                                        </div>
                                        <div class="w-20 h-10 bg-purple-100 rounded-lg flex items-center justify-center text-purple-800 text-xs font-medium">
                                            充电服务
                                        </div>
                                    </div>

                                    <!-- 运营服务 -->
                                    <div class="text-center">
                                        <div class="flex justify-center space-x-1 mb-2">
                                            <div class="w-6 h-6 bg-purple-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-purple-400" title="滴滴">滴</div>
                                            <div class="w-6 h-6 bg-purple-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-purple-400" title="曹操出行">曹</div>
                                        </div>
                                        <div class="w-20 h-10 bg-purple-100 rounded-lg flex items-center justify-center text-purple-800 text-xs font-medium">
                                            运营服务
                                        </div>
                                    </div>

                                    <!-- 销售渠道 -->
                                    <div class="text-center">
                                        <div class="flex justify-center space-x-1 mb-2">
                                            <div class="w-6 h-6 bg-purple-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-purple-400" title="汽车之家">汽</div>
                                            <div class="w-6 h-6 bg-purple-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-purple-400" title="易车">易</div>
                                        </div>
                                        <div class="w-20 h-10 bg-purple-100 rounded-lg flex items-center justify-center text-purple-800 text-xs font-medium">
                                            销售渠道
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </div>

                            <!-- 左侧分支：配套服务 -->
                            <div class="absolute top-1/2 left-8 transform -translate-y-1/2">
                                <!-- 连接线到中心 -->
                                <svg class="absolute top-1/2 left-28 transform -translate-y-1/2 w-32 h-4" viewBox="0 0 128 16">
                                    <path d="M 0 8 L 128 8" stroke="#f59e0b" stroke-width="3" opacity="0.7"/>
                                </svg>

                                <div class="w-28 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-xl flex items-center justify-center text-white font-semibold text-sm shadow-lg">
                                    配套服务
                                </div>

                                <!-- 子分支 -->
                                <div class="absolute top-1/2 left-32 transform -translate-y-1/2 space-y-4">
                                    <!-- 金融服务 -->
                                    <div class="text-left">
                                        <div class="w-20 h-10 bg-amber-100 rounded-lg flex items-center justify-center text-amber-800 text-xs font-medium mb-2">
                                            金融服务
                                        </div>
                                        <div class="flex justify-start space-x-1">
                                            <div class="w-6 h-6 bg-amber-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-amber-400" title="招商银行">招</div>
                                            <div class="w-6 h-6 bg-amber-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-amber-400" title="平安租赁">平</div>
                                        </div>
                                    </div>

                                    <!-- 检测认证 -->
                                    <div class="text-left">
                                        <div class="w-20 h-10 bg-amber-100 rounded-lg flex items-center justify-center text-amber-800 text-xs font-medium mb-2">
                                            检测认证
                                        </div>
                                        <div class="flex justify-start space-x-1">
                                            <div class="w-6 h-6 bg-amber-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-amber-400" title="中汽研">中</div>
                                            <div class="w-6 h-6 bg-amber-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-amber-400" title="华测检测">华</div>
                                        </div>
                                    </div>

                                    <!-- 研发设计 -->
                                    <div class="text-left">
                                        <div class="w-20 h-10 bg-amber-100 rounded-lg flex items-center justify-center text-amber-800 text-xs font-medium mb-2">
                                            研发设计
                                        </div>
                                        <div class="flex justify-start space-x-1">
                                            <div class="w-6 h-6 bg-amber-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-amber-400" title="中科院">中</div>
                                            <div class="w-6 h-6 bg-amber-300 rounded-full text-white text-xs flex items-center justify-center cursor-pointer hover:bg-amber-400" title="清华大学">清</div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        <!-- 思维导图工具栏 -->
                        <div class="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg p-2">
                            <div class="flex space-x-2">
                                <button class="p-2 hover:bg-gray-100 rounded" title="放大">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </button>
                                <button class="p-2 hover:bg-gray-100 rounded" title="缩小">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                    </svg>
                                </button>
                                <button class="p-2 hover:bg-gray-100 rounded" title="居中">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </button>
                                <button class="p-2 hover:bg-gray-100 rounded" title="展开全部">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                                    </svg>
                                </button>
                                <button class="p-2 hover:bg-gray-100 rounded" title="折叠全部">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 9l6 6m0 0l6-6m-6 6V3"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- 思维导图图例 -->
                        <div class="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 mr-32">
                            <h4 class="text-sm font-semibold text-gray-900 mb-2">思维导图图例</h4>
                            <div class="space-y-1 text-xs">
                                <div class="flex items-center">
                                    <div class="w-4 h-3 bg-blue-600 rounded mr-2"></div>
                                    <span>中心节点：产业核心</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-3 bg-green-500 rounded mr-2"></div>
                                    <span>上游分支：供应链</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-3 bg-blue-500 rounded mr-2"></div>
                                    <span>右上分支：核心制造</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-3 bg-purple-500 rounded mr-2"></div>
                                    <span>左下分支：下游应用</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-3 bg-amber-500 rounded mr-2"></div>
                                    <span>右下分支：配套服务</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                                    <span>企业节点</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-2 mr-2" viewBox="0 0 16 8">
                                        <path d="M 0 4 Q 8 0 16 4" stroke="#6b7280" stroke-width="1" fill="none"/>
                                    </svg>
                                    <span>分支连接</span>
                                </div>
                            </div>
                        </div>

                        <!-- 思维导图说明 -->
                        <div class="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 max-w-xs">
                            <h4 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                                <svg class="w-4 h-4 mr-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                思维导图说明
                            </h4>
                            <div class="text-xs text-gray-600 space-y-1">
                                <p>• 中心节点为产业核心主题</p>
                                <p>• 四个主分支展示产业结构</p>
                                <p>• 点击企业节点查看详细信息</p>
                                <p>• 分支可展开查看更多细节</p>
                                <p>• 支持拖拽和缩放操作</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧分析面板 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 图谱统计 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">图谱统计</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">节点总数</span>
                            <span class="text-lg font-bold text-gray-900">2,456</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">连接数</span>
                            <span class="text-lg font-bold text-blue-600">8,934</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">产业链环节</span>
                            <span class="text-lg font-bold text-green-600">28</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">核心企业</span>
                            <span class="text-lg font-bold text-purple-600">156</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">网络密度</span>
                            <span class="text-lg font-bold text-orange-600">0.73</span>
                        </div>
                    </div>
                </div>

                <!-- 关键节点排行 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">关键节点排行</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center mr-3">1</span>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">比亚迪股份</p>
                                    <p class="text-xs text-gray-500">整车制造</p>
                                </div>
                            </div>
                            <span class="text-sm font-bold text-blue-600">0.89</span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-green-600 text-white text-xs rounded-full flex items-center justify-center mr-3">2</span>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">宁德时代</p>
                                    <p class="text-xs text-gray-500">动力电池</p>
                                </div>
                            </div>
                            <span class="text-sm font-bold text-green-600">0.85</span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-purple-600 text-white text-xs rounded-full flex items-center justify-center mr-3">3</span>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">特斯拉</p>
                                    <p class="text-xs text-gray-500">整车制造</p>
                                </div>
                            </div>
                            <span class="text-sm font-bold text-purple-600">0.82</span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-orange-600 text-white text-xs rounded-full flex items-center justify-center mr-3">4</span>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">汇川技术</p>
                                    <p class="text-xs text-gray-500">电机电控</p>
                                </div>
                            </div>
                            <span class="text-sm font-bold text-orange-600">0.78</span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center mr-3">5</span>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">特来电</p>
                                    <p class="text-xs text-gray-500">充电设施</p>
                                </div>
                            </div>
                            <span class="text-sm font-bold text-red-600">0.75</span>
                        </div>
                    </div>
                </div>

                <!-- 产业链环节分析 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">产业链环节</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">上游环节</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">12个</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 42.8%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">核心环节</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">4个</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 14.3%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">下游环节</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">8个</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 28.6%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">配套服务</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">4个</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-orange-500 h-2 rounded-full" style="width: 14.3%"></div>
                        </div>
                    </div>
                </div>

                <!-- 网络分析指标 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">网络分析</h3>
                    <div class="space-y-4">
                        <div class="p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-blue-800">集群系数</span>
                                <span class="text-lg font-bold text-blue-600">0.73</span>
                            </div>
                            <p class="text-xs text-blue-700">网络聚集程度较高</p>
                        </div>

                        <div class="p-3 bg-green-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-green-800">平均路径长度</span>
                                <span class="text-lg font-bold text-green-600">2.8</span>
                            </div>
                            <p class="text-xs text-green-700">节点间连接效率良好</p>
                        </div>

                        <div class="p-3 bg-purple-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-purple-800">网络直径</span>
                                <span class="text-lg font-bold text-purple-600">6</span>
                            </div>
                            <p class="text-xs text-purple-700">网络规模适中</p>
                        </div>

                        <div class="p-3 bg-orange-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-orange-800">模块化程度</span>
                                <span class="text-lg font-bold text-orange-600">0.65</span>
                            </div>
                            <p class="text-xs text-orange-700">产业分工明确</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
