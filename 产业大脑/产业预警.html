<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业预警 | 产业大脑系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        accent: '#60a5fa',
                        background: '#f8fafc',
                        navbar: '#ffffff'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-navbar shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- 左侧：Logo和主导航 -->
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Logo" class="h-8 w-8 rounded">
                        <span class="ml-2 text-xl font-bold text-gray-900">产业大脑</span>
                    </div>
                    
                    <!-- 主导航菜单 -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="首页.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">首页</a>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                企业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="企业登记.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业登记</a>
                                    <a href="企业云图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">企业云图</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业链管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业链管理</a>
                                    <a href="产业资源管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业资源管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                项目管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商项目管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商项目管理</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">重点项目管理</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业招商
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="招商企业地图.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业地图</a>
                                    <a href="招商企业档案.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">招商企业档案</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="bg-primary text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                产业运营
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="产业经济看板.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业经济看板</a>
                                    <a href="产业图谱.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业图谱</a>
                                    <a href="产业预警.html" class="block px-4 py-2 text-sm text-primary bg-blue-50 font-medium">产业预警</a>
                                    <a href="产业报告.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">产业报告</a>
                                </div>
                            </div>
                        </div>
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                系统管理
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-1">
                                    <a href="账号管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">账号管理</a>
                                    <a href="角色管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">角色管理</a>
                                    <a href="日志管理.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">日志管理</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：搜索和用户信息 -->
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input type="text" placeholder="搜索..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    
                    <!-- 通知 -->
                    <button class="relative p-2 text-gray-600 hover:text-primary">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                    </button>
                    
                    <!-- 用户头像和信息 -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-primary">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="text-sm font-medium">管理员</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">个人设置</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 面包屑导航 -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="首页.html" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        首页
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="#" class="ml-1 text-sm font-medium text-gray-700 hover:text-primary md:ml-2">产业运营</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">产业预警</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- 页面标题和操作按钮 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">产业预警</h1>
                <p class="mt-2 text-gray-600">监控企业经营异常、财务风险和合规问题，提供预警分析</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    导出预警
                </button>
                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    预警分析
                </button>
            </div>
        </div>

        <!-- 预警统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- 高风险预警 -->
            <div class="bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-sm p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-red-100 text-sm">高风险预警</p>
                        <p class="text-3xl font-bold">23</p>
                        <div class="flex items-center mt-2">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l5-5 5 5M7 7l5 5 5-5"></path>
                            </svg>
                            <span class="text-sm">+5 本周新增</span>
                        </div>
                    </div>
                    <div class="p-3 bg-red-400 bg-opacity-30 rounded-full">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 中风险预警 -->
            <div class="bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl shadow-sm p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-yellow-100 text-sm">中风险预警</p>
                        <p class="text-3xl font-bold">67</p>
                        <div class="flex items-center mt-2">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l5-5 5 5M7 7l5 5 5-5"></path>
                            </svg>
                            <span class="text-sm">+12 本周新增</span>
                        </div>
                    </div>
                    <div class="p-3 bg-yellow-400 bg-opacity-30 rounded-full">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 低风险预警 -->
            <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-sm p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm">低风险预警</p>
                        <p class="text-3xl font-bold">156</p>
                        <div class="flex items-center mt-2">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l5-5 5 5M7 7l5 5 5-5"></path>
                            </svg>
                            <span class="text-sm">+8 本周新增</span>
                        </div>
                    </div>
                    <div class="p-3 bg-blue-400 bg-opacity-30 rounded-full">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 已处理预警 -->
            <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-sm p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm">已处理预警</p>
                        <p class="text-3xl font-bold">342</p>
                        <div class="flex items-center mt-2">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            <span class="text-sm">处理率 93.2%</span>
                        </div>
                    </div>
                    <div class="p-3 bg-green-400 bg-opacity-30 rounded-full">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预警筛选和搜索 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">企业名称</label>
                    <div class="relative">
                        <input type="text" placeholder="请输入企业名称" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">预警类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全部类型</option>
                        <option>经营异常</option>
                        <option>行政处罚</option>
                        <option>企业欠税</option>
                        <option>被执行人</option>
                        <option>失信人</option>
                        <option>财务异常</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">风险等级</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全部等级</option>
                        <option>高风险</option>
                        <option>中风险</option>
                        <option>低风险</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">处理状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>全部状态</option>
                        <option>待处理</option>
                        <option>处理中</option>
                        <option>已处理</option>
                        <option>已忽略</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 预警列表 -->
        <div class="space-y-6">
            <!-- 高风险预警项目 -->
            <div class="bg-white rounded-xl shadow-sm border-l-4 border-red-500 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-3">
                            <h3 class="text-lg font-semibold text-gray-900">智造科技集团有限公司</h3>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">高风险</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-orange-100 text-orange-800">被执行人</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">待处理</span>
                        </div>
                        <p class="text-gray-600 mb-4">企业因合同纠纷被列为被执行人，执行标的额为2,350万元，存在较大财务风险。同时发现企业近3个月营收下降45%，税收缴纳异常。</p>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div>
                                <p class="text-xs text-gray-500">预警时间</p>
                                <p class="text-sm font-semibold text-gray-900">2024-01-20 09:30</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">执行标的</p>
                                <p class="text-sm font-semibold text-red-600">¥2,350万</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">营收下降</p>
                                <p class="text-sm font-semibold text-red-600">-45%</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">所属产业</p>
                                <p class="text-sm font-semibold text-gray-900">智能制造</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-6 text-sm text-gray-600">
                            <span>📍 高新技术开发区</span>
                            <span>📅 成立时间：2018-03-15</span>
                            <span>💰 注册资本：5,000万元</span>
                            <span>👤 法人：张某某</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 ml-6">
                        <button class="px-4 py-2 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">查看详情</button>
                        <button class="px-4 py-2 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">处理预警</button>
                        <button class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">忽略</button>
                    </div>
                </div>
            </div>

            <!-- 中风险预警项目 -->
            <div class="bg-white rounded-xl shadow-sm border-l-4 border-yellow-500 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-3">
                            <h3 class="text-lg font-semibold text-gray-900">绿能动力科技股份有限公司</h3>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">中风险</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800">行政处罚</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">处理中</span>
                        </div>
                        <p class="text-gray-600 mb-4">企业因环保违规被行政处罚，罚款金额150万元。同时发现企业税收缴纳存在延迟情况，需要关注合规风险。</p>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div>
                                <p class="text-xs text-gray-500">预警时间</p>
                                <p class="text-sm font-semibold text-gray-900">2024-01-19 14:20</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">处罚金额</p>
                                <p class="text-sm font-semibold text-yellow-600">¥150万</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">税收状态</p>
                                <p class="text-sm font-semibold text-yellow-600">延迟缴纳</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">所属产业</p>
                                <p class="text-sm font-semibold text-gray-900">新能源汽车</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-6 text-sm text-gray-600">
                            <span>📍 经济技术开发区</span>
                            <span>📅 成立时间：2015-08-22</span>
                            <span>💰 注册资本：8,000万元</span>
                            <span>👤 法人：李某某</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 ml-6">
                        <button class="px-4 py-2 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">查看详情</button>
                        <button class="px-4 py-2 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">继续处理</button>
                        <button class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">暂停</button>
                    </div>
                </div>
            </div>

            <!-- 低风险预警项目 -->
            <div class="bg-white rounded-xl shadow-sm border-l-4 border-blue-500 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-3">
                            <h3 class="text-lg font-semibold text-gray-900">华康生物科技有限公司</h3>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">低风险</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-indigo-100 text-indigo-800">财务异常</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">待处理</span>
                        </div>
                        <p class="text-gray-600 mb-4">企业近期财务指标出现轻微波动，营收环比下降15%，但整体经营状况稳定，建议持续关注。</p>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div>
                                <p class="text-xs text-gray-500">预警时间</p>
                                <p class="text-sm font-semibold text-gray-900">2024-01-18 16:45</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">营收变化</p>
                                <p class="text-sm font-semibold text-blue-600">-15%</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">经营状态</p>
                                <p class="text-sm font-semibold text-green-600">稳定</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">所属产业</p>
                                <p class="text-sm font-semibold text-gray-900">生物医药</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-6 text-sm text-gray-600">
                            <span>📍 生物医药产业园</span>
                            <span>📅 成立时间：2019-11-08</span>
                            <span>💰 注册资本：3,000万元</span>
                            <span>👤 法人：王某某</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 ml-6">
                        <button class="px-4 py-2 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">查看详情</button>
                        <button class="px-4 py-2 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">处理预警</button>
                        <button class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">忽略</button>
                    </div>
                </div>
            </div>

            <!-- 已处理预警项目 -->
            <div class="bg-white rounded-xl shadow-sm border-l-4 border-green-500 p-6 hover:shadow-md transition-shadow opacity-75">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-3">
                            <h3 class="text-lg font-semibold text-gray-900">中科集成电路技术有限公司</h3>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800">中风险</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">企业欠税</span>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">已处理</span>
                        </div>
                        <p class="text-gray-600 mb-4">企业欠税问题已解决，补缴税款280万元，相关风险已消除。处理人：张管理员，处理时间：2024-01-17。</p>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div>
                                <p class="text-xs text-gray-500">预警时间</p>
                                <p class="text-sm font-semibold text-gray-900">2024-01-15 10:20</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">补缴金额</p>
                                <p class="text-sm font-semibold text-green-600">¥280万</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">处理时间</p>
                                <p class="text-sm font-semibold text-green-600">2天</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">所属产业</p>
                                <p class="text-sm font-semibold text-gray-900">集成电路</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-6 text-sm text-gray-600">
                            <span>📍 高新技术开发区</span>
                            <span>📅 成立时间：2017-05-12</span>
                            <span>💰 注册资本：6,000万元</span>
                            <span>👤 法人：赵某某</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 ml-6">
                        <button class="px-4 py-2 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">查看详情</button>
                        <button class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">处理记录</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-8">
            <div class="text-sm text-gray-700">
                显示第 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">246</span> 项预警
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50 disabled:opacity-50" disabled>
                    上一页
                </button>
                <button class="px-3 py-1 text-sm bg-primary text-white rounded-md">1</button>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">2</button>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">3</button>
                <span class="px-3 py-1 text-sm text-gray-500">...</span>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">62</button>
                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    下一页
                </button>
            </div>
        </div>
    </main>
</body>
</html>
